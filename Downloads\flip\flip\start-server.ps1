# PowerShell script to start a simple web server
Write-Host "Starting local web server..." -ForegroundColor Green
Write-Host ""

# Try to start a simple HTTP server using PowerShell
$port = 8000
$path = Get-Location

try {
    # Try Python first
    Write-Host "Trying Python HTTP server..." -ForegroundColor Yellow
    $pythonProcess = Start-Process -FilePath "python" -ArgumentList "-m", "http.server", $port -NoNewWindow -PassThru -ErrorAction SilentlyContinue
    
    if ($pythonProcess) {
        Write-Host "SUCCESS! Python server started on http://localhost:$port" -ForegroundColor Green
        Write-Host "Open your browser and go to: http://localhost:$port" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Press any key to stop the server..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        Stop-Process -Id $pythonProcess.Id -Force
        exit
    }
}
catch {
    Write-Host "Python not found or failed to start" -ForegroundColor Red
}

try {
    # Try PHP
    Write-Host "Trying PHP built-in server..." -ForegroundColor Yellow
    $phpProcess = Start-Process -FilePath "php" -ArgumentList "-S", "localhost:$port" -NoNewWindow -PassThru -ErrorAction SilentlyContinue
    
    if ($phpProcess) {
        Write-Host "SUCCESS! PHP server started on http://localhost:$port" -ForegroundColor Green
        Write-Host "Open your browser and go to: http://localhost:$port" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Press any key to stop the server..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        Stop-Process -Id $phpProcess.Id -Force
        exit
    }
}
catch {
    Write-Host "PHP not found or failed to start" -ForegroundColor Red
}

Write-Host ""
Write-Host "ERROR: No suitable web server found!" -ForegroundColor Red
Write-Host ""
Write-Host "Please install one of the following:" -ForegroundColor Yellow
Write-Host "1. PHP (recommended for PHP files)" -ForegroundColor White
Write-Host "2. Python 3" -ForegroundColor White
Write-Host "3. Node.js" -ForegroundColor White
Write-Host ""
Write-Host "Installation links:" -ForegroundColor Yellow
Write-Host "PHP: https://www.php.net/downloads" -ForegroundColor Cyan
Write-Host "Python: https://www.python.org/downloads/" -ForegroundColor Cyan
Write-Host "Node.js: https://nodejs.org/" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
