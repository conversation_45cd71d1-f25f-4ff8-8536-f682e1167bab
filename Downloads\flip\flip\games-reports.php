<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>Dashboard </title><meta name="viewport"
		content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="assets/cy/css/media.css?vs=100">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.1/css/all.min.css" integrity="sha512-9my9Mb2+0YO+I4PUCSwUYO7sEK21Y0STBAiFEYoWtd2VzLEZZ4QARDrZ30hdM1GlioHJ8o8cWQiy8IAb1hy/Hg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  max-width: 160px;
}
.topline-refill-btn {
  line-height: 1;
  color: #EBF4FF;
  font-size: 15px;
  text-align: center;
  height: 40px;
}
.topline-refill-btn {
  width: 170px;
}
.toplinen img {
  width: 17px;
}
@media only screen and (min-width: 601px) {
  .db-page-topline__right {
  margin-left: -200px;
}
}

.dropdown-container {
  margin-top: 5px;
  border-radius: 5px;
  padding-bottom: 5px;
}
.dropdown-container {
  display: none;
  /*background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);**/
  border-radius:5px;}
/* Optional: Style the caret down icon */


/* Some media queries for responsiveness */
@media screen and (max-width: 780px) {
 .mobhidden{display:none !important;}
  .cabMenu li.hidden {
    display: flex !important;
  }
  .langList{display:none}
}

.dropdown-btn .fa {
  position: absolute;
  right: 5px;
  top: 65px;
  font-size: 20px;
  color: #fff;
}
.fa-caret-up{display:none;}
.dactive .fa-caret-down{display:none;}
.dactive .fa-caret-up{display:inline-block !important;}
.dropdown-container li {
  height: auto;
  padding: 5px 10px 5px 10px;
  line-height: 1;
  
}
.dropdown-containers li:hover{
 background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);;
 color:#fff;
}
.dropdown-container li:hover cap{
 color:#fff;
}
.hidden:hover {
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.capa a {
	margin-left:20px;  color: #fff;
  text-decoration: none;
  font-size:14px;
}
.db-sidemenu-icon img {
  width: 25px;
}
.db-sidemenu-icon {
  position: inherit;
}
.mobile-db-menu-link__text {
  margin-left: 5px;
}
</style>
<link rel="shortcut icon" href="assets/fav.png" />
</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>
<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
								aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn"></button>
      </div>
    </div>
  </div>
  <div class="mobile-db-menu-block">
    <div class="mobile-db-menu">
      <ul>
  <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a	href='account.php'													class="mobile-db-menu-link mobile-db-menu-link--dashboard active">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Dashboard</div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a	href="javascript:void(0)"													class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="change-password.php">Change Password</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="balance.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="transactions.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="activation.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="activation_report.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="left-network.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="right-network.php">Right Direct</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="total-team.php">Total Team</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="tree.php">Tree View</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="non-working-bonus.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="matching-bonus.php">Matching Bonus</a></span> </li>

    <li class="hidden" ><span class="capa"><a href="reward-bonus.php">Reward Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="lottery-bonus.php">Lottery Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="game-bonus.php">Game Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="bonus-report.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"><a href="activation-wallet.php">Activation Wallet</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="cashback-wallet.php">Cashback Wallet</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="earning-wallet.php">Earning Wallet</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="gaming-wallet.php">Gaming Wallet</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="wallet-report.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="withdrawal.php" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="withdrawal-report.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/lotery.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Lotteries <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="buy-lottery.php" class="openMod" data-modal="buy">Buy Lottery</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="lottery-reports.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/gaming.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Gaming <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="play-games.php" class="openMod" data-modal="buy">Play Games</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="games-reports.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="promo.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/promo.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Promo Stuff </div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/support.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Support Ticket </div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="topline-logout-btn__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Logout</div>
    </div>
    </a> </li>
</ul>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-menu">
        
      </div>
    </div>
  </div>
</div>
<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a	href='account.php'													class="db-sidemenu-link db-sidemenu-link--dashboard active">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>
             
            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a	href="javascript:void(0)"													class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="change-password.php">Change Password</a></span> </li>
      </div>
      
          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="balance.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="transactions.php">Report</a></span> </li>
      </div>
            
          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="activation.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="activation_report.php">Report</a></span> </li>
      </div>
      
      
          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="left-network.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="right-network.php">Right Direct</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="total-team.php">Total Team</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="tree.php">Tree View</a></span> </li>
      </div>
            
          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="non-working-bonus.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="matching-bonus.php">Matching Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="reward-bonus.php">Reward Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="lottery-bonus.php">Lottery Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="game-bonus.php">Game Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="bonus-report.php">Report</a></span> </li>
             
      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"><a href="activation-wallet.php">Activation Wallet</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="cashback-wallet.php">Cashback Wallet</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="earning-wallet.php">Earning Wallet</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="gaming-wallet.php">Gaming Wallet</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="wallet-report.php">Report</a></span> </li>
         </div>
            
            
             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="withdrawal.php" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="withdrawal-report.php">Report</a></span> </li>
      </div>
            
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/lotery.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Lotteries <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="buy-lottery.php" class="openMod" data-modal="buy">Buy Lottery</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="lottery-reports.php">Report</a></span> </li>
      </div>
      
         <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/gaming.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Gaming <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="play-games.php" class="openMod" data-modal="buy">Play Games</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="games-reports.php">Report</a></span> </li>
      </div>
      
      
            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="promo.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/promo.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Promo Stuff </div>
            </div>
            </a> </li>
            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
           <div class="db-sidemenu-icon"><img src="assets/support.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Support Ticket </div>
            </div>
            </a> </li>
            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>
          
            
            
           


            
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">
        
        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">
            
            <div class="topline-refill-btn-block"> <a href="balance.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : Expert</div>
              </a> </div>
              
            <div class="topline-refill-btn-block"> <a href="balance.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">
              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img src="assets/logo.png"
																			class="image" alt=""> </div>
                </a> </div>
            </div>
            <div class="db-page-topline-panel__right__content">
              <div class="topline-lang-panel-block">
                <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
								aria-expanded="false">
                  <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="current-lang__text">EN</div>
                  </a>
                  <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                    <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                    <div class="lang-link__text">EN</div>
                    </a> </div>
                </div>
              </div>
              <div class="topline-logout-btn-block"> <a href="logout/" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
              <div class="mobile-panel-btn-block">
                <button type="button" class="mobile-panel-btn"></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
/* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content - This allows the user to have multiple dropdowns without any conflict */
var dropdown = document.getElementsByClassName("dropdown-btn");
var i;

for (i = 0; i < dropdown.length; i++) {
  dropdown[
i].addEventListener("click", function() {
    this.classList.toggle("active");
	this.classList.toggle("dactive");
    var dropdownContent = this.nextElementSibling;
    if (dropdownContent.style.display === "block") {
      dropdownContent.style.display = "none";
	  
	  
    } else {
      dropdownContent.style.display = "block";
    }
  });
}
</script><div class="db-page-content-block">
  <div class="db-page-content">
    <div class="transactions-block">
      <div class="transactions">
        <div class="transactions-top-block">
          <div class="transactions-top">
            <h1> Transaction Details  </h1>
            <div class="transactions-top-descr">You Can Check The History of Games You Played Here.
</div>
          </div>
        </div>
        <div class="transactions-content-block">
          <div class="transactions-content">
            <div class="transactions-empty-empty">
              <div class="transactions-empty-empty__image"> <img class="image" src="/assets/cy/images/svg/smiley-melting.svg" alt=""> </div>
              <div class="transactions-empty-empty__descr"> No transactions found on your account </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


</div>
<script>
		function depositDetails(button) {
			modal = $('#modalDeposit');
			if (button.data('status') == 'completed') {
				modal.find('.modalDepositActive').addClass('d-none');
				modal.find('.modalDepositCompleted').removeClass('d-none');
			} else {
				modal.find('.modalDepositActive').removeClass('d-none');
				modal.find('.modalDepositCompleted').addClass('d-none');
			}
			modal.find('.modalDepositPercent').html(button.data('plan'));
			modal.find('.modalDepositProgress').attr('style','width:'+button.data('progress')+'%');
			currencyInfo = globalCurrencies[button.data('currency')];
			modal.find('.modalDepositCurrencyIcon').html(currencyInfo.title);
			modal.find('.modalDepositCurrencyIcon').attr('src','assets/cy/images/svg/payment/'+currencyInfo.config.data.icon+'.svg');
			modal.find('.modalDepositDailyReturn').html(button.data('percent-amount'));
			modal.find('.modalDepositAmount').html(button.data('amount'));
			modal.find('.modalDepositTotalReturn').html(button.data('total-return'));
			modal.find('.modalDepositDuration').html(button.data('duration'));
			modal.find('.modalDepositStart').html(button.data('start'));
			modal.find('.modalDepositEnd').html(button.data('end'));
			
			modal.modal('show');
		}
	</script>
<div class="modals">
  <div class="modal custom-modal fade" id="modalConfirm" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title h2" >Security Verification</div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="add-wallet-form-block">
              <form action="/check_confirm/" class="form add-wallet-form">
                <div class="field-block">
                  <div class="field-title-block">
                    <div class="field-title"> Google 2FA Code </div>
                  </div>
                  <div
													class="field field--input field--have-icon field--pin">
                    <div class="field-icon"></div>
                    <input type="text" name="secret" maxlength="7" class="form-control" value="">
                  </div>
                </div>
                <div class="form-button-block">
                  <button type="submit" class="green-gr-btn send-btn">
                  <div class="send-btn__text">Confirm</div>
                  <div class="send-btn__icon"></div>
                  </button>
                </div>
                <input type="hidden" name="code" value="">
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal custom-modal fade modal--login" id="modalLogin" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title h2">Sign In</div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="login-form-block">
              <form action="/auth/" class="form login-form">
                <div class="field-block">
                  <div class="field-title-block">
                    <div class="field-title"> Email or Username <span class="field-required-star">*</span> </div>
                  </div>
                  <div class="field field--input">
                    <input type="text" name="login" maxlength="255" autocomplete="off">
                  </div>
                </div>
                <div class="field-block">
                  <div class="field-title-block">
                    <div class="field-title"> Password <span class="field-required-star">*</span> </div>
                  </div>
                  <div
											class="field field--input field--have-icon field--password">
                    <div class="field-icon"></div>
                    <input type="password" name="password" maxlength="255" class="form-control">
                    <div class="field-right-panel-block">
                      <div class="field-right-panel">
                        <div class="change-pswd-type-link-block">
                          <button type="button" class="change-pswd-type-link"></button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="field-bottom-link-block">
                    <button type="button" class="reset-password-link" type="button" data-bs-toggle="modal"
												data-bs-target="#modalRecovery">
                    Forgot password
                    </button>
                  </div>
                </div>
                <div class="field-block">
                  <div class="h-captcha" data-sitekey="c226d114-21dc-49d6-aa0a-2f1c65f60186" data-theme="dark"></div>
                </div>
                <div class="form-button-block">
                  <button type="submit" class="green-gr-btn send-btn">
                  <div class="send-btn__text">Sign In</div>
                  <div class="send-btn__icon"></div>
                  </button>
                </div>
                <div class="form-bottom-note-block">
                  <div class="form-bottom-note"> Don’t have an account? <a href="/signup/">Register an account</a> </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal custom-modal fade modal--promotion" id="modalPromo1" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title h2">Get 3% Cashback</div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="m-promotion-image-block">
              <div class="m-promotion-image"> <img class="image" src="assets/cy/news/promo-1-3.png" alt=""> </div>
            </div>
            <div class="m-promotion-panel-block">
              <div class="m-promotion-panel">
                <div class="m-promotion-panel-left">
                  <div class="m-promotion-tags-block">
                    <div class="m-promotion-tags">
                      <div class="m-promotion-tag-wrapper">
                        <div class="m-promotion-tag"> Staking </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="m-promotion-panel-right">
                  <div class="m-promotion-like-block">
                    <button class="m-promotion-like active liked" type="button" onclick="if (!$(this).hasClass('liked')) { $(this).find('.promotion-item__like__count').html(parseInt($(this).find('.promotion-item__like__count').html()) + 1);$(this).addClass('liked') }">
                    <div class="m-promotion-like__count"> 6520 </div>
                    <div class="m-promotion-like__icon"> </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="m-promotion-info-block">
              <div class="m-promotion-info">
                <p>Dear users, we are pleased to announce a new promotion.</p>
                <p>Until February 23 23:59:59, make a deposit into Staking and receive a 3% cashback.</p>
                <p>Our team is continuously working to improve our services, and we look forward to welcoming you to our Staking platform.</p>
                <p>Make a deposit and enjoy high returns.</p>
              </div>
            </div>
            <div class="m-promotion-buttons-block">
              <div class="m-promotion-buttons">
                <div class="m-promotion-btn-wrapper"> <a href="/signup/" class="m-promotion-btn green-gr-btn">Claim Your Bonus</a> </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script>
			
			
			function gameDetails(button) {
				modal = $('#modalGame');
				if (button.data('game') == 'limbo') {
					modal.find('.modalGameName').html(langLimbo);
				} else {
					modal.find('.modalGameName').html(langDice);
				}
				modal.find('.modalGameFair').removeClass('modalGameFairLoaded');
				modal.find('.modalGameFairBody').css('display','none');
				modal.find('.modalGameFairPanel').removeClass('active');
				modal.find('.modalGameUser').data('user-login',button.data('user-login'));
				modal.find('.modalGameUserName').html(button.data('user-login'));
				modal.find('.modalGameUserPhoto').attr('src',button.data('user-photo'));
				modal.find('.modalGameId').val(button.data('bet-id'));
				modal.find('.modalGameDateTime').html(button.data('date-time-create-text'));
				modal.find('.modalGameAmount').html(button.data('price-text-short'));
				modal.find('.modalGameCurrencyImage').attr('src',button.data('currency-icon'));
				modal.find('.modalGameWinner').removeClass('m-bet-info-item-amount--green');
				modal.find('.modalGameWinnerAmount').html(button.data('winner-amount-text-short'));
				modal.find('.modalGameMultiplier').html('0.00x');
				if (BigNumber(button.data('winner-amount')).isGreaterThan('0')) {
					modal.find('.modalGameWinner').addClass('m-bet-info-item-amount--green');
					modal.find('.modalGameMultiplier').html(BigNumber(button.data('winner-factor')).toFixed(2)+'x');
				}
				modal.find('.modalGameGames').addClass('d-none');
				modal.find('.modalGameFair').data('bet-id',button.data('bet-id'));
				info = JSON.parse(button.data('info'));
				result = JSON.parse(button.data('result'));
				switch(button.data('game')) {
					case 'dice':
					modalDiceRange = modal.find(".game-dice-slider").data("ionRangeSlider");
					modal.find('.modalGameGamesDice').removeClass('d-none');
					
					modal.find('.modalGameGamesDiceCheckNumber').val(info.check_number);
					modal.find('.modalGameGamesDiceRollTitle').html(modal.find('.modalGameGamesDiceRollTitle').data('roll-'+info.mode));
					modalDiceRange.update({
						from: parseFloat(info.check_number),
						from_min: parseFloat(info.check_number),
						from_max: parseFloat(info.check_number) 
					});
					
					checkNumber = BigNumber(info.check_number).toFixed(2);
				    
				    if (info.mode == 'less') {
						winFactor = BigNumber('99').dividedBy(checkNumber);
						winChance = checkNumber;
						modal.find('.irs-bar--single').css('background-color','#2ee57a');
						modal.find('.irs-line').css('background-color','#fc5757');
					} else {
						winFactor = BigNumber('99').dividedBy(BigNumber('100').minus(checkNumber));
						winChance = BigNumber('100').minus(checkNumber);
						modal.find('.irs-bar--single').css('background-color','#fc5757');
						modal.find('.irs-line').css('background-color','#2ee57a');
					}
					
					winFactor = BigNumber(winFactor).toFixed(4);
					winChance = BigNumber(winChance).toFixed(4);
					
					modal.find('.modalGameGamesDiceMultiplier').val(convert(parseFloat(winFactor)));
					modal.find('.modalGameGamesDiceWinChance').val(convert(parseFloat(winChance)));
					modal.find('.irs-with-grid .game-dice-bet').remove();
					if (BigNumber(button.data('winner-amount')).isGreaterThanOrEqualTo(0)) {
						diceResultColor = 'green';
					} else {
						diceResultColor = 'red';
					}
					modal.find('.irs-with-grid').append('<div class="game-dice-bet game-dice-bet--'+diceResultColor+'" style="left: ' + convertToPercentDice(result.number) + '%"><div class="game-dice-bet__count">' + result.number + '</div></div>');
					break;
					case 'limbo':
					modal.find('.modalGameGamesLimbo').removeClass('d-none');
					modal.find('.modalGameGamesLimboTarget').html(info.target_win_factor+' x');
					modal.find('.modalGameGamesLimboResult').html(result.win_factor+' x');
					modal.find('.modalGameGamesLimboRes').removeClass('m-limbo-bet-game-param--red');
					modal.find('.modalGameGamesLimboRes').removeClass('m-limbo-bet-game-param--green');
					if (BigNumber(button.data('winner-amount')).isGreaterThanOrEqualTo(0)) {
						limboResultColor = 'green';
					} else {
						limboResultColor = 'red';
					}
					modal.find('.modalGameGamesLimboRes').addClass('m-limbo-bet-game-param--'+limboResultColor);
					break;
				}
				
				
				modal.modal('show');
			}
			
			function gameFairActive(button) {
				modal = $('#modalGameActiveFair');
				modal.find('.modalGameActiveFairUrlInput').val('');
				modal.find('.modalGameActiveFairUrlButtonDownload').addClass('d-none');
				modal.find('.modalGameActiveFairSeedIdInput').val('');
				modal.find('.modalGameActiveFairSeedIdCopy').data('clipboard-text','');
				modal.find('.modalGameActiveFairSeedIdCopy').attr('data-clipboard-text','');
				modal.find('.modalGameActiveFairNonceInput').val('');
				modal.find('.modalGameActiveFairNonceCopy').data('clipboard-text','');
				modal.find('.modalGameActiveFairNonceCopy').attr('data-clipboard-text','');
				modal.find('.modalGameActiveFairRotateInfo').addClass('d-none');
				modal.find('.modalGameActiveFairRotateButton').addClass('d-none');
				setValueSelectPicker(modal.find('select[name=game_select]'),button.data('game'));
				modal.find('input[name=game]').val(button.data('game'));
				startProgress()
				$.ajax({
					  	url: "/games_active_seed/",
					  	type: "POST",
					  	data: "game="+button.data('game'),
					  	error: function() {
						  	
					  	},
					  	success: function (responseText) {
						  	if (isJson(responseText)){
							  	stopProgress();
							  	ans = JSON.parse(responseText);
							  	if (ans.seed) {
								  	modal.find('.modalGameActiveFairUrlInput').val(ans.seed.full_password_url);
								  	modal.find('.modalGameActiveFairUrlButtonDownload').attr('href',ans.seed.password_url).removeClass('d-none');
								  	modal.find('.modalGameActiveFairSeedIdInput').val(ans.seed.id);
									modal.find('.modalGameActiveFairSeedIdCopy').data('clipboard-text',ans.seed.seed);
									modal.find('.modalGameActiveFairSeedIdCopy').attr('data-clipboard-text',ans.seed.seed);
									modal.find('.modalGameActiveFairNonceInput').val(ans.seed.nonce);
									modal.find('.modalGameActiveFairNonceCopy').data('clipboard-text',ans.seed.nonce);
									modal.find('.modalGameActiveFairNonceCopy').attr('data-clipboard-text',ans.seed.nonce);
									if (ans.seed.nonce >= 1) {
										modal.find('.modalGameActiveFairRotateButton').removeClass('d-none');
									} else {
										modal.find('.modalGameActiveFairRotateInfo').removeClass('d-none');
									}
							  	}
							}
							
						}
					});
				modal.modal('show');
			}
			
			function gameFair(button) {
				if (!button.hasClass('modalGameFairLoaded')) {
					button.addClass('modalGameFairLoaded');
					modal = $('#modalGame');
					modal.find('.modalGameFairUrlInput').val('');
					modal.find('.modalGameFairUrlButtonDownload').addClass('d-none');
					modal.find('.modalGameFairPasswordInput').val('');
					modal.find('.modalGameFairPasswordCopy').data('clipboard-text','');
					modal.find('.modalGameFairPasswordCopy').attr('data-clipboard-text','');
					modal.find('.modalGameFairSeedIdInput').val('');
					modal.find('.modalGameFairSeedIdCopy').data('clipboard-text','');
					modal.find('.modalGameFairSeedIdCopy').attr('data-clipboard-text','');
					modal.find('.modalGameFairNonceInput').val('');
					modal.find('.modalGameFairNonceCopy').data('clipboard-text','');
					modal.find('.modalGameFairNonceCopy').attr('data-clipboard-text','');
					modal.find('.modalGameFairPasswordInfo').addClass('d-none');
					startProgress()
					$.ajax({
					  	url: "/games/",
					  	type: "POST",
					  	data: "id="+button.data('bet-id'),
					  	error: function() {
						  	
					  	},
					  	success: function (responseText) {
						  	if (isJson(responseText)){
							  	stopProgress();
							  	ans = JSON.parse(responseText);
							  	if (ans.games[0]) {
								  	modal.find('.modalGameFairUrlInput').val(ans.games[0].full_password_url);
								  	modal.find('.modalGameFairUrlButtonDownload').attr('href',ans.games[0].password_url).removeClass('d-none');
								  	modal.find('.modalGameFairSeedIdInput').val(ans.games[0].seed);
									modal.find('.modalGameFairSeedIdCopy').data('clipboard-text',ans.games[0].seed);
									modal.find('.modalGameFairSeedIdCopy').attr('data-clipboard-text',ans.games[0].seed);
									modal.find('.modalGameFairNonceInput').val(ans.games[0].nonce);
									modal.find('.modalGameFairNonceCopy').data('clipboard-text',ans.games[0].nonce);
									modal.find('.modalGameFairNonceCopy').attr('data-clipboard-text',ans.games[0].nonce);
								  	if (ans.games[0].password) {
									  	modal.find('.modalGameFairPasswordInput').val(ans.games[0].password);
									  	modal.find('.modalGameFairPasswordCopy').data('clipboard-text',ans.games[0].password);
									  	modal.find('.modalGameFairPasswordCopy').attr('data-clipboard-text',ans.games[0].password);
								  	} else {
									  	modal.find('.modalGameFairPasswordInfo').removeClass('d-none');
								  	}
							  	}
							}
							
						}
					});
				}
				
			}
		</script>
  <div class="modal custom-modal fade modal--bet" id="modalGame" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title-icon"> <img class="image" src="assets/cy/images/svg/games/modal-bet-title-icon.svg" alt=""> </div>
            <div class="modal-title h2">Bet</div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="field-block field-block--bet-id">
              <div class="field-title-block">
                <div class="field-title-block__left">
                  <div class="field-title h3 modalGameName"> </div>
                </div>
                <div class="field-title-block__right">
                  <div class="field-title-user-block">
                    <div class="field-title-user modalGameUser openUserInfo" data-login="" data-from="games">
                      <div class="field-title-user__icon"> <img class="image modalGameUserPhoto" src=""
														alt=""> </div>
                      <div class="field-title-user__username modalGameUserName"> </div>
                      <div class="field-title-user__username-hidden"> </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="field field--input field--id field--have-icon--right-two">
                <input type="text" name="chip-value" autocomplete="off"
										value="" class="modalGameId" readonly>
              </div>
            </div>
            <div class="m-bet-info-block">
              <div class="m-bet-info">
                <div class="m-bet-info-items">
                  <div class="m-bet-info-item m-bet-info-item--date">
                    <div class="m-bet-info-item__left">
                      <div class="m-bet-info-item__title"> Placed on </div>
                    </div>
                    <div class="m-bet-info-item__right">
                      <div class="m-bet-info-item__date modalGameDateTime"> </div>
                    </div>
                  </div>
                  <div class="m-bet-info-item m-bet-info-item--bet">
                    <div class="m-bet-info-item__left">
                      <div class="m-bet-info-item__title"> Bet Amount </div>
                    </div>
                    <div class="m-bet-info-item__right">
                      <div class="m-bet-info-item-amount">
                        <div class="m-bet-info-item-amount__amount modalGameAmount"> </div>
                        <div class="m-bet-info-item-amount__icon"> <img class="image modalGameCurrencyImage" src="" alt=""> </div>
                      </div>
                    </div>
                  </div>
                  <div class="m-bet-info-item m-bet-info-item--ratio">
                    <div class="m-bet-info-item__left">
                      <div class="m-bet-info-item__title"> Multiplier </div>
                    </div>
                    <div class="m-bet-info-item__right">
                      <div class="m-bet-info-item__ratio modalGameMultiplier"></div>
                    </div>
                  </div>
                  <div class="m-bet-info-item m-bet-info-item--payment">
                    <div class="m-bet-info-item__left">
                      <div class="m-bet-info-item__title"> Payout </div>
                    </div>
                    <div class="m-bet-info-item__right">
                      <div class="m-bet-info-item-amount m-bet-info-item-amount--green modalGameWinner">
                        <div class="m-bet-info-item-amount__amount modalGameWinnerAmount"> </div>
                        <div class="m-bet-info-item-amount__icon"> <img class="image modalGameCurrencyImage" src="" alt=""> </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="m-dice-bet-game-block modalGameGames modalGameGamesDice">
              <div class="game-dice-block">
                <div class="game-dice">
                  <div class="game-dice-slider-block">
                    <div class="game-dice-slider-wrapper">
                      <div class="game-dice-slider"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="game-dice-fields-block">
                <div class="game-dice-fields">
                  <div class="game-dice-fields-row">
                    <div class="game-dice-fields-col">
                      <div class="field-block">
                        <div class="field-title-block">
                          <div class="field-title-block__left">
                            <div class="field-title"> Multiplier </div>
                          </div>
                          <div class="field-title-block__right"> </div>
                        </div>
                        <div
														class="field field--input field--have-icon--right">
                          <input type="text" 
															 autocomplete="off" readonly class="modalGameGamesDiceMultiplier">
                          <div class="field-right-panel-block">
                            <div class="field-right-panel">
                              <div
																	class="field-multiplier-icon-block">
                                <div class="field-multiplier-icon"> </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="game-dice-fields-col">
                      <div class="field-block">
                        <div class="field-title-block">
                          <div class="field-title-block__left">
                            <div class="field-title modalGameGamesDiceRollTitle" data-roll-more="Roll Over" data-roll-less="Roll Under"> Roll Over </div>
                          </div>
                          <div class="field-title-block__right"> </div>
                        </div>
                        <div
														class="field field--input field--have-icon--right">
                          <input type="text" 
															 autocomplete="off" readonly class="modalGameGamesDiceCheckNumber">
                          <div class="field-right-panel-block">
                            <div class="field-right-panel">
                              <div class="field-roll-over-icon-block">
                                <div class="field-roll-over-icon"> </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="game-dice-fields-col">
                      <div class="field-block">
                        <div class="field-title-block">
                          <div class="field-title-block__left">
                            <div class="field-title"> Win Chance </div>
                          </div>
                          <div class="field-title-block__right"> </div>
                        </div>
                        <div
														class="field field--input field--have-icon--right">
                          <input type="text" 
															 autocomplete="off" readonly class="modalGameGamesDiceWinChance">
                          <div class="field-right-panel-block">
                            <div class="field-right-panel">
                              <div class="field-percent-icon-block">
                                <div class="field-percent-icon"> </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="m-bet-play-btn-block"> <a href="/games/dice/" class="m-bet-play-btn green-gr-btn">Play Dice</a> </div>
            </div>
            <div class="m-limbo-bet-game-block modalGameGames modalGameGamesLimbo">
              <div class="m-limbo-bet-game-params-block">
                <div class="m-limbo-bet-game-params">
                  <div class="m-limbo-bet-game-param-wrapper">
                    <div class="m-limbo-bet-game-param">
                      <div class="m-limbo-bet-game-param__value modalGameGamesLimboTarget"></div>
                      <div class="m-limbo-bet-game-param__title">Target</div>
                    </div>
                  </div>
                  <div class="m-limbo-bet-game-param-wrapper">
                    <div class="m-limbo-bet-game-param modalGameGamesLimboRes">
                      <div class="m-limbo-bet-game-param__value modalGameGamesLimboResult"></div>
                      <div class="m-limbo-bet-game-param__title">Result</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="m-bet-play-btn-block"> <a href="/games/limbo/" class="m-bet-play-btn green-gr-btn">Play Limbo</a> </div>
            </div>
            <div class="m-bet-honesty-panel-block ">
              <div class="m-bet-honesty-panel modalGameFairPanel">
                <div class="m-bet-honesty-panel-heading modalGameFair" data-bet-id="" onclick="gameFair($(this))">
                  <div class="m-bet-honesty-panel-heading__title"> Provable Fairness </div>
                  <div class="m-bet-honesty-panel-heading__arrow"></div>
                </div>
                <div class="m-bet-honesty-panel-body modalGameFairBody">
                  <div class="m-bet-honesty-panel-form form">
                    <div class="field-block">
                      <div class="field-title-block">
                        <div class="field-title"> File with results </div>
                      </div>
                      <div class="field field--input field--new-client-seed modalGameFairUrl">
                        <input type="text" class="modalGameFairUrlInput" readonly placeholder="" autocomplete="off" value="">
                        <div class="field-right-panel-block modalGameFairUrlButton">
                          <div class="field-right-panel">
                            <div class="field-change-btn-block"> <a href="javascript:void(0)" class="field-change-btn purple-btn modalGameFairUrlButtonDownload">Download</a> </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="field-block">
                      <div class="field-title-block">
                        <div class="field-title"> Password </div>
                      </div>
                      <div class="field field--input field--have-icon--right modalGameFairPassword">
                        <input type="text" class="modalGameFairPasswordInput" readonly placeholder="" autocomplete="off"
														value="">
                        <div class="field-right-panel-block">
                          <div class="field-right-panel">
                            <div class="copy-field-btn-block"> <a href="javascript:void(0)" class="copy-field-btn modalGameFairPasswordCopy"
																	data-clipboard-text=""
																	aria-label="Successfully"></a> </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="field-message-block modalGameFairPasswordInfo">
                      <div class="field-message">
                        <div class="field-message__icon"></div>
                        <div class="field-message__text">Password to verify this bet will be available after rotate your seed.</div>
                      </div>
                    </div>
                    <div class="m-bet-honesty-panel-form-row">
                      <div class="row">
                        <div class="col-12 col-lg-6">
                          <div class="m-bet-honesty-panel-form-col">
                            <div class="field-block">
                              <div class="field-title-block">
                                <div class="field-title"> Seed ID </div>
                              </div>
                              <div class="field field--input field--have-icon--right modalGameFairSeedId">
                                <input type="text" readonly 
																		autocomplete="off" class="modalGameFairSeedIdInput">
                                <div class="field-right-panel-block">
                                  <div class="field-right-panel">
                                    <div class="copy-field-btn-block"> <a href="javascript:void(0)" class="copy-field-btn modalGameFairSeedIdCopy"
																						data-clipboard-text=""
																						aria-label="Successfully"></a> </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-12 col-lg-6">
                          <div class="m-bet-honesty-panel-form-col">
                            <div class="field-block">
                              <div class="field-title-block">
                                <div class="field-title"> Nonce </div>
                              </div>
                              <div class="field field--input field--have-icon--right modalGameFairNonce">
                                <input type="text"  placeholder=""
																		autocomplete="off" value="" class="modalGameFairNonceInput">
                                <div class="field-right-panel-block">
                                  <div class="field-right-panel">
                                    <div class="copy-field-btn-block"> <a href="javascript:void(0)" class="copy-field-btn modalGameFairNonceCopy"
																					data-clipboard-text=""
																					aria-label="Successfully"></a> </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="m-bet-honesty-bottom-link-block"> <a href="/fair-play/" class="m-bet-honesty-bottom-link">Fair Play</a> </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript">
			function partnerDetails(button) {
				modal = $('#modalPartner');
				modal.find('.modalPartnerUserLogin').html(button.data('user-login'));
				modal.find('.modalPartnerUserPhoto').attr('src','assets/'+globalFolder+'/users/'+button.data('user-photo')+'.jpg');
				modal.find('.modalPartnerCurrencyItems').each(function() {
					currency = $(this).data('currency');
					$(this).find('.modalPartnerCurrencyItemsItemGames').removeClass('m-partner-t-i-games--white').html('0');
					$(this).find('.modalPartnerCurrencyItemsItemLotteries').removeClass('m-partner-t-i-games--white').html('0');
					$(this).find('.modalPartnerCurrencyItemsItemStaking').removeClass('m-partner-t-i-games--white').html('0');
				})
				$.each(button.data('games'),function(index, value) {
					modal.find('.modalPartnerCurrencyItemsItem[data-currency="'+index+'"] .modalPartnerCurrencyItemsItemGames').html(value);
					if (BigNumber(value).isGreaterThan(0)) {
						modal.find('.modalPartnerCurrencyItemsItem[data-currency="'+index+'"] .modalPartnerCurrencyItemsItemGames').addClass('m-partner-t-i-games--white');
					}
				})
				$.each(button.data('lotteries'),function(index, value) {
					modal.find('.modalPartnerCurrencyItemsItem[data-currency="'+index+'"] .modalPartnerCurrencyItemsItemLotteries').html(value);
					if (BigNumber(value).isGreaterThan(0)) {
						modal.find('.modalPartnerCurrencyItemsItem[data-currency="'+index+'"] .modalPartnerCurrencyItemsItemLotteries').addClass('m-partner-t-i-games--white');
					}
				})
				$.each(button.data('staking'),function(index, value) {
					modal.find('.modalPartnerCurrencyItemsItem[data-currency="'+index+'"] .modalPartnerCurrencyItemsItemStaking').html(value);
					if (BigNumber(value).isGreaterThan(0)) {
						modal.find('.modalPartnerCurrencyItemsItem[data-currency="'+index+'"] .modalPartnerCurrencyItemsItemStaking').addClass('m-partner-t-i-games--white');
					}
				})
				modal.modal('show');
			}
		</script>
  <div class="modal custom-modal fade modal--partner" id="modalPartner" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title-icon"> <img class="image modalPartnerUserPhoto" src="" alt=""> </div>
            <div class="modal-title h2 modalPartnerUserLogin"></div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="m-partner-table-block">
              <div class="m-partner-table">
                <div class="m-partner-t-heading">
                  <div class="m-partner-t-h-col m-partner-t-h-col--currency">
                    <div class="m-partner-t-h-col-title-block">
                      <div class="m-partner-t-h-col-title"> Asset </div>
                    </div>
                  </div>
                  <div class="m-partner-t-h-col m-partner-t-h-col--games">
                    <div class="m-partner-t-h-col-title-block">
                      <div class="m-partner-t-h-col-title"> Games </div>
                    </div>
                  </div>
                  <div class="m-partner-t-h-col m-partner-t-h-col--lotteries">
                    <div class="m-partner-t-h-col-title-block">
                      <div class="m-partner-t-h-col-title"> Lotteries </div>
                    </div>
                  </div>
                  <div class="m-partner-t-h-col m-partner-t-h-col--stacking">
                    <div class="m-partner-t-h-col-title-block">
                      <div class="m-partner-t-h-col-title"> Staking </div>
                    </div>
                  </div>
                </div>
                <div class="m-partner-t-items modalPartnerCurrencyItems">
                  <div class="m-partner-t-item modalPartnerCurrencyItemsItem" data-currency="usdt">
                    <div class="m-partner-t-i-col m-partner-t-i-col--currency">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Asset </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-currency"> USDT </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--games">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Games </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-games  modalPartnerCurrencyItemsItemGames"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--lotteries">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Lotteries </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-lotteries modalPartnerCurrencyItemsItemLotteries"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--stacking">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Staking </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-stacking modalPartnerCurrencyItemsItemStaking"> </div>
                      </div>
                    </div>
                  </div>
                  <div class="m-partner-t-item modalPartnerCurrencyItemsItem" data-currency="btc">
                    <div class="m-partner-t-i-col m-partner-t-i-col--currency">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Asset </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-currency"> BTC </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--games">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Games </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-games  modalPartnerCurrencyItemsItemGames"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--lotteries">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Lotteries </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-lotteries modalPartnerCurrencyItemsItemLotteries"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--stacking">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Staking </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-stacking modalPartnerCurrencyItemsItemStaking"> </div>
                      </div>
                    </div>
                  </div>
                  <div class="m-partner-t-item modalPartnerCurrencyItemsItem" data-currency="eth">
                    <div class="m-partner-t-i-col m-partner-t-i-col--currency">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Asset </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-currency"> ETH </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--games">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Games </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-games  modalPartnerCurrencyItemsItemGames"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--lotteries">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Lotteries </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-lotteries modalPartnerCurrencyItemsItemLotteries"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--stacking">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Staking </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-stacking modalPartnerCurrencyItemsItemStaking"> </div>
                      </div>
                    </div>
                  </div>
                  <div class="m-partner-t-item modalPartnerCurrencyItemsItem" data-currency="bnb">
                    <div class="m-partner-t-i-col m-partner-t-i-col--currency">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Asset </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-currency"> BNB </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--games">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Games </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-games  modalPartnerCurrencyItemsItemGames"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--lotteries">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Lotteries </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-lotteries modalPartnerCurrencyItemsItemLotteries"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--stacking">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Staking </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-stacking modalPartnerCurrencyItemsItemStaking"> </div>
                      </div>
                    </div>
                  </div>
                  <div class="m-partner-t-item modalPartnerCurrencyItemsItem" data-currency="sol">
                    <div class="m-partner-t-i-col m-partner-t-i-col--currency">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Asset </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-currency"> SOL </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--games">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Games </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-games  modalPartnerCurrencyItemsItemGames"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--lotteries">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Lotteries </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-lotteries modalPartnerCurrencyItemsItemLotteries"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--stacking">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Staking </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-stacking modalPartnerCurrencyItemsItemStaking"> </div>
                      </div>
                    </div>
                  </div>
                  <div class="m-partner-t-item modalPartnerCurrencyItemsItem" data-currency="trx">
                    <div class="m-partner-t-i-col m-partner-t-i-col--currency">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Asset </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-currency"> TRX </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--games">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Games </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-games  modalPartnerCurrencyItemsItemGames"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--lotteries">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Lotteries </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-lotteries modalPartnerCurrencyItemsItemLotteries"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--stacking">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Staking </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-stacking modalPartnerCurrencyItemsItemStaking"> </div>
                      </div>
                    </div>
                  </div>
                  <div class="m-partner-t-item modalPartnerCurrencyItemsItem" data-currency="xrp">
                    <div class="m-partner-t-i-col m-partner-t-i-col--currency">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Asset </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-currency"> XRP </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--games">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Games </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-games  modalPartnerCurrencyItemsItemGames"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--lotteries">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Lotteries </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-lotteries modalPartnerCurrencyItemsItemLotteries"> </div>
                      </div>
                    </div>
                    <div class="m-partner-t-i-col m-partner-t-i-col--stacking">
                      <div class="m-partner-t-i-col__title-block">
                        <div class="m-partner-t-i-col__title"> Staking </div>
                      </div>
                      <div class="m-partner-t-i-col__value-block">
                        <div class="m-partner-t-i-stacking modalPartnerCurrencyItemsItemStaking"> </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal custom-modal fade modal--recovery" id="modalRecovery" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title h2">Restore access</div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="recovery-form-block">
              <form action="/password/" class="form recovery-form">
                <div class="field-block">
                  <div class="field-title-block">
                    <div class="field-title"> Email <span class="field-required-star">*</span> </div>
                  </div>
                  <div class="field field--input field--have-icon field--email">
                    <div class="field-icon"></div>
                    <input id="authEmail" type="text" name="email" maxlength="255" autocomplete="off">
                  </div>
                </div>
                <div class="field-block">
                  <div class="h-captcha" data-sitekey="c226d114-21dc-49d6-aa0a-2f1c65f60186" data-theme="dark"></div>
                </div>
                <div class="form-button-block">
                  <button type="submit" class="green-gr-btn send-btn">
                  <div class="send-btn__text">Restore</div>
                  <div class="send-btn__icon"></div>
                  </button>
                </div>
                <div class="form-bottom-note-block">
                  <div class="form-bottom-note"> Remember your password? <a href="javascript:void(0)" type="button" data-bs-toggle="modal"
												data-bs-target="#modalLogin">Sign In</a> </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal custom-modal fade modal--recovery" id="modalPasswordSet" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title h2">Restore access</div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="recovery-form-block">
              <div class="form-info-block">
                <div class="form-info form-info--warning">
                  <div class="form-info__icon"></div>
                  <div class="form-info__text" > Usually, the message is delivered within 1 to 5 minutes. If you haven’t received the message, please check your "Spam" folder. </div>
                </div>
              </div>
              <br />
              <form action="/password/" class="form recovery-form">
                <div class="field-block">
                  <div class="field-title-block">
                    <div class="field-title"> A verification code sent to your email </div>
                  </div>
                  <div
													class="field field--input field--have-icon field--pin">
                    <div class="field-icon"></div>
                    <input type="text" name="secret" class="form-control" value="">
                  </div>
                </div>
                <div class="field-block">
                  <div class="field-title-block">
                    <div class="field-title"> New password <span class="field-required-star">*</span> </div>
                  </div>
                  <div
											class="field field--input field--have-icon field--password">
                    <div class="field-icon"></div>
                    <input type="password" name="new_password" maxlength="255" class="form-control">
                    <div class="field-right-panel-block">
                      <div class="field-right-panel">
                        <div class="change-pswd-type-link-block">
                          <button type="button" class="change-pswd-type-link"></button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="field-block">
                  <div class="field-title-block">
                    <div class="field-title"> Repeat new password <span class="field-required-star">*</span> </div>
                  </div>
                  <div
											class="field field--input field--have-icon field--password">
                    <div class="field-icon"></div>
                    <input type="password" name="repeat_new_password" maxlength="255" class="form-control">
                    <div class="field-right-panel-block">
                      <div class="field-right-panel">
                        <div class="change-pswd-type-link-block">
                          <button type="button" class="change-pswd-type-link"></button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-button-block">
                  <button type="submit" class="green-gr-btn send-btn">
                  <div class="send-btn__text">Confirm</div>
                  <div class="send-btn__icon"></div>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal custom-modal fade modal--fairness" id="modalGameActiveFair" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title-icon"> <img class="image" src="assets/cy/images/svg/games/modal-fairness-title-icon.svg" alt=""> </div>
            <div class="modal-title h2">Fairness</div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="m-fairness-tabs-content-block">
              <div class="m-fairness-tabs-content">
                <div class="m-fairness-tab-content">
                  <div class="m-fairness-seeds-form-block">
                    <form action="/games_seed_rotate/" class="form m-fairness-seeds-form">
                      <div class="m-fairness-seeds-form-fieldset">
                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> Game </div>
                          </div>
                          <div class="field field--select">
                            <select name="game_select" class="select" disabled>
                              <option
																	data-content='<div class="select-item"><div class="select-item-text">Dice</div></div>'
																	value="dice">Dice</option>
                              <option
																	data-content='<div class="select-item"><div class="select-item-text">Limbo</div></div>'
																	value="limbo">Limbo</option>
                            </select>
                          </div>
                        </div>
                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> File with results </div>
                          </div>
                          <div class="field field--input field--new-client-seed modalGameActiveFairUrl">
                            <input type="text" class="modalGameActiveFairUrlInput" readonly placeholder="" autocomplete="off" value="">
                            <div class="field-right-panel-block modalGameActiveFairUrlButton">
                              <div class="field-right-panel">
                                <div class="field-change-btn-block"> <a href="javascript:void(0)" class="field-change-btn purple-btn modalGameActiveFairUrlButtonDownload">Download</a> </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="field-message-block modalGameActiveFairPasswordInfo">
                          <div class="field-message">
                            <div class="field-message__icon"></div>
                            <div class="field-message__text">Password to verify this bet will be available after rotate your seed.</div>
                          </div>
                        </div>
                        <div class="m-bet-honesty-panel-form-row">
                          <div class="row">
                            <div class="col-12 col-lg-6">
                              <div class="m-bet-honesty-panel-form-col">
                                <div class="field-block">
                                  <div class="field-title-block">
                                    <div class="field-title"> Seed ID </div>
                                  </div>
                                  <div class="field field--input field--have-icon--right modalGameActiveFairSeedId">
                                    <input type="text" readonly 
																				autocomplete="off" class="modalGameActiveFairSeedIdInput">
                                    <div class="field-right-panel-block">
                                      <div class="field-right-panel">
                                        <div class="copy-field-btn-block"> <a href="javascript:void(0)" class="copy-field-btn modalGameActiveFairSeedIdCopy"
																								data-clipboard-text=""
																								aria-label="Successfully"></a> </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div class="col-12 col-lg-6">
                              <div class="m-bet-honesty-panel-form-col">
                                <div class="field-block">
                                  <div class="field-title-block">
                                    <div class="field-title"> Next Nonce </div>
                                  </div>
                                  <div class="field field--input field--have-icon--right modalGameActiveFairNonce">
                                    <input type="text" readonly placeholder=""
																				autocomplete="off" value="" class="modalGameActiveFairNonceInput">
                                    <div class="field-right-panel-block">
                                      <div class="field-right-panel">
                                        <div class="copy-field-btn-block"> <a href="javascript:void(0)" class="copy-field-btn modalGameActiveFairNonceCopy"
																							data-clipboard-text=""
																							aria-label="Successfully"></a> </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="m-fairness-seeds-form-fieldset">
                        <div class="m-fairness-seeds-form-fieldset-title h4"> Rotate Seed </div>
                        <div class="field-message-block modalGameActiveFairRotateInfo">
                          <div class="field-message">
                            <div class="field-message__icon"></div>
                            <div class="field-message__text">You can change the seed if you have played at least one game with the current seed.</div>
                          </div>
                        </div>
                        <div class="field-block modalGameActiveFairRotateButton">
                          <button type="submit" class="purple-btn btn-block"> Rotate </button>
                        </div>
                      </div>
                      <input type="hidden" name="game" value="">
                    </form>
                  </div>
                  <div class="m-bet-honesty-bottom-link-block"> <a href="/games_seeds/" class="m-bet-honesty-bottom-link">All My Seeds</a> </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal custom-modal fade modal--user" id="modalUser" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title-icon"> <img class="image modalUserPhoto" src="" alt=""> </div>
            <div class="modal-title h2 modalUserLogin"></div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="m-user-info-block">
              <div class="m-user-info">
                <div class="m-user-info-items">
                  <div class="m-user-info-item">
                    <div class="m-user-info-item__left">
                      <div class="m-user-info-item__title"> Joined on </div>
                    </div>
                    <div class="m-user-info-item__right">
                      <div class="m-user-info-item__date modalUserDateCreate"> </div>
                    </div>
                  </div>
                  <div class="m-user-info-item modalUserGame" data-game="dice">
                    <div class="m-user-info-item__left">
                      <div class="m-user-info-item-game-block">
                        <div class="m-user-info-item-game m-user-info-item-game--dice">
                          <div class="m-user-info-item-game__icon"></div>
                          <div class="m-user-info-item-game__content">
                            <div class="m-user-info-item-game__title">Dice</div>
                            <div class="m-user-info-item-game__table">(Biggest Win)</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="m-user-info-item__right">
                      <div class="m-user-info-item-amount">
                        <div class="m-user-info-item-amount__amount modalUserGameAmount" data-no-data='No data'> No data </div>
                        <div class="m-user-info-item-amount__icon modalUserGameCurrency d-none"> <img class="image modalUserGameCurrencyIcon" src="" alt=""> </div>
                      </div>
                    </div>
                  </div>
                  <div class="m-user-info-item modalUserGame" data-game="limbo">
                    <div class="m-user-info-item__left">
                      <div class="m-user-info-item-game-block">
                        <div class="m-user-info-item-game m-user-info-item-game--limbo">
                          <div class="m-user-info-item-game__icon"></div>
                          <div class="m-user-info-item-game__content">
                            <div class="m-user-info-item-game__title">Limbo</div>
                            <div class="m-user-info-item-game__table">(Biggest Win)</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="m-user-info-item__right">
                      <div class="m-user-info-item-amount">
                        <div class="m-user-info-item-amount__amount modalUserGameAmount" data-no-data='No data'> No data </div>
                        <div class="m-user-info-item-amount__icon modalUserGameCurrency d-none"> <img class="image modalUserGameCurrencyIcon" src="" alt=""> </div>
                      </div>
                    </div>
                  </div>
                  <div class="m-user-info-item modalUserGame" data-game="lottery">
                    <div class="m-user-info-item__left">
                      <div class="m-user-info-item-game-block">
                        <div class="m-user-info-item-game m-user-info-item-game--roulette">
                          <div class="m-user-info-item-game__icon"></div>
                          <div class="m-user-info-item-game__content">
                            <div class="m-user-info-item-game__title">Lotteries</div>
                            <div class="m-user-info-item-game__table">(Biggest Win)</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="m-user-info-item__right">
                      <div class="m-user-info-item-amount">
                        <div class="m-user-info-item-amount__amount modalUserGameAmount" data-no-data='No data'> No data </div>
                        <div class="m-user-info-item-amount__icon modalUserGameCurrency d-none"> <img class="image modalUserGameCurrencyIcon" src="" alt=""> </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal custom-modal fade modal--info modal--info--success" id="modalSuccess" tabindex="-1"
			aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="modal-info-block">
              <div class="modal-info modal-info--success">
                <div class="modal-info__icon-block">
                  <div class="modal-info__icon"> <img class="image" src="assets/cy/images/svg/dashboard/modal-info-icon--success.svg"
												alt=""> </div>
                </div>
                <div class="modal-info__content">
                  <div class="modal-info__title"> Successfully </div>
                  <div class="modal-info__descr"> </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal custom-modal fade modal--info modal--info--fail" id="modalError" tabindex="-1"
			aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="modal-info-block">
              <div class="modal-info modal-info--fail">
                <div class="modal-info__icon-block">
                  <div class="modal-info__icon"> <img class="image" src="assets/cy/images/svg/dashboard/modal-info-icon--fail.svg"
												alt=""> </div>
                </div>
                <div class="modal-info__content">
                  <div class="modal-info__title"> Warning </div>
                  <div class="modal-info__descr"> </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">/*! jQuery v3.6.1 | (c) OpenJS Foundation and other contributors | jquery.org/license */
!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(C,e){"use strict";var t=[],r=Object.getPrototypeOf,s=t.slice,g=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},u=t.push,i=t.indexOf,n={},o=n.toString,y=n.hasOwnProperty,a=y.toString,l=a.call(Object),v={},m=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},x=function(e){return null!=e&&e===e.window},E=C.document,c={type:!0,src:!0,nonce:!0,noModule:!0};function b(e,t,n){var r,i,o=(n=n||E).createElement("script");if(o.text=e,t)for(r in c)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function w(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?n[o.call(e)]||"object":typeof e}var f="3.6.1",S=function(e,t){return new S.fn.init(e,t)};function p(e){var t=!!e&&"length"in e&&e.length,n=w(e);return!m(e)&&!x(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}S.fn=S.prototype={jquery:f,constructor:S,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=S.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return S.each(this,e)},map:function(n){return this.pushStack(S.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(S.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(S.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(0<=n&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:u,sort:t.sort,splice:t.splice},S.extend=S.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==typeof a||m(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(e=arguments[s]))for(t in e)r=e[t],"__proto__"!==t&&a!==r&&(l&&r&&(S.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[t],o=i&&!Array.isArray(n)?[]:i||S.isPlainObject(n)?n:{},i=!1,a[t]=S.extend(l,o,r)):void 0!==r&&(a[t]=r));return a},S.extend({expando:"jQuery"+(f+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==o.call(e))&&(!(t=r(e))||"function"==typeof(n=y.call(t,"constructor")&&t.constructor)&&a.call(n)===l)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){b(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(p(e)){for(n=e.length;r<n;r++)if(!1===t.call(e[r],r,e[r]))break}else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},makeArray:function(e,t){var n=t||[];return null!=e&&(p(Object(e))?S.merge(n,"string"==typeof e?[e]:e):u.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:i.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!==a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(p(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return g(a)},guid:1,support:v}),"function"==typeof Symbol&&(S.fn[Symbol.iterator]=t[Symbol.iterator]),S.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){n["[object "+t+"]"]=t.toLowerCase()});var d=function(n){var e,d,b,o,i,h,f,g,w,u,l,T,C,a,E,y,s,c,v,S="sizzle"+1*new Date,p=n.document,k=0,r=0,m=ue(),x=ue(),A=ue(),N=ue(),j=function(e,t){return e===t&&(l=!0),0},D={}.hasOwnProperty,t=[],q=t.pop,L=t.push,H=t.push,O=t.slice,P=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},R="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",M="[\\x20\\t\\r\\n\\f]",I="(?:\\\\[\\da-fA-F]{1,6}"+M+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",W="\\["+M+"*("+I+")(?:"+M+"*([*^$|!~]?=)"+M+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+I+"))|)"+M+"*\\]",F=":("+I+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+W+")*)|.*)\\)|)",$=new RegExp(M+"+","g"),B=new RegExp("^"+M+"+|((?:^|[^\\\\])(?:\\\\.)*)"+M+"+$","g"),_=new RegExp("^"+M+"*,"+M+"*"),z=new RegExp("^"+M+"*([>+~]|"+M+")"+M+"*"),U=new RegExp(M+"|>"),X=new RegExp(F),V=new RegExp("^"+I+"$"),G={ID:new RegExp("^#("+I+")"),CLASS:new RegExp("^\\.("+I+")"),TAG:new RegExp("^("+I+"|[*])"),ATTR:new RegExp("^"+W),PSEUDO:new RegExp("^"+F),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+M+"*(even|odd|(([+-]|)(\\d*)n|)"+M+"*(?:([+-]|)"+M+"*(\\d+)|))"+M+"*\\)|)","i"),bool:new RegExp("^(?:"+R+")$","i"),needsContext:new RegExp("^"+M+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+M+"*((?:-\\d)?\\d*)"+M+"*\\)|)(?=[^-]|$)","i")},Y=/HTML$/i,Q=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,K=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ee=/[+~]/,te=new RegExp("\\\\[\\da-fA-F]{1,6}"+M+"?|\\\\([^\\r\\n\\f])","g"),ne=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},re=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ie=function(e,t){return t?"\0"===e?"\ufffd":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},oe=function(){T()},ae=be(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{H.apply(t=O.call(p.childNodes),p.childNodes),t[p.childNodes.length].nodeType}catch(e){H={apply:t.length?function(e,t){L.apply(e,O.call(t))}:function(e,t){var n=e.length,r=0;while(e[n++]=t[r++]);e.length=n-1}}}function se(t,e,n,r){var i,o,a,s,u,l,c,f=e&&e.ownerDocument,p=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==p&&9!==p&&11!==p)return n;if(!r&&(T(e),e=e||C,E)){if(11!==p&&(u=Z.exec(t)))if(i=u[1]){if(9===p){if(!(a=e.getElementById(i)))return n;if(a.id===i)return n.push(a),n}else if(f&&(a=f.getElementById(i))&&v(e,a)&&a.id===i)return n.push(a),n}else{if(u[2])return H.apply(n,e.getElementsByTagName(t)),n;if((i=u[3])&&d.getElementsByClassName&&e.getElementsByClassName)return H.apply(n,e.getElementsByClassName(i)),n}if(d.qsa&&!N[t+" "]&&(!y||!y.test(t))&&(1!==p||"object"!==e.nodeName.toLowerCase())){if(c=t,f=e,1===p&&(U.test(t)||z.test(t))){(f=ee.test(t)&&ve(e.parentNode)||e)===e&&d.scope||((s=e.getAttribute("id"))?s=s.replace(re,ie):e.setAttribute("id",s=S)),o=(l=h(t)).length;while(o--)l[o]=(s?"#"+s:":scope")+" "+xe(l[o]);c=l.join(",")}try{return H.apply(n,f.querySelectorAll(c)),n}catch(e){N(t,!0)}finally{s===S&&e.removeAttribute("id")}}}return g(t.replace(B,"$1"),e,n,r)}function ue(){var r=[];return function e(t,n){return r.push(t+" ")>b.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function le(e){return e[S]=!0,e}function ce(e){var t=C.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function fe(e,t){var n=e.split("|"),r=n.length;while(r--)b.attrHandle[n[r]]=t}function pe(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)while(n=n.nextSibling)if(n===t)return-1;return e?1:-1}function de(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function he(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}function ge(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&ae(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function ye(a){return le(function(o){return o=+o,le(function(e,t){var n,r=a([],e.length,o),i=r.length;while(i--)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function ve(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}for(e in d=se.support={},i=se.isXML=function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!Y.test(t||n&&n.nodeName||"HTML")},T=se.setDocument=function(e){var t,n,r=e?e.ownerDocument||e:p;return r!=C&&9===r.nodeType&&r.documentElement&&(a=(C=r).documentElement,E=!i(C),p!=C&&(n=C.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",oe,!1):n.attachEvent&&n.attachEvent("onunload",oe)),d.scope=ce(function(e){return a.appendChild(e).appendChild(C.createElement("div")),"undefined"!=typeof e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),d.attributes=ce(function(e){return e.className="i",!e.getAttribute("className")}),d.getElementsByTagName=ce(function(e){return e.appendChild(C.createComment("")),!e.getElementsByTagName("*").length}),d.getElementsByClassName=K.test(C.getElementsByClassName),d.getById=ce(function(e){return a.appendChild(e).id=S,!C.getElementsByName||!C.getElementsByName(S).length}),d.getById?(b.filter.ID=function(e){var t=e.replace(te,ne);return function(e){return e.getAttribute("id")===t}},b.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&E){var n=t.getElementById(e);return n?[n]:[]}}):(b.filter.ID=function(e){var n=e.replace(te,ne);return function(e){var t="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return t&&t.value===n}},b.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&E){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];i=t.getElementsByName(e),r=0;while(o=i[r++])if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),b.find.TAG=d.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):d.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){while(n=o[i++])1===n.nodeType&&r.push(n);return r}return o},b.find.CLASS=d.getElementsByClassName&&function(e,t){if("undefined"!=typeof t.getElementsByClassName&&E)return t.getElementsByClassName(e)},s=[],y=[],(d.qsa=K.test(C.querySelectorAll))&&(ce(function(e){var t;a.appendChild(e).innerHTML="<a id='"+S+"'></a><select id='"+S+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&y.push("[*^$]="+M+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||y.push("\\["+M+"*(?:value|"+R+")"),e.querySelectorAll("[id~="+S+"-]").length||y.push("~="),(t=C.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||y.push("\\["+M+"*name"+M+"*="+M+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||y.push(":checked"),e.querySelectorAll("a#"+S+"+*").length||y.push(".#.+[+~]"),e.querySelectorAll("\\\f"),y.push("[\\r\\n\\f]")}),ce(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=C.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&y.push("name"+M+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&y.push(":enabled",":disabled"),a.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&y.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),y.push(",.*:")})),(d.matchesSelector=K.test(c=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector))&&ce(function(e){d.disconnectedMatch=c.call(e,"*"),c.call(e,"[s!='']:x"),s.push("!=",F)}),y=y.length&&new RegExp(y.join("|")),s=s.length&&new RegExp(s.join("|")),t=K.test(a.compareDocumentPosition),v=t||K.test(a.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)while(t=t.parentNode)if(t===e)return!0;return!1},j=t?function(e,t){if(e===t)return l=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!d.sortDetached&&t.compareDocumentPosition(e)===n?e==C||e.ownerDocument==p&&v(p,e)?-1:t==C||t.ownerDocument==p&&v(p,t)?1:u?P(u,e)-P(u,t):0:4&n?-1:1)}:function(e,t){if(e===t)return l=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],s=[t];if(!i||!o)return e==C?-1:t==C?1:i?-1:o?1:u?P(u,e)-P(u,t):0;if(i===o)return pe(e,t);n=e;while(n=n.parentNode)a.unshift(n);n=t;while(n=n.parentNode)s.unshift(n);while(a[r]===s[r])r++;return r?pe(a[r],s[r]):a[r]==p?-1:s[r]==p?1:0}),C},se.matches=function(e,t){return se(e,null,null,t)},se.matchesSelector=function(e,t){if(T(e),d.matchesSelector&&E&&!N[t+" "]&&(!s||!s.test(t))&&(!y||!y.test(t)))try{var n=c.call(e,t);if(n||d.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){N(t,!0)}return 0<se(t,C,null,[e]).length},se.contains=function(e,t){return(e.ownerDocument||e)!=C&&T(e),v(e,t)},se.attr=function(e,t){(e.ownerDocument||e)!=C&&T(e);var n=b.attrHandle[t.toLowerCase()],r=n&&D.call(b.attrHandle,t.toLowerCase())?n(e,t,!E):void 0;return void 0!==r?r:d.attributes||!E?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},se.escape=function(e){return(e+"").replace(re,ie)},se.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},se.uniqueSort=function(e){var t,n=[],r=0,i=0;if(l=!d.detectDuplicates,u=!d.sortStable&&e.slice(0),e.sort(j),l){while(t=e[i++])t===e[i]&&(r=n.push(i));while(r--)e.splice(n[r],1)}return u=null,e},o=se.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else while(t=e[r++])n+=o(t);return n},(b=se.selectors={cacheLength:50,createPseudo:le,match:G,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(te,ne),e[3]=(e[3]||e[4]||e[5]||"").replace(te,ne),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||se.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&se.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return G.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&X.test(n)&&(t=h(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(te,ne).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=m[e+" "];return t||(t=new RegExp("(^|"+M+")"+e+"("+M+"|$)"))&&m(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(n,r,i){return function(e){var t=se.attr(e,n);return null==t?"!="===r:!r||(t+="","="===r?t===i:"!="===r?t!==i:"^="===r?i&&0===t.indexOf(i):"*="===r?i&&-1<t.indexOf(i):"$="===r?i&&t.slice(-i.length)===i:"~="===r?-1<(" "+t.replace($," ")+" ").indexOf(i):"|="===r&&(t===i||t.slice(0,i.length+1)===i+"-"))}},CHILD:function(h,e,t,g,y){var v="nth"!==h.slice(0,3),m="last"!==h.slice(-4),x="of-type"===e;return 1===g&&0===y?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,u,l=v!==m?"nextSibling":"previousSibling",c=e.parentNode,f=x&&e.nodeName.toLowerCase(),p=!n&&!x,d=!1;if(c){if(v){while(l){a=e;while(a=a[l])if(x?a.nodeName.toLowerCase()===f:1===a.nodeType)return!1;u=l="only"===h&&!u&&"nextSibling"}return!0}if(u=[m?c.firstChild:c.lastChild],m&&p){d=(s=(r=(i=(o=(a=c)[S]||(a[S]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===k&&r[1])&&r[2],a=s&&c.childNodes[s];while(a=++s&&a&&a[l]||(d=s=0)||u.pop())if(1===a.nodeType&&++d&&a===e){i[h]=[k,s,d];break}}else if(p&&(d=s=(r=(i=(o=(a=e)[S]||(a[S]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===k&&r[1]),!1===d)while(a=++s&&a&&a[l]||(d=s=0)||u.pop())if((x?a.nodeName.toLowerCase()===f:1===a.nodeType)&&++d&&(p&&((i=(o=a[S]||(a[S]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]=[k,d]),a===e))break;return(d-=y)===g||d%g==0&&0<=d/g}}},PSEUDO:function(e,o){var t,a=b.pseudos[e]||b.setFilters[e.toLowerCase()]||se.error("unsupported pseudo: "+e);return a[S]?a(o):1<a.length?(t=[e,e,"",o],b.setFilters.hasOwnProperty(e.toLowerCase())?le(function(e,t){var n,r=a(e,o),i=r.length;while(i--)e[n=P(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:le(function(e){var r=[],i=[],s=f(e.replace(B,"$1"));return s[S]?le(function(e,t,n,r){var i,o=s(e,null,r,[]),a=e.length;while(a--)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),r[0]=null,!i.pop()}}),has:le(function(t){return function(e){return 0<se(t,e).length}}),contains:le(function(t){return t=t.replace(te,ne),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:le(function(n){return V.test(n||"")||se.error("unsupported lang: "+n),n=n.replace(te,ne).toLowerCase(),function(e){var t;do{if(t=E?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===a},focus:function(e){return e===C.activeElement&&(!C.hasFocus||C.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ge(!1),disabled:ge(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!b.pseudos.empty(e)},header:function(e){return J.test(e.nodeName)},input:function(e){return Q.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:ye(function(){return[0]}),last:ye(function(e,t){return[t-1]}),eq:ye(function(e,t,n){return[n<0?n+t:n]}),even:ye(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:ye(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:ye(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:ye(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=b.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[e]=de(e);for(e in{submit:!0,reset:!0})b.pseudos[e]=he(e);function me(){}function xe(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function be(s,e,t){var u=e.dir,l=e.next,c=l||u,f=t&&"parentNode"===c,p=r++;return e.first?function(e,t,n){while(e=e[u])if(1===e.nodeType||f)return s(e,t,n);return!1}:function(e,t,n){var r,i,o,a=[k,p];if(n){while(e=e[u])if((1===e.nodeType||f)&&s(e,t,n))return!0}else while(e=e[u])if(1===e.nodeType||f)if(i=(o=e[S]||(e[S]={}))[e.uniqueID]||(o[e.uniqueID]={}),l&&l===e.nodeName.toLowerCase())e=e[u]||e;else{if((r=i[c])&&r[0]===k&&r[1]===p)return a[2]=r[2];if((i[c]=a)[2]=s(e,t,n))return!0}return!1}}function we(i){return 1<i.length?function(e,t,n){var r=i.length;while(r--)if(!i[r](e,t,n))return!1;return!0}:i[0]}function Te(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),l&&t.push(s)));return a}function Ce(d,h,g,y,v,e){return y&&!y[S]&&(y=Ce(y)),v&&!v[S]&&(v=Ce(v,e)),le(function(e,t,n,r){var i,o,a,s=[],u=[],l=t.length,c=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)se(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),f=!d||!e&&h?c:Te(c,s,d,n,r),p=g?v||(e?d:l||y)?[]:t:f;if(g&&g(f,p,n,r),y){i=Te(p,u),y(i,[],n,r),o=i.length;while(o--)(a=i[o])&&(p[u[o]]=!(f[u[o]]=a))}if(e){if(v||d){if(v){i=[],o=p.length;while(o--)(a=p[o])&&i.push(f[o]=a);v(null,p=[],i,r)}o=p.length;while(o--)(a=p[o])&&-1<(i=v?P(e,a):s[o])&&(e[i]=!(t[i]=a))}}else p=Te(p===t?p.splice(l,p.length):p),v?v(null,t,p,r):H.apply(t,p)})}function Ee(e){for(var i,t,n,r=e.length,o=b.relative[e[0].type],a=o||b.relative[" "],s=o?1:0,u=be(function(e){return e===i},a,!0),l=be(function(e){return-1<P(i,e)},a,!0),c=[function(e,t,n){var r=!o&&(n||t!==w)||((i=t).nodeType?u(e,t,n):l(e,t,n));return i=null,r}];s<r;s++)if(t=b.relative[e[s].type])c=[be(we(c),t)];else{if((t=b.filter[e[s].type].apply(null,e[s].matches))[S]){for(n=++s;n<r;n++)if(b.relative[e[n].type])break;return Ce(1<s&&we(c),1<s&&xe(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(B,"$1"),t,s<n&&Ee(e.slice(s,n)),n<r&&Ee(e=e.slice(n)),n<r&&xe(e))}c.push(t)}return we(c)}return me.prototype=b.filters=b.pseudos,b.setFilters=new me,h=se.tokenize=function(e,t){var n,r,i,o,a,s,u,l=x[e+" "];if(l)return t?0:l.slice(0);a=e,s=[],u=b.preFilter;while(a){for(o in n&&!(r=_.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=z.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(B," ")}),a=a.slice(n.length)),b.filter)!(r=G[o].exec(a))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?se.error(e):x(e,s).slice(0)},f=se.compile=function(e,t){var n,y,v,m,x,r,i=[],o=[],a=A[e+" "];if(!a){t||(t=h(e)),n=t.length;while(n--)(a=Ee(t[n]))[S]?i.push(a):o.push(a);(a=A(e,(y=o,m=0<(v=i).length,x=0<y.length,r=function(e,t,n,r,i){var o,a,s,u=0,l="0",c=e&&[],f=[],p=w,d=e||x&&b.find.TAG("*",i),h=k+=null==p?1:Math.random()||.1,g=d.length;for(i&&(w=t==C||t||i);l!==g&&null!=(o=d[l]);l++){if(x&&o){a=0,t||o.ownerDocument==C||(T(o),n=!E);while(s=y[a++])if(s(o,t||C,n)){r.push(o);break}i&&(k=h)}m&&((o=!s&&o)&&u--,e&&c.push(o))}if(u+=l,m&&l!==u){a=0;while(s=v[a++])s(c,f,t,n);if(e){if(0<u)while(l--)c[l]||f[l]||(f[l]=q.call(r));f=Te(f)}H.apply(r,f),i&&!e&&0<f.length&&1<u+v.length&&se.uniqueSort(r)}return i&&(k=h,w=p),c},m?le(r):r))).selector=e}return a},g=se.select=function(e,t,n,r){var i,o,a,s,u,l="function"==typeof e&&e,c=!r&&h(e=l.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(a=o[0]).type&&9===t.nodeType&&E&&b.relative[o[1].type]){if(!(t=(b.find.ID(a.matches[0].replace(te,ne),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}i=G.needsContext.test(e)?0:o.length;while(i--){if(a=o[i],b.relative[s=a.type])break;if((u=b.find[s])&&(r=u(a.matches[0].replace(te,ne),ee.test(o[0].type)&&ve(t.parentNode)||t))){if(o.splice(i,1),!(e=r.length&&xe(o)))return H.apply(n,r),n;break}}}return(l||f(e,c))(r,t,!E,n,!t||ee.test(e)&&ve(t.parentNode)||t),n},d.sortStable=S.split("").sort(j).join("")===S,d.detectDuplicates=!!l,T(),d.sortDetached=ce(function(e){return 1&e.compareDocumentPosition(C.createElement("fieldset"))}),ce(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||fe("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),d.attributes&&ce(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||fe("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ce(function(e){return null==e.getAttribute("disabled")})||fe(R,function(e,t,n){var r;if(!n)return!0===e[t]?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null}),se}(C);S.find=d,S.expr=d.selectors,S.expr[":"]=S.expr.pseudos,S.uniqueSort=S.unique=d.uniqueSort,S.text=d.getText,S.isXMLDoc=d.isXML,S.contains=d.contains,S.escapeSelector=d.escape;var h=function(e,t,n){var r=[],i=void 0!==n;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(i&&S(e).is(n))break;r.push(e)}return r},T=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},k=S.expr.match.needsContext;function A(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var N=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function j(e,n,r){return m(n)?S.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?S.grep(e,function(e){return e===n!==r}):"string"!=typeof n?S.grep(e,function(e){return-1<i.call(n,e)!==r}):S.filter(n,e,r)}S.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?S.find.matchesSelector(r,e)?[r]:[]:S.find.matches(e,S.grep(t,function(e){return 1===e.nodeType}))},S.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(S(e).filter(function(){for(t=0;t<r;t++)if(S.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)S.find(e,i[t],n);return 1<r?S.uniqueSort(n):n},filter:function(e){return this.pushStack(j(this,e||[],!1))},not:function(e){return this.pushStack(j(this,e||[],!0))},is:function(e){return!!j(this,"string"==typeof e&&k.test(e)?S(e):e||[],!1).length}});var D,q=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(S.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||D,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:q.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof S?t[0]:t,S.merge(this,S.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:E,!0)),N.test(r[1])&&S.isPlainObject(t))for(r in t)m(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=E.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):m(e)?void 0!==n.ready?n.ready(e):e(S):S.makeArray(e,this)}).prototype=S.fn,D=S(E);var L=/^(?:parents|prev(?:Until|All))/,H={children:!0,contents:!0,next:!0,prev:!0};function O(e,t){while((e=e[t])&&1!==e.nodeType);return e}S.fn.extend({has:function(e){var t=S(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(S.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&S(e);if(!k.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&S.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?S.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?i.call(S(e),this[0]):i.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(S.uniqueSort(S.merge(this.get(),S(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),S.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return h(e,"parentNode")},parentsUntil:function(e,t,n){return h(e,"parentNode",n)},next:function(e){return O(e,"nextSibling")},prev:function(e){return O(e,"previousSibling")},nextAll:function(e){return h(e,"nextSibling")},prevAll:function(e){return h(e,"previousSibling")},nextUntil:function(e,t,n){return h(e,"nextSibling",n)},prevUntil:function(e,t,n){return h(e,"previousSibling",n)},siblings:function(e){return T((e.parentNode||{}).firstChild,e)},children:function(e){return T(e.firstChild)},contents:function(e){return null!=e.contentDocument&&r(e.contentDocument)?e.contentDocument:(A(e,"template")&&(e=e.content||e),S.merge([],e.childNodes))}},function(r,i){S.fn[r]=function(e,t){var n=S.map(this,i,e);return"Until"!==r.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=S.filter(t,n)),1<this.length&&(H[r]||S.uniqueSort(n),L.test(r)&&n.reverse()),this.pushStack(n)}});var P=/[^\x20\t\r\n\f]+/g;function R(e){return e}function M(e){throw e}function I(e,t,n,r){var i;try{e&&m(i=e.promise)?i.call(e).done(t).fail(n):e&&m(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}S.Callbacks=function(r){var e,n;r="string"==typeof r?(e=r,n={},S.each(e.match(P)||[],function(e,t){n[t]=!0}),n):S.extend({},r);var i,t,o,a,s=[],u=[],l=-1,c=function(){for(a=a||r.once,o=i=!0;u.length;l=-1){t=u.shift();while(++l<s.length)!1===s[l].apply(t[0],t[1])&&r.stopOnFalse&&(l=s.length,t=!1)}r.memory||(t=!1),i=!1,a&&(s=t?[]:"")},f={add:function(){return s&&(t&&!i&&(l=s.length-1,u.push(t)),function n(e){S.each(e,function(e,t){m(t)?r.unique&&f.has(t)||s.push(t):t&&t.length&&"string"!==w(t)&&n(t)})}(arguments),t&&!i&&c()),this},remove:function(){return S.each(arguments,function(e,t){var n;while(-1<(n=S.inArray(t,s,n)))s.splice(n,1),n<=l&&l--}),this},has:function(e){return e?-1<S.inArray(e,s):0<s.length},empty:function(){return s&&(s=[]),this},disable:function(){return a=u=[],s=t="",this},disabled:function(){return!s},lock:function(){return a=u=[],t||i||(s=t=""),this},locked:function(){return!!a},fireWith:function(e,t){return a||(t=[e,(t=t||[]).slice?t.slice():t],u.push(t),i||c()),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!o}};return f},S.extend({Deferred:function(e){var o=[["notify","progress",S.Callbacks("memory"),S.Callbacks("memory"),2],["resolve","done",S.Callbacks("once memory"),S.Callbacks("once memory"),0,"resolved"],["reject","fail",S.Callbacks("once memory"),S.Callbacks("once memory"),1,"rejected"]],i="pending",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},"catch":function(e){return a.then(null,e)},pipe:function(){var i=arguments;return S.Deferred(function(r){S.each(o,function(e,t){var n=m(i[t[4]])&&i[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&m(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var u=0;function l(i,o,a,s){return function(){var n=this,r=arguments,e=function(){var e,t;if(!(i<u)){if((e=a.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,m(t)?s?t.call(e,l(u,o,R,s),l(u,o,M,s)):(u++,t.call(e,l(u,o,R,s),l(u,o,M,s),l(u,o,R,o.notifyWith))):(a!==R&&(n=void 0,r=[e]),(s||o.resolveWith)(n,r))}},t=s?e:function(){try{e()}catch(e){S.Deferred.exceptionHook&&S.Deferred.exceptionHook(e,t.stackTrace),u<=i+1&&(a!==M&&(n=void 0,r=[e]),o.rejectWith(n,r))}};i?t():(S.Deferred.getStackHook&&(t.stackTrace=S.Deferred.getStackHook()),C.setTimeout(t))}}return S.Deferred(function(e){o[0][3].add(l(0,e,m(r)?r:R,e.notifyWith)),o[1][3].add(l(0,e,m(t)?t:R)),o[2][3].add(l(0,e,m(n)?n:M))}).promise()},promise:function(e){return null!=e?S.extend(e,a):a}},s={};return S.each(o,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){i=r},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){var n=arguments.length,t=n,r=Array(t),i=s.call(arguments),o=S.Deferred(),a=function(t){return function(e){r[t]=this,i[t]=1<arguments.length?s.call(arguments):e,--n||o.resolveWith(r,i)}};if(n<=1&&(I(e,o.done(a(t)).resolve,o.reject,!n),"pending"===o.state()||m(i[t]&&i[t].then)))return o.then();while(t--)I(i[t],a(t),o.reject);return o.promise()}});var W=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;S.Deferred.exceptionHook=function(e,t){C.console&&C.console.warn&&e&&W.test(e.name)&&C.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},S.readyException=function(e){C.setTimeout(function(){throw e})};var F=S.Deferred();function $(){E.removeEventListener("DOMContentLoaded",$),C.removeEventListener("load",$),S.ready()}S.fn.ready=function(e){return F.then(e)["catch"](function(e){S.readyException(e)}),this},S.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--S.readyWait:S.isReady)||(S.isReady=!0)!==e&&0<--S.readyWait||F.resolveWith(E,[S])}}),S.ready.then=F.then,"complete"===E.readyState||"loading"!==E.readyState&&!E.documentElement.doScroll?C.setTimeout(S.ready):(E.addEventListener("DOMContentLoaded",$),C.addEventListener("load",$));var B=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===w(n))for(s in i=!0,n)B(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,m(r)||(a=!0),l&&(a?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(S(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},_=/^-ms-/,z=/-([a-z])/g;function U(e,t){return t.toUpperCase()}function X(e){return e.replace(_,"ms-").replace(z,U)}var V=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function G(){this.expando=S.expando+G.uid++}G.uid=1,G.prototype={cache:function(e){var t=e[this.expando];return t||(t={},V(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[X(t)]=n;else for(r in t)i[X(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][X(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(X):(t=X(t))in r?[t]:t.match(P)||[]).length;while(n--)delete r[t[n]]}(void 0===t||S.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!S.isEmptyObject(t)}};var Y=new G,Q=new G,J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,K=/[A-Z]/g;function Z(e,t,n){var r,i;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(K,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:J.test(i)?JSON.parse(i):i)}catch(e){}Q.set(e,t,n)}else n=void 0;return n}S.extend({hasData:function(e){return Q.hasData(e)||Y.hasData(e)},data:function(e,t,n){return Q.access(e,t,n)},removeData:function(e,t){Q.remove(e,t)},_data:function(e,t,n){return Y.access(e,t,n)},_removeData:function(e,t){Y.remove(e,t)}}),S.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;if(void 0===n){if(this.length&&(i=Q.get(o),1===o.nodeType&&!Y.get(o,"hasDataAttrs"))){t=a.length;while(t--)a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=X(r.slice(5)),Z(o,r,i[r]));Y.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof n?this.each(function(){Q.set(this,n)}):B(this,function(e){var t;if(o&&void 0===e)return void 0!==(t=Q.get(o,n))?t:void 0!==(t=Z(o,n))?t:void 0;this.each(function(){Q.set(this,n,e)})},null,e,1<arguments.length,null,!0)},removeData:function(e){return this.each(function(){Q.remove(this,e)})}}),S.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=Y.get(e,t),n&&(!r||Array.isArray(n)?r=Y.access(e,t,S.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=S.queue(e,t),r=n.length,i=n.shift(),o=S._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){S.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Y.get(e,n)||Y.access(e,n,{empty:S.Callbacks("once memory").add(function(){Y.remove(e,[t+"queue",n])})})}}),S.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?S.queue(this[0],t):void 0===n?this:this.each(function(){var e=S.queue(this,t,n);S._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&S.dequeue(this,t)})},dequeue:function(e){return this.each(function(){S.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=S.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};"string"!=typeof e&&(t=e,e=void 0),e=e||"fx";while(a--)(n=Y.get(o[a],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var ee=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,te=new RegExp("^(?:([+-])=|)("+ee+")([a-z%]*)$","i"),ne=["Top","Right","Bottom","Left"],re=E.documentElement,ie=function(e){return S.contains(e.ownerDocument,e)},oe={composed:!0};re.getRootNode&&(ie=function(e){return S.contains(e.ownerDocument,e)||e.getRootNode(oe)===e.ownerDocument});var ae=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&ie(e)&&"none"===S.css(e,"display")};function se(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return S.css(e,t,"")},u=s(),l=n&&n[3]||(S.cssNumber[t]?"":"px"),c=e.nodeType&&(S.cssNumber[t]||"px"!==l&&+u)&&te.exec(S.css(e,t));if(c&&c[3]!==l){u/=2,l=l||c[3],c=+u||1;while(a--)S.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;c*=2,S.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var ue={};function le(e,t){for(var n,r,i,o,a,s,u,l=[],c=0,f=e.length;c<f;c++)(r=e[c]).style&&(n=r.style.display,t?("none"===n&&(l[c]=Y.get(r,"display")||null,l[c]||(r.style.display="")),""===r.style.display&&ae(r)&&(l[c]=(u=a=o=void 0,a=(i=r).ownerDocument,s=i.nodeName,(u=ue[s])||(o=a.body.appendChild(a.createElement(s)),u=S.css(o,"display"),o.parentNode.removeChild(o),"none"===u&&(u="block"),ue[s]=u)))):"none"!==n&&(l[c]="none",Y.set(r,"display",n)));for(c=0;c<f;c++)null!=l[c]&&(e[c].style.display=l[c]);return e}S.fn.extend({show:function(){return le(this,!0)},hide:function(){return le(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ae(this)?S(this).show():S(this).hide()})}});var ce,fe,pe=/^(?:checkbox|radio)$/i,de=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,he=/^$|^module$|\/(?:java|ecma)script/i;ce=E.createDocumentFragment().appendChild(E.createElement("div")),(fe=E.createElement("input")).setAttribute("type","radio"),fe.setAttribute("checked","checked"),fe.setAttribute("name","t"),ce.appendChild(fe),v.checkClone=ce.cloneNode(!0).cloneNode(!0).lastChild.checked,ce.innerHTML="<textarea>x</textarea>",v.noCloneChecked=!!ce.cloneNode(!0).lastChild.defaultValue,ce.innerHTML="<option></option>",v.option=!!ce.lastChild;var ge={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function ye(e,t){var n;return n="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&A(e,t)?S.merge([e],n):n}function ve(e,t){for(var n=0,r=e.length;n<r;n++)Y.set(e[n],"globalEval",!t||Y.get(t[n],"globalEval"))}ge.tbody=ge.tfoot=ge.colgroup=ge.caption=ge.thead,ge.th=ge.td,v.option||(ge.optgroup=ge.option=[1,"<select multiple='multiple'>","</select>"]);var me=/<|&#?\w+;/;function xe(e,t,n,r,i){for(var o,a,s,u,l,c,f=t.createDocumentFragment(),p=[],d=0,h=e.length;d<h;d++)if((o=e[d])||0===o)if("object"===w(o))S.merge(p,o.nodeType?[o]:o);else if(me.test(o)){a=a||f.appendChild(t.createElement("div")),s=(de.exec(o)||["",""])[1].toLowerCase(),u=ge[s]||ge._default,a.innerHTML=u[1]+S.htmlPrefilter(o)+u[2],c=u[0];while(c--)a=a.lastChild;S.merge(p,a.childNodes),(a=f.firstChild).textContent=""}else p.push(t.createTextNode(o));f.textContent="",d=0;while(o=p[d++])if(r&&-1<S.inArray(o,r))i&&i.push(o);else if(l=ie(o),a=ye(f.appendChild(o),"script"),l&&ve(a),n){c=0;while(o=a[c++])he.test(o.type||"")&&n.push(o)}return f}var be=/^([^.]*)(?:\.(.+)|)/;function we(){return!0}function Te(){return!1}function Ce(e,t){return e===function(){try{return E.activeElement}catch(e){}}()==("focus"===t)}function Ee(e,t,n,r,i,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)Ee(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Te;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return S().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=S.guid++)),e.each(function(){S.event.add(this,t,i,r,n)})}function Se(e,i,o){o?(Y.set(e,i,!1),S.event.add(e,i,{namespace:!1,handler:function(e){var t,n,r=Y.get(this,i);if(1&e.isTrigger&&this[i]){if(r.length)(S.event.special[i]||{}).delegateType&&e.stopPropagation();else if(r=s.call(arguments),Y.set(this,i,r),t=o(this,i),this[i](),r!==(n=Y.get(this,i))||t?Y.set(this,i,!1):n={},r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n&&n.value}else r.length&&(Y.set(this,i,{value:S.event.trigger(S.extend(r[0],S.Event.prototype),r.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===Y.get(e,i)&&S.event.add(e,i,we)}S.event={global:{},add:function(t,e,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,y=Y.get(t);if(V(t)){n.handler&&(n=(o=n).handler,i=o.selector),i&&S.find.matchesSelector(re,i),n.guid||(n.guid=S.guid++),(u=y.events)||(u=y.events=Object.create(null)),(a=y.handle)||(a=y.handle=function(e){return"undefined"!=typeof S&&S.event.triggered!==e.type?S.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(P)||[""]).length;while(l--)d=g=(s=be.exec(e[l])||[])[1],h=(s[2]||"").split(".").sort(),d&&(f=S.event.special[d]||{},d=(i?f.delegateType:f.bindType)||d,f=S.event.special[d]||{},c=S.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&S.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=u[d])||((p=u[d]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(t,r,h,a)||t.addEventListener&&t.addEventListener(d,a)),f.add&&(f.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),S.event.global[d]=!0)}},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,y=Y.hasData(e)&&Y.get(e);if(y&&(u=y.events)){l=(t=(t||"").match(P)||[""]).length;while(l--)if(d=g=(s=be.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),d){f=S.event.special[d]||{},p=u[d=(r?f.delegateType:f.bindType)||d]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=p.length;while(o--)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));a&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,y.handle)||S.removeEvent(e,d,y.handle),delete u[d])}else for(d in u)S.event.remove(e,d+t[l],n,r,!0);S.isEmptyObject(u)&&Y.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=new Array(arguments.length),u=S.event.fix(e),l=(Y.get(this,"events")||Object.create(null))[u.type]||[],c=S.event.special[u.type]||{};for(s[0]=u,t=1;t<arguments.length;t++)s[t]=arguments[t];if(u.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,u)){a=S.event.handlers.call(this,u,l),t=0;while((i=a[t++])&&!u.isPropagationStopped()){u.currentTarget=i.elem,n=0;while((o=i.handlers[n++])&&!u.isImmediatePropagationStopped())u.rnamespace&&!1!==o.namespace&&!u.rnamespace.test(o.namespace)||(u.handleObj=o,u.data=o.data,void 0!==(r=((S.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s))&&!1===(u.result=r)&&(u.preventDefault(),u.stopPropagation()))}return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&1<=e.button))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?-1<S(i,this).index(l):S.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(t,e){Object.defineProperty(S.Event.prototype,t,{enumerable:!0,configurable:!0,get:m(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[S.expando]?e:new S.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return pe.test(t.type)&&t.click&&A(t,"input")&&Se(t,"click",we),!1},trigger:function(e){var t=this||e;return pe.test(t.type)&&t.click&&A(t,"input")&&Se(t,"click"),!0},_default:function(e){var t=e.target;return pe.test(t.type)&&t.click&&A(t,"input")&&Y.get(t,"click")||A(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},S.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},S.Event=function(e,t){if(!(this instanceof S.Event))return new S.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?we:Te,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&S.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[S.expando]=!0},S.Event.prototype={constructor:S.Event,isDefaultPrevented:Te,isPropagationStopped:Te,isImmediatePropagationStopped:Te,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=we,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=we,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=we,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},S.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,"char":!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},S.event.addProp),S.each({focus:"focusin",blur:"focusout"},function(t,e){S.event.special[t]={setup:function(){return Se(this,t,Ce),!1},trigger:function(){return Se(this,t),!0},_default:function(e){return Y.get(e.target,t)},delegateType:e}}),S.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){S.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||S.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),S.fn.extend({on:function(e,t,n,r){return Ee(this,e,t,n,r)},one:function(e,t,n,r){return Ee(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,S(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Te),this.each(function(){S.event.remove(this,e,n,t)})}});var ke=/<script|<style|<link/i,Ae=/checked\s*(?:[^=]|=\s*.checked.)/i,Ne=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function je(e,t){return A(e,"table")&&A(11!==t.nodeType?t:t.firstChild,"tr")&&S(e).children("tbody")[0]||e}function De(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function qe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Le(e,t){var n,r,i,o,a,s;if(1===t.nodeType){if(Y.hasData(e)&&(s=Y.get(e).events))for(i in Y.remove(t,"handle events"),s)for(n=0,r=s[i].length;n<r;n++)S.event.add(t,i,s[i][n]);Q.hasData(e)&&(o=Q.access(e),a=S.extend({},o),Q.set(t,a))}}function He(n,r,i,o){r=g(r);var e,t,a,s,u,l,c=0,f=n.length,p=f-1,d=r[0],h=m(d);if(h||1<f&&"string"==typeof d&&!v.checkClone&&Ae.test(d))return n.each(function(e){var t=n.eq(e);h&&(r[0]=d.call(this,e,t.html())),He(t,r,i,o)});if(f&&(t=(e=xe(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(s=(a=S.map(ye(e,"script"),De)).length;c<f;c++)u=e,c!==p&&(u=S.clone(u,!0,!0),s&&S.merge(a,ye(u,"script"))),i.call(n[c],u,c);if(s)for(l=a[a.length-1].ownerDocument,S.map(a,qe),c=0;c<s;c++)u=a[c],he.test(u.type||"")&&!Y.access(u,"globalEval")&&S.contains(l,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?S._evalUrl&&!u.noModule&&S._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},l):b(u.textContent.replace(Ne,""),u,l))}return n}function Oe(e,t,n){for(var r,i=t?S.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||S.cleanData(ye(r)),r.parentNode&&(n&&ie(r)&&ve(ye(r,"script")),r.parentNode.removeChild(r));return e}S.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,u,l,c=e.cloneNode(!0),f=ie(e);if(!(v.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||S.isXMLDoc(e)))for(a=ye(c),r=0,i=(o=ye(e)).length;r<i;r++)s=o[r],u=a[r],void 0,"input"===(l=u.nodeName.toLowerCase())&&pe.test(s.type)?u.checked=s.checked:"input"!==l&&"textarea"!==l||(u.defaultValue=s.defaultValue);if(t)if(n)for(o=o||ye(e),a=a||ye(c),r=0,i=o.length;r<i;r++)Le(o[r],a[r]);else Le(e,c);return 0<(a=ye(c,"script")).length&&ve(a,!f&&ye(e,"script")),c},cleanData:function(e){for(var t,n,r,i=S.event.special,o=0;void 0!==(n=e[o]);o++)if(V(n)){if(t=n[Y.expando]){if(t.events)for(r in t.events)i[r]?S.event.remove(n,r):S.removeEvent(n,r,t.handle);n[Y.expando]=void 0}n[Q.expando]&&(n[Q.expando]=void 0)}}}),S.fn.extend({detach:function(e){return Oe(this,e,!0)},remove:function(e){return Oe(this,e)},text:function(e){return B(this,function(e){return void 0===e?S.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return He(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||je(this,e).appendChild(e)})},prepend:function(){return He(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=je(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return He(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return He(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(S.cleanData(ye(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return S.clone(this,e,t)})},html:function(e){return B(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!ke.test(e)&&!ge[(de.exec(e)||["",""])[1].toLowerCase()]){e=S.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(S.cleanData(ye(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return He(this,arguments,function(e){var t=this.parentNode;S.inArray(this,n)<0&&(S.cleanData(ye(this)),t&&t.replaceChild(e,this))},n)}}),S.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){S.fn[e]=function(e){for(var t,n=[],r=S(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),S(r[o])[a](t),u.apply(n,t.get());return this.pushStack(n)}});var Pe=new RegExp("^("+ee+")(?!px)[a-z%]+$","i"),Re=/^--/,Me=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=C),t.getComputedStyle(e)},Ie=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},We=new RegExp(ne.join("|"),"i"),Fe="[\\x20\\t\\r\\n\\f]",$e=new RegExp("^"+Fe+"+|((?:^|[^\\\\])(?:\\\\.)*)"+Fe+"+$","g");function Be(e,t,n){var r,i,o,a,s=Re.test(t),u=e.style;return(n=n||Me(e))&&(a=n.getPropertyValue(t)||n[t],s&&(a=a.replace($e,"$1")),""!==a||ie(e)||(a=S.style(e,t)),!v.pixelBoxStyles()&&Pe.test(a)&&We.test(t)&&(r=u.width,i=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=a,a=n.width,u.width=r,u.minWidth=i,u.maxWidth=o)),void 0!==a?a+"":a}function _e(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(l){u.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",l.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",re.appendChild(u).appendChild(l);var e=C.getComputedStyle(l);n="1%"!==e.top,s=12===t(e.marginLeft),l.style.right="60%",o=36===t(e.right),r=36===t(e.width),l.style.position="absolute",i=12===t(l.offsetWidth/3),re.removeChild(u),l=null}}function t(e){return Math.round(parseFloat(e))}var n,r,i,o,a,s,u=E.createElement("div"),l=E.createElement("div");l.style&&(l.style.backgroundClip="content-box",l.cloneNode(!0).style.backgroundClip="",v.clearCloneStyle="content-box"===l.style.backgroundClip,S.extend(v,{boxSizingReliable:function(){return e(),r},pixelBoxStyles:function(){return e(),o},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),s},scrollboxSize:function(){return e(),i},reliableTrDimensions:function(){var e,t,n,r;return null==a&&(e=E.createElement("table"),t=E.createElement("tr"),n=E.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",re.appendChild(e).appendChild(t).appendChild(n),r=C.getComputedStyle(t),a=parseInt(r.height,10)+parseInt(r.borderTopWidth,10)+parseInt(r.borderBottomWidth,10)===t.offsetHeight,re.removeChild(e)),a}}))}();var ze=["Webkit","Moz","ms"],Ue=E.createElement("div").style,Xe={};function Ve(e){var t=S.cssProps[e]||Xe[e];return t||(e in Ue?e:Xe[e]=function(e){var t=e[0].toUpperCase()+e.slice(1),n=ze.length;while(n--)if((e=ze[n]+t)in Ue)return e}(e)||e)}var Ge=/^(none|table(?!-c[ea]).+)/,Ye={position:"absolute",visibility:"hidden",display:"block"},Qe={letterSpacing:"0",fontWeight:"400"};function Je(e,t,n){var r=te.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function Ke(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(u+=S.css(e,n+ne[a],!0,i)),r?("content"===n&&(u-=S.css(e,"padding"+ne[a],!0,i)),"margin"!==n&&(u-=S.css(e,"border"+ne[a]+"Width",!0,i))):(u+=S.css(e,"padding"+ne[a],!0,i),"padding"!==n?u+=S.css(e,"border"+ne[a]+"Width",!0,i):s+=S.css(e,"border"+ne[a]+"Width",!0,i));return!r&&0<=o&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u}function Ze(e,t,n){var r=Me(e),i=(!v.boxSizingReliable()||n)&&"border-box"===S.css(e,"boxSizing",!1,r),o=i,a=Be(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(Pe.test(a)){if(!n)return a;a="auto"}return(!v.boxSizingReliable()&&i||!v.reliableTrDimensions()&&A(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===S.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===S.css(e,"boxSizing",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+Ke(e,t,n||(i?"border":"content"),o,r,a)+"px"}function et(e,t,n,r,i){return new et.prototype.init(e,t,n,r,i)}S.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Be(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=X(t),u=Re.test(t),l=e.style;if(u||(t=Ve(s)),a=S.cssHooks[t]||S.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];"string"===(o=typeof n)&&(i=te.exec(n))&&i[1]&&(n=se(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||u||(n+=i&&i[3]||(S.cssNumber[s]?"":"px")),v.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o,a,s=X(t);return Re.test(t)||(t=Ve(s)),(a=S.cssHooks[t]||S.cssHooks[s])&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=Be(e,t,r)),"normal"===i&&t in Qe&&(i=Qe[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),S.each(["height","width"],function(e,u){S.cssHooks[u]={get:function(e,t,n){if(t)return!Ge.test(S.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?Ze(e,u,n):Ie(e,Ye,function(){return Ze(e,u,n)})},set:function(e,t,n){var r,i=Me(e),o=!v.scrollboxSize()&&"absolute"===i.position,a=(o||n)&&"border-box"===S.css(e,"boxSizing",!1,i),s=n?Ke(e,u,n,a,i):0;return a&&o&&(s-=Math.ceil(e["offset"+u[0].toUpperCase()+u.slice(1)]-parseFloat(i[u])-Ke(e,u,"border",!1,i)-.5)),s&&(r=te.exec(t))&&"px"!==(r[3]||"px")&&(e.style[u]=t,t=S.css(e,u)),Je(0,t,s)}}}),S.cssHooks.marginLeft=_e(v.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Be(e,"marginLeft"))||e.getBoundingClientRect().left-Ie(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),S.each({margin:"",padding:"",border:"Width"},function(i,o){S.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+ne[t]+o]=r[t]||r[t-2]||r[0];return n}},"margin"!==i&&(S.cssHooks[i+o].set=Je)}),S.fn.extend({css:function(e,t){return B(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Me(e),i=t.length;a<i;a++)o[t[a]]=S.css(e,t[a],!1,r);return o}return void 0!==n?S.style(e,t,n):S.css(e,t)},e,t,1<arguments.length)}}),((S.Tween=et).prototype={constructor:et,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||S.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(S.cssNumber[n]?"":"px")},cur:function(){var e=et.propHooks[this.prop];return e&&e.get?e.get(this):et.propHooks._default.get(this)},run:function(e){var t,n=et.propHooks[this.prop];return this.options.duration?this.pos=t=S.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):et.propHooks._default.set(this),this}}).init.prototype=et.prototype,(et.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=S.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){S.fx.step[e.prop]?S.fx.step[e.prop](e):1!==e.elem.nodeType||!S.cssHooks[e.prop]&&null==e.elem.style[Ve(e.prop)]?e.elem[e.prop]=e.now:S.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=et.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},S.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},S.fx=et.prototype.init,S.fx.step={};var tt,nt,rt,it,ot=/^(?:toggle|show|hide)$/,at=/queueHooks$/;function st(){nt&&(!1===E.hidden&&C.requestAnimationFrame?C.requestAnimationFrame(st):C.setTimeout(st,S.fx.interval),S.fx.tick())}function ut(){return C.setTimeout(function(){tt=void 0}),tt=Date.now()}function lt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=ne[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function ct(e,t,n){for(var r,i=(ft.tweeners[t]||[]).concat(ft.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function ft(o,e,t){var n,a,r=0,i=ft.prefilters.length,s=S.Deferred().always(function(){delete u.elem}),u=function(){if(a)return!1;for(var e=tt||ut(),t=Math.max(0,l.startTime+l.duration-e),n=1-(t/l.duration||0),r=0,i=l.tweens.length;r<i;r++)l.tweens[r].run(n);return s.notifyWith(o,[l,n,t]),n<1&&i?t:(i||s.notifyWith(o,[l,1,0]),s.resolveWith(o,[l]),!1)},l=s.promise({elem:o,props:S.extend({},e),opts:S.extend(!0,{specialEasing:{},easing:S.easing._default},t),originalProperties:e,originalOptions:t,startTime:tt||ut(),duration:t.duration,tweens:[],createTween:function(e,t){var n=S.Tween(o,l.opts,e,t,l.opts.specialEasing[e]||l.opts.easing);return l.tweens.push(n),n},stop:function(e){var t=0,n=e?l.tweens.length:0;if(a)return this;for(a=!0;t<n;t++)l.tweens[t].run(1);return e?(s.notifyWith(o,[l,1,0]),s.resolveWith(o,[l,e])):s.rejectWith(o,[l,e]),this}}),c=l.props;for(!function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=X(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=S.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);r<i;r++)if(n=ft.prefilters[r].call(l,o,c,l.opts))return m(n.stop)&&(S._queueHooks(l.elem,l.opts.queue).stop=n.stop.bind(n)),n;return S.map(c,ct,l),m(l.opts.start)&&l.opts.start.call(o,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),S.fx.timer(S.extend(u,{elem:o,anim:l,queue:l.opts.queue})),l}S.Animation=S.extend(ft,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return se(n.elem,e,te.exec(t),n),n}]},tweener:function(e,t){m(e)?(t=e,e=["*"]):e=e.match(P);for(var n,r=0,i=e.length;r<i;r++)n=e[r],ft.tweeners[n]=ft.tweeners[n]||[],ft.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,u,l,c,f="width"in t||"height"in t,p=this,d={},h=e.style,g=e.nodeType&&ae(e),y=Y.get(e,"fxshow");for(r in n.queue||(null==(a=S._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always(function(){p.always(function(){a.unqueued--,S.queue(e,"fx").length||a.empty.fire()})})),t)if(i=t[r],ot.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!y||void 0===y[r])continue;g=!0}d[r]=y&&y[r]||S.style(e,r)}if((u=!S.isEmptyObject(t))||!S.isEmptyObject(d))for(r in f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(l=y&&y.display)&&(l=Y.get(e,"display")),"none"===(c=S.css(e,"display"))&&(l?c=l:(le([e],!0),l=e.style.display||l,c=S.css(e,"display"),le([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===S.css(e,"float")&&(u||(p.done(function(){h.display=l}),null==l&&(c=h.display,l="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1,d)u||(y?"hidden"in y&&(g=y.hidden):y=Y.access(e,"fxshow",{display:l}),o&&(y.hidden=!g),g&&le([e],!0),p.done(function(){for(r in g||le([e]),Y.remove(e,"fxshow"),d)S.style(e,r,d[r])})),u=ct(g?y[r]:0,r,p),r in y||(y[r]=u.start,g&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?ft.prefilters.unshift(e):ft.prefilters.push(e)}}),S.speed=function(e,t,n){var r=e&&"object"==typeof e?S.extend({},e):{complete:n||!n&&t||m(e)&&e,duration:e,easing:n&&t||t&&!m(t)&&t};return S.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in S.fx.speeds?r.duration=S.fx.speeds[r.duration]:r.duration=S.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){m(r.old)&&r.old.call(this),r.queue&&S.dequeue(this,r.queue)},r},S.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ae).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){var i=S.isEmptyObject(t),o=S.speed(e,n,r),a=function(){var e=ft(this,S.extend({},t),o);(i||Y.get(this,"finish"))&&e.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(i,e,o){var a=function(e){var t=e.stop;delete e.stop,t(o)};return"string"!=typeof i&&(o=e,e=i,i=void 0),e&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=S.timers,r=Y.get(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&at.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||S.dequeue(this,i)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var e,t=Y.get(this),n=t[a+"queue"],r=t[a+"queueHooks"],i=S.timers,o=n?n.length:0;for(t.finish=!0,S.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===a&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),S.each(["toggle","show","hide"],function(e,r){var i=S.fn[r];S.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(lt(r,!0),e,t,n)}}),S.each({slideDown:lt("show"),slideUp:lt("hide"),slideToggle:lt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){S.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),S.timers=[],S.fx.tick=function(){var e,t=0,n=S.timers;for(tt=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||S.fx.stop(),tt=void 0},S.fx.timer=function(e){S.timers.push(e),S.fx.start()},S.fx.interval=13,S.fx.start=function(){nt||(nt=!0,st())},S.fx.stop=function(){nt=null},S.fx.speeds={slow:600,fast:200,_default:400},S.fn.delay=function(r,e){return r=S.fx&&S.fx.speeds[r]||r,e=e||"fx",this.queue(e,function(e,t){var n=C.setTimeout(e,r);t.stop=function(){C.clearTimeout(n)}})},rt=E.createElement("input"),it=E.createElement("select").appendChild(E.createElement("option")),rt.type="checkbox",v.checkOn=""!==rt.value,v.optSelected=it.selected,(rt=E.createElement("input")).value="t",rt.type="radio",v.radioValue="t"===rt.value;var pt,dt=S.expr.attrHandle;S.fn.extend({attr:function(e,t){return B(this,S.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){S.removeAttr(this,e)})}}),S.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"==typeof e.getAttribute?S.prop(e,t,n):(1===o&&S.isXMLDoc(e)||(i=S.attrHooks[t.toLowerCase()]||(S.expr.match.bool.test(t)?pt:void 0)),void 0!==n?null===n?void S.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=S.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!v.radioValue&&"radio"===t&&A(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(P);if(i&&1===e.nodeType)while(n=i[r++])e.removeAttribute(n)}}),pt={set:function(e,t,n){return!1===t?S.removeAttr(e,n):e.setAttribute(n,n),n}},S.each(S.expr.match.bool.source.match(/\w+/g),function(e,t){var a=dt[t]||S.find.attr;dt[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(i=dt[o],dt[o]=r,r=null!=a(e,t,n)?o:null,dt[o]=i),r}});var ht=/^(?:input|select|textarea|button)$/i,gt=/^(?:a|area)$/i;function yt(e){return(e.match(P)||[]).join(" ")}function vt(e){return e.getAttribute&&e.getAttribute("class")||""}function mt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(P)||[]}S.fn.extend({prop:function(e,t){return B(this,S.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[S.propFix[e]||e]})}}),S.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&S.isXMLDoc(e)||(t=S.propFix[t]||t,i=S.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=S.find.attr(e,"tabindex");return t?parseInt(t,10):ht.test(e.nodeName)||gt.test(e.nodeName)&&e.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}}),v.optSelected||(S.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),S.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){S.propFix[this.toLowerCase()]=this}),S.fn.extend({addClass:function(t){var e,n,r,i,o,a;return m(t)?this.each(function(e){S(this).addClass(t.call(this,e,vt(this)))}):(e=mt(t)).length?this.each(function(){if(r=vt(this),n=1===this.nodeType&&" "+yt(r)+" "){for(o=0;o<e.length;o++)i=e[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");a=yt(n),r!==a&&this.setAttribute("class",a)}}):this},removeClass:function(t){var e,n,r,i,o,a;return m(t)?this.each(function(e){S(this).removeClass(t.call(this,e,vt(this)))}):arguments.length?(e=mt(t)).length?this.each(function(){if(r=vt(this),n=1===this.nodeType&&" "+yt(r)+" "){for(o=0;o<e.length;o++){i=e[o];while(-1<n.indexOf(" "+i+" "))n=n.replace(" "+i+" "," ")}a=yt(n),r!==a&&this.setAttribute("class",a)}}):this:this.attr("class","")},toggleClass:function(t,n){var e,r,i,o,a=typeof t,s="string"===a||Array.isArray(t);return m(t)?this.each(function(e){S(this).toggleClass(t.call(this,e,vt(this),n),n)}):"boolean"==typeof n&&s?n?this.addClass(t):this.removeClass(t):(e=mt(t),this.each(function(){if(s)for(o=S(this),i=0;i<e.length;i++)r=e[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==t&&"boolean"!==a||((r=vt(this))&&Y.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===t?"":Y.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,r=0;t=" "+e+" ";while(n=this[r++])if(1===n.nodeType&&-1<(" "+yt(vt(n))+" ").indexOf(t))return!0;return!1}});var xt=/\r/g;S.fn.extend({val:function(n){var r,e,i,t=this[0];return arguments.length?(i=m(n),this.each(function(e){var t;1===this.nodeType&&(null==(t=i?n.call(this,e,S(this).val()):n)?t="":"number"==typeof t?t+="":Array.isArray(t)&&(t=S.map(t,function(e){return null==e?"":e+""})),(r=S.valHooks[this.type]||S.valHooks[this.nodeName.toLowerCase()])&&"set"in r&&void 0!==r.set(this,t,"value")||(this.value=t))})):t?(r=S.valHooks[t.type]||S.valHooks[t.nodeName.toLowerCase()])&&"get"in r&&void 0!==(e=r.get(t,"value"))?e:"string"==typeof(e=t.value)?e.replace(xt,""):null==e?"":e:void 0}}),S.extend({valHooks:{option:{get:function(e){var t=S.find.attr(e,"value");return null!=t?t:yt(S.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,a="select-one"===e.type,s=a?null:[],u=a?o+1:i.length;for(r=o<0?u:a?o:0;r<u;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!A(n.parentNode,"optgroup"))){if(t=S(n).val(),a)return t;s.push(t)}return s},set:function(e,t){var n,r,i=e.options,o=S.makeArray(t),a=i.length;while(a--)((r=i[a]).selected=-1<S.inArray(S.valHooks.option.get(r),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),S.each(["radio","checkbox"],function(){S.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<S.inArray(S(e).val(),t)}},v.checkOn||(S.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),v.focusin="onfocusin"in C;var bt=/^(?:focusinfocus|focusoutblur)$/,wt=function(e){e.stopPropagation()};S.extend(S.event,{trigger:function(e,t,n,r){var i,o,a,s,u,l,c,f,p=[n||E],d=y.call(e,"type")?e.type:e,h=y.call(e,"namespace")?e.namespace.split("."):[];if(o=f=a=n=n||E,3!==n.nodeType&&8!==n.nodeType&&!bt.test(d+S.event.triggered)&&(-1<d.indexOf(".")&&(d=(h=d.split(".")).shift(),h.sort()),u=d.indexOf(":")<0&&"on"+d,(e=e[S.expando]?e:new S.Event(d,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=h.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:S.makeArray(t,[e]),c=S.event.special[d]||{},r||!c.trigger||!1!==c.trigger.apply(n,t))){if(!r&&!c.noBubble&&!x(n)){for(s=c.delegateType||d,bt.test(s+d)||(o=o.parentNode);o;o=o.parentNode)p.push(o),a=o;a===(n.ownerDocument||E)&&p.push(a.defaultView||a.parentWindow||C)}i=0;while((o=p[i++])&&!e.isPropagationStopped())f=o,e.type=1<i?s:c.bindType||d,(l=(Y.get(o,"events")||Object.create(null))[e.type]&&Y.get(o,"handle"))&&l.apply(o,t),(l=u&&o[u])&&l.apply&&V(o)&&(e.result=l.apply(o,t),!1===e.result&&e.preventDefault());return e.type=d,r||e.isDefaultPrevented()||c._default&&!1!==c._default.apply(p.pop(),t)||!V(n)||u&&m(n[d])&&!x(n)&&((a=n[u])&&(n[u]=null),S.event.triggered=d,e.isPropagationStopped()&&f.addEventListener(d,wt),n[d](),e.isPropagationStopped()&&f.removeEventListener(d,wt),S.event.triggered=void 0,a&&(n[u]=a)),e.result}},simulate:function(e,t,n){var r=S.extend(new S.Event,n,{type:e,isSimulated:!0});S.event.trigger(r,null,t)}}),S.fn.extend({trigger:function(e,t){return this.each(function(){S.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return S.event.trigger(e,t,n,!0)}}),v.focusin||S.each({focus:"focusin",blur:"focusout"},function(n,r){var i=function(e){S.event.simulate(r,e.target,S.event.fix(e))};S.event.special[r]={setup:function(){var e=this.ownerDocument||this.document||this,t=Y.access(e,r);t||e.addEventListener(n,i,!0),Y.access(e,r,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=Y.access(e,r)-1;t?Y.access(e,r,t):(e.removeEventListener(n,i,!0),Y.remove(e,r))}}});var Tt=C.location,Ct={guid:Date.now()},Et=/\?/;S.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new C.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||S.error("Invalid XML: "+(n?S.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t};var St=/\[\]$/,kt=/\r?\n/g,At=/^(?:submit|button|image|reset|file)$/i,Nt=/^(?:input|select|textarea|keygen)/i;function jt(n,e,r,i){var t;if(Array.isArray(e))S.each(e,function(e,t){r||St.test(n)?i(n,t):jt(n+"["+("object"==typeof t&&null!=t?e:"")+"]",t,r,i)});else if(r||"object"!==w(e))i(n,e);else for(t in e)jt(n+"["+t+"]",e[t],r,i)}S.param=function(e,t){var n,r=[],i=function(e,t){var n=m(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!S.isPlainObject(e))S.each(e,function(){i(this.name,this.value)});else for(n in e)jt(n,e[n],t,i);return r.join("&")},S.fn.extend({serialize:function(){return S.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=S.prop(this,"elements");return e?S.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!S(this).is(":disabled")&&Nt.test(this.nodeName)&&!At.test(e)&&(this.checked||!pe.test(e))}).map(function(e,t){var n=S(this).val();return null==n?null:Array.isArray(n)?S.map(n,function(e){return{name:t.name,value:e.replace(kt,"\r\n")}}):{name:t.name,value:n.replace(kt,"\r\n")}}).get()}});var Dt=/%20/g,qt=/#.*$/,Lt=/([?&])_=[^&]*/,Ht=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ot=/^(?:GET|HEAD)$/,Pt=/^\/\//,Rt={},Mt={},It="*/".concat("*"),Wt=E.createElement("a");function Ft(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(P)||[];if(m(t))while(n=i[r++])"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function $t(t,i,o,a){var s={},u=t===Mt;function l(e){var r;return s[e]=!0,S.each(t[e]||[],function(e,t){var n=t(i,o,a);return"string"!=typeof n||u||s[n]?u?!(r=n):void 0:(i.dataTypes.unshift(n),l(n),!1)}),r}return l(i.dataTypes[0])||!s["*"]&&l("*")}function Bt(e,t){var n,r,i=S.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&S.extend(!0,e,r),e}Wt.href=Tt.href,S.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Tt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Tt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":It,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":S.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Bt(Bt(e,S.ajaxSettings),t):Bt(S.ajaxSettings,e)},ajaxPrefilter:Ft(Rt),ajaxTransport:Ft(Mt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var c,f,p,n,d,r,h,g,i,o,y=S.ajaxSetup({},t),v=y.context||y,m=y.context&&(v.nodeType||v.jquery)?S(v):S.event,x=S.Deferred(),b=S.Callbacks("once memory"),w=y.statusCode||{},a={},s={},u="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(h){if(!n){n={};while(t=Ht.exec(p))n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2])}t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return h?p:null},setRequestHeader:function(e,t){return null==h&&(e=s[e.toLowerCase()]=s[e.toLowerCase()]||e,a[e]=t),this},overrideMimeType:function(e){return null==h&&(y.mimeType=e),this},statusCode:function(e){var t;if(e)if(h)T.always(e[T.status]);else for(t in e)w[t]=[w[t],e[t]];return this},abort:function(e){var t=e||u;return c&&c.abort(t),l(0,t),this}};if(x.promise(T),y.url=((e||y.url||Tt.href)+"").replace(Pt,Tt.protocol+"//"),y.type=t.method||t.type||y.method||y.type,y.dataTypes=(y.dataType||"*").toLowerCase().match(P)||[""],null==y.crossDomain){r=E.createElement("a");try{r.href=y.url,r.href=r.href,y.crossDomain=Wt.protocol+"//"+Wt.host!=r.protocol+"//"+r.host}catch(e){y.crossDomain=!0}}if(y.data&&y.processData&&"string"!=typeof y.data&&(y.data=S.param(y.data,y.traditional)),$t(Rt,y,t,T),h)return T;for(i in(g=S.event&&y.global)&&0==S.active++&&S.event.trigger("ajaxStart"),y.type=y.type.toUpperCase(),y.hasContent=!Ot.test(y.type),f=y.url.replace(qt,""),y.hasContent?y.data&&y.processData&&0===(y.contentType||"").indexOf("application/x-www-form-urlencoded")&&(y.data=y.data.replace(Dt,"+")):(o=y.url.slice(f.length),y.data&&(y.processData||"string"==typeof y.data)&&(f+=(Et.test(f)?"&":"?")+y.data,delete y.data),!1===y.cache&&(f=f.replace(Lt,"$1"),o=(Et.test(f)?"&":"?")+"_="+Ct.guid+++o),y.url=f+o),y.ifModified&&(S.lastModified[f]&&T.setRequestHeader("If-Modified-Since",S.lastModified[f]),S.etag[f]&&T.setRequestHeader("If-None-Match",S.etag[f])),(y.data&&y.hasContent&&!1!==y.contentType||t.contentType)&&T.setRequestHeader("Content-Type",y.contentType),T.setRequestHeader("Accept",y.dataTypes[0]&&y.accepts[y.dataTypes[0]]?y.accepts[y.dataTypes[0]]+("*"!==y.dataTypes[0]?", "+It+"; q=0.01":""):y.accepts["*"]),y.headers)T.setRequestHeader(i,y.headers[i]);if(y.beforeSend&&(!1===y.beforeSend.call(v,T,y)||h))return T.abort();if(u="abort",b.add(y.complete),T.done(y.success),T.fail(y.error),c=$t(Mt,y,t,T)){if(T.readyState=1,g&&m.trigger("ajaxSend",[T,y]),h)return T;y.async&&0<y.timeout&&(d=C.setTimeout(function(){T.abort("timeout")},y.timeout));try{h=!1,c.send(a,l)}catch(e){if(h)throw e;l(-1,e)}}else l(-1,"No Transport");function l(e,t,n,r){var i,o,a,s,u,l=t;h||(h=!0,d&&C.clearTimeout(d),c=void 0,p=r||"",T.readyState=0<e?4:0,i=200<=e&&e<300||304===e,n&&(s=function(e,t,n){var r,i,o,a,s=e.contents,u=e.dataTypes;while("*"===u[0])u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]}(y,T,n)),!i&&-1<S.inArray("script",y.dataTypes)&&S.inArray("json",y.dataTypes)<0&&(y.converters["text script"]=function(){}),s=function(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];o=c.shift();while(o)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(a=l[u+" "+o]||l["* "+o]))for(i in l)if((s=i.split(" "))[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e["throws"])t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(y,s,T,i),i?(y.ifModified&&((u=T.getResponseHeader("Last-Modified"))&&(S.lastModified[f]=u),(u=T.getResponseHeader("etag"))&&(S.etag[f]=u)),204===e||"HEAD"===y.type?l="nocontent":304===e?l="notmodified":(l=s.state,o=s.data,i=!(a=s.error))):(a=l,!e&&l||(l="error",e<0&&(e=0))),T.status=e,T.statusText=(t||l)+"",i?x.resolveWith(v,[o,l,T]):x.rejectWith(v,[T,l,a]),T.statusCode(w),w=void 0,g&&m.trigger(i?"ajaxSuccess":"ajaxError",[T,y,i?o:a]),b.fireWith(v,[T,l]),g&&(m.trigger("ajaxComplete",[T,y]),--S.active||S.event.trigger("ajaxStop")))}return T},getJSON:function(e,t,n){return S.get(e,t,n,"json")},getScript:function(e,t){return S.get(e,void 0,t,"script")}}),S.each(["get","post"],function(e,i){S[i]=function(e,t,n,r){return m(t)&&(r=r||n,n=t,t=void 0),S.ajax(S.extend({url:e,type:i,dataType:r,data:t,success:n},S.isPlainObject(e)&&e))}}),S.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),S._evalUrl=function(e,t,n){return S.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){S.globalEval(e,t,n)}})},S.fn.extend({wrapAll:function(e){var t;return this[0]&&(m(e)&&(e=e.call(this[0])),t=S(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){var e=this;while(e.firstElementChild)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return m(n)?this.each(function(e){S(this).wrapInner(n.call(this,e))}):this.each(function(){var e=S(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=m(t);return this.each(function(e){S(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){S(this).replaceWith(this.childNodes)}),this}}),S.expr.pseudos.hidden=function(e){return!S.expr.pseudos.visible(e)},S.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},S.ajaxSettings.xhr=function(){try{return new C.XMLHttpRequest}catch(e){}};var _t={0:200,1223:204},zt=S.ajaxSettings.xhr();v.cors=!!zt&&"withCredentials"in zt,v.ajax=zt=!!zt,S.ajaxTransport(function(i){var o,a;if(v.cors||zt&&!i.crossDomain)return{send:function(e,t){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=a=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===e?r.abort():"error"===e?"number"!=typeof r.status?t(0,"error"):t(r.status,r.statusText):t(_t[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),a=r.onerror=r.ontimeout=o("error"),void 0!==r.onabort?r.onabort=a:r.onreadystatechange=function(){4===r.readyState&&C.setTimeout(function(){o&&a()})},o=o("abort");try{r.send(i.hasContent&&i.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),S.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),S.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return S.globalEval(e),e}}}),S.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),S.ajaxTransport("script",function(n){var r,i;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){r=S("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",i=function(e){r.remove(),i=null,e&&t("error"===e.type?404:200,e.type)}),E.head.appendChild(r[0])},abort:function(){i&&i()}}});var Ut,Xt=[],Vt=/(=)\?(?=&|$)|\?\?/;S.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Xt.pop()||S.expando+"_"+Ct.guid++;return this[e]=!0,e}}),S.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,a=!1!==e.jsonp&&(Vt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Vt.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=m(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Vt,"$1"+r):!1!==e.jsonp&&(e.url+=(Et.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||S.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=C[r],C[r]=function(){o=arguments},n.always(function(){void 0===i?S(C).removeProp(r):C[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,Xt.push(r)),o&&m(i)&&i(o[0]),o=i=void 0}),"script"}),v.createHTMLDocument=((Ut=E.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Ut.childNodes.length),S.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(v.createHTMLDocument?((r=(t=E.implementation.createHTMLDocument("")).createElement("base")).href=E.location.href,t.head.appendChild(r)):t=E),o=!n&&[],(i=N.exec(e))?[t.createElement(i[1])]:(i=xe([e],t,o),o&&o.length&&S(o).remove(),S.merge([],i.childNodes)));var r,i,o},S.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return-1<s&&(r=yt(e.slice(s)),e=e.slice(0,s)),m(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),0<a.length&&S.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?S("<div>").append(S.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},S.expr.pseudos.animated=function(t){return S.grep(S.timers,function(e){return t===e.elem}).length},S.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l=S.css(e,"position"),c=S(e),f={};"static"===l&&(e.style.position="relative"),s=c.offset(),o=S.css(e,"top"),u=S.css(e,"left"),("absolute"===l||"fixed"===l)&&-1<(o+u).indexOf("auto")?(a=(r=c.position()).top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),m(t)&&(t=t.call(e,n,S.extend({},s))),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+i),"using"in t?t.using.call(e,f):c.css(f)}},S.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){S.offset.setOffset(this,t,e)});var e,n,r=this[0];return r?r.getClientRects().length?(e=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===S.css(r,"position"))t=r.getBoundingClientRect();else{t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;while(e&&(e===n.body||e===n.documentElement)&&"static"===S.css(e,"position"))e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=S(e).offset()).top+=S.css(e,"borderTopWidth",!0),i.left+=S.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-S.css(r,"marginTop",!0),left:t.left-i.left-S.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){var e=this.offsetParent;while(e&&"static"===S.css(e,"position"))e=e.offsetParent;return e||re})}}),S.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;S.fn[t]=function(e){return B(this,function(e,t,n){var r;if(x(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n)return r?r[i]:e[t];r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n},t,e,arguments.length)}}),S.each(["top","left"],function(e,n){S.cssHooks[n]=_e(v.pixelPosition,function(e,t){if(t)return t=Be(e,n),Pe.test(t)?S(e).position()[n]+"px":t})}),S.each({Height:"height",Width:"width"},function(a,s){S.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,o){S.fn[o]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return B(this,function(e,t,n){var r;return x(e)?0===o.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?S.css(e,t,i):S.style(e,t,n,i)},s,n?e:void 0,n)}})}),S.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){S.fn[t]=function(e){return this.on(t,e)}}),S.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),S.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){S.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var Gt=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;S.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),m(e))return r=s.call(arguments,2),(i=function(){return e.apply(t||this,r.concat(s.call(arguments)))}).guid=e.guid=e.guid||S.guid++,i},S.holdReady=function(e){e?S.readyWait++:S.ready(!0)},S.isArray=Array.isArray,S.parseJSON=JSON.parse,S.nodeName=A,S.isFunction=m,S.isWindow=x,S.camelCase=X,S.type=w,S.now=Date.now,S.isNumeric=function(e){var t=S.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},S.trim=function(e){return null==e?"":(e+"").replace(Gt,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return S});var Yt=C.jQuery,Qt=C.$;return S.noConflict=function(e){return C.$===S&&(C.$=Qt),e&&C.jQuery===S&&(C.jQuery=Yt),S},"undefined"==typeof e&&(C.jQuery=C.$=S),S});/* bignumber.js v8.0.1 https://github.com/MikeMcl/bignumber.js/LICENCE */
!function(e){"use strict";var r,L=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,B=Math.ceil,T=Math.floor,U="[BigNumber Error] ",I=U+"Number primitive has more than 15 significant digits: ",C=1e14,M=14,G=9007199254740991,F=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],k=1e7,q=1e9;function j(e){var r=0|e;return 0<e||e===r?r:r-1}function $(e){for(var r,n,t=1,i=e.length,o=e[0]+"";t<i;){for(r=e[t++]+"",n=M-r.length;n--;r="0"+r);o+=r}for(i=o.length;48===o.charCodeAt(--i););return o.slice(0,i+1||1)}function z(e,r){var n,t,i=e.c,o=r.c,s=e.s,f=r.s,u=e.e,l=r.e;if(!s||!f)return null;if(n=i&&!i[0],t=o&&!o[0],n||t)return n?t?0:-f:s;if(s!=f)return s;if(n=s<0,t=u==l,!i||!o)return t?0:!i^n?1:-1;if(!t)return l<u^n?1:-1;for(f=(u=i.length)<(l=o.length)?u:l,s=0;s<f;s++)if(i[s]!=o[s])return i[s]>o[s]^n?1:-1;return u==l?0:l<u^n?1:-1}function H(e,r,n,t){if(e<r||n<e||e!==(e<0?B(e):T(e)))throw Error(U+(t||"Argument")+("number"==typeof e?e<r||n<e?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function V(e){var r=e.c.length-1;return j(e.e/M)==r&&e.c[r]%2!=0}function W(e,r){return(1<e.length?e.charAt(0)+"."+e.slice(1):e)+(r<0?"e":"e+")+r}function X(e,r,n){var t,i;if(r<0){for(i=n+".";++r;i+=n);e=i+e}else if(++r>(t=e.length)){for(i=n,r-=t;--r;i+=n);e+=i}else r<t&&(e=e.slice(0,r)+"."+e.slice(r));return e}(r=function e(r){var v,a,h,n,l,s,f,u,c,p,t=_.prototype={constructor:_,toString:null,valueOf:null},w=new _(1),y=20,N=4,g=-7,i=21,m=-1e7,d=1e7,O=!1,o=1,b=0,E={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},A="0123456789abcdefghijklmnopqrstuvwxyz";function _(e,r){var n,t,i,o,s,f,u,l,c=this;if(!(c instanceof _))return new _(e,r);if(null==r){if(e instanceof _)return c.s=e.s,c.e=e.e,void(c.c=(e=e.c)?e.slice():e);if((f="number"==typeof e)&&0*e==0){if(c.s=1/e<0?(e=-e,-1):1,e===~~e){for(o=0,s=e;10<=s;s/=10,o++);return c.e=o,void(c.c=[e])}l=String(e)}else{if(l=String(e),!L.test(l))return h(c,l,f);c.s=45==l.charCodeAt(0)?(l=l.slice(1),-1):1}-1<(o=l.indexOf("."))&&(l=l.replace(".","")),0<(s=l.search(/e/i))?(o<0&&(o=s),o+=+l.slice(s+1),l=l.substring(0,s)):o<0&&(o=l.length)}else{if(H(r,2,A.length,"Base"),l=String(e),10==r)return x(c=new _(e instanceof _?e:l),y+c.e+1,N);if(f="number"==typeof e){if(0*e!=0)return h(c,l,f,r);if(c.s=1/e<0?(l=l.slice(1),-1):1,_.DEBUG&&15<l.replace(/^0\.0*|\./,"").length)throw Error(I+e);f=!1}else c.s=45===l.charCodeAt(0)?(l=l.slice(1),-1):1;for(n=A.slice(0,r),o=s=0,u=l.length;s<u;s++)if(n.indexOf(t=l.charAt(s))<0){if("."==t){if(o<s){o=u;continue}}else if(!i&&(l==l.toUpperCase()&&(l=l.toLowerCase())||l==l.toLowerCase()&&(l=l.toUpperCase()))){i=!0,s=-1,o=0;continue}return h(c,String(e),f,r)}-1<(o=(l=a(l,r,10,c.s)).indexOf("."))?l=l.replace(".",""):o=l.length}for(s=0;48===l.charCodeAt(s);s++);for(u=l.length;48===l.charCodeAt(--u););if(l=l.slice(s,++u)){if(u-=s,f&&_.DEBUG&&15<u&&(G<e||e!==T(e)))throw Error(I+c.s*e);if(d<(o=o-s-1))c.c=c.e=null;else if(o<m)c.c=[c.e=0];else{if(c.e=o,c.c=[],s=(o+1)%M,o<0&&(s+=M),s<u){for(s&&c.c.push(+l.slice(0,s)),u-=M;s<u;)c.c.push(+l.slice(s,s+=M));l=l.slice(s),s=M-l.length}else s-=u;for(;s--;l+="0");c.c.push(+l)}}else c.c=[c.e=0]}function S(e,r,n,t){var i,o,s,f,u;if(null==n?n=N:H(n,0,8),!e.c)return e.toString();if(i=e.c[0],s=e.e,null==r)u=$(e.c),u=1==t||2==t&&s<=g?W(u,s):X(u,s,"0");else if(o=(e=x(new _(e),r,n)).e,f=(u=$(e.c)).length,1==t||2==t&&(r<=o||o<=g)){for(;f<r;u+="0",f++);u=W(u,o)}else if(r-=s,u=X(u,o,"0"),f<o+1){if(0<--r)for(u+=".";r--;u+="0");}else if(0<(r+=o-f))for(o+1==f&&(u+=".");r--;u+="0");return e.s<0&&i?"-"+u:u}function R(e,r){for(var n,t=1,i=new _(e[0]);t<e.length;t++){if(!(n=new _(e[t])).s){i=n;break}r.call(i,n)&&(i=n)}return i}function P(e,r,n){for(var t=1,i=r.length;!r[--i];r.pop());for(i=r[0];10<=i;i/=10,t++);return(n=t+n*M-1)>d?e.c=e.e=null:e.c=n<m?[e.e=0]:(e.e=n,r),e}function x(e,r,n,t){var i,o,s,f,u,l,c,a=e.c,h=F;if(a){e:{for(i=1,f=a[0];10<=f;f/=10,i++);if((o=r-i)<0)o+=M,s=r,c=(u=a[l=0])/h[i-s-1]%10|0;else if((l=B((o+1)/M))>=a.length){if(!t)break e;for(;a.length<=l;a.push(0));u=c=0,s=(o%=M)-M+(i=1)}else{for(u=f=a[l],i=1;10<=f;f/=10,i++);c=(s=(o%=M)-M+i)<0?0:u/h[i-s-1]%10|0}if(t=t||r<0||null!=a[l+1]||(s<0?u:u%h[i-s-1]),t=n<4?(c||t)&&(0==n||n==(e.s<0?3:2)):5<c||5==c&&(4==n||t||6==n&&(0<o?0<s?u/h[i-s]:0:a[l-1])%10&1||n==(e.s<0?8:7)),r<1||!a[0])return a.length=0,t?(r-=e.e+1,a[0]=h[(M-r%M)%M],e.e=-r||0):a[0]=e.e=0,e;if(0==o?(a.length=l,f=1,l--):(a.length=l+1,f=h[M-o],a[l]=0<s?T(u/h[i-s]%h[s])*f:0),t)for(;;){if(0==l){for(o=1,s=a[0];10<=s;s/=10,o++);for(s=a[0]+=f,f=1;10<=s;s/=10,f++);o!=f&&(e.e++,a[0]==C&&(a[0]=1));break}if(a[l]+=f,a[l]!=C)break;a[l--]=0,f=1}for(o=a.length;0===a[--o];a.pop());}e.e>d?e.c=e.e=null:e.e<m&&(e.c=[e.e=0])}return e}function D(e){var r,n=e.e;return null===n?e.toString():(r=$(e.c),r=n<=g||i<=n?W(r,n):X(r,n,"0"),e.s<0?"-"+r:r)}return _.clone=e,_.ROUND_UP=0,_.ROUND_DOWN=1,_.ROUND_CEIL=2,_.ROUND_FLOOR=3,_.ROUND_HALF_UP=4,_.ROUND_HALF_DOWN=5,_.ROUND_HALF_EVEN=6,_.ROUND_HALF_CEIL=7,_.ROUND_HALF_FLOOR=8,_.EUCLID=9,_.config=_.set=function(e){var r,n;if(null!=e){if("object"!=typeof e)throw Error(U+"Object expected: "+e);if(e.hasOwnProperty(r="DECIMAL_PLACES")&&(H(n=e[r],0,q,r),y=n),e.hasOwnProperty(r="ROUNDING_MODE")&&(H(n=e[r],0,8,r),N=n),e.hasOwnProperty(r="EXPONENTIAL_AT")&&((n=e[r])&&n.pop?(H(n[0],-q,0,r),H(n[1],0,q,r),g=n[0],i=n[1]):(H(n,-q,q,r),g=-(i=n<0?-n:n))),e.hasOwnProperty(r="RANGE"))if((n=e[r])&&n.pop)H(n[0],-q,-1,r),H(n[1],1,q,r),m=n[0],d=n[1];else{if(H(n,-q,q,r),!n)throw Error(U+r+" cannot be zero: "+n);m=-(d=n<0?-n:n)}if(e.hasOwnProperty(r="CRYPTO")){if((n=e[r])!==!!n)throw Error(U+r+" not true or false: "+n);if(n){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw O=!n,Error(U+"crypto unavailable");O=n}else O=n}if(e.hasOwnProperty(r="MODULO_MODE")&&(H(n=e[r],0,9,r),o=n),e.hasOwnProperty(r="POW_PRECISION")&&(H(n=e[r],0,q,r),b=n),e.hasOwnProperty(r="FORMAT")){if("object"!=typeof(n=e[r]))throw Error(U+r+" not an object: "+n);E=n}if(e.hasOwnProperty(r="ALPHABET")){if("string"!=typeof(n=e[r])||/^.$|[+-.\s]|(.).*\1/.test(n))throw Error(U+r+" invalid: "+n);A=n}}return{DECIMAL_PLACES:y,ROUNDING_MODE:N,EXPONENTIAL_AT:[g,i],RANGE:[m,d],CRYPTO:O,MODULO_MODE:o,POW_PRECISION:b,FORMAT:E,ALPHABET:A}},_.isBigNumber=function(e){return e instanceof _||e&&!0===e._isBigNumber||!1},_.maximum=_.max=function(){return R(arguments,t.lt)},_.minimum=_.min=function(){return R(arguments,t.gt)},_.random=(n=9007199254740992,l=Math.random()*n&2097151?function(){return T(Math.random()*n)}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var r,n,t,i,o,s=0,f=[],u=new _(w);if(null==e?e=y:H(e,0,q),i=B(e/M),O)if(crypto.getRandomValues){for(r=crypto.getRandomValues(new Uint32Array(i*=2));s<i;)9e15<=(o=131072*r[s]+(r[s+1]>>>11))?(n=crypto.getRandomValues(new Uint32Array(2)),r[s]=n[0],r[s+1]=n[1]):(f.push(o%1e14),s+=2);s=i/2}else{if(!crypto.randomBytes)throw O=!1,Error(U+"crypto unavailable");for(r=crypto.randomBytes(i*=7);s<i;)9e15<=(o=281474976710656*(31&r[s])+1099511627776*r[s+1]+4294967296*r[s+2]+16777216*r[s+3]+(r[s+4]<<16)+(r[s+5]<<8)+r[s+6])?crypto.randomBytes(7).copy(r,s):(f.push(o%1e14),s+=7);s=i/7}if(!O)for(;s<i;)(o=l())<9e15&&(f[s++]=o%1e14);for(i=f[--s],e%=M,i&&e&&(o=F[M-e],f[s]=T(i/o)*o);0===f[s];f.pop(),s--);if(s<0)f=[t=0];else{for(t=-1;0===f[0];f.splice(0,1),t-=M);for(s=1,o=f[0];10<=o;o/=10,s++);s<M&&(t-=M-s)}return u.e=t,u.c=f,u}),_.sum=function(){for(var e=1,r=arguments,n=new _(r[0]);e<r.length;)n=n.plus(r[e++]);return n},a=function(){var m="0123456789";function d(e,r,n,t){for(var i,o,s=[0],f=0,u=e.length;f<u;){for(o=s.length;o--;s[o]*=r);for(s[0]+=t.indexOf(e.charAt(f++)),i=0;i<s.length;i++)s[i]>n-1&&(null==s[i+1]&&(s[i+1]=0),s[i+1]+=s[i]/n|0,s[i]%=n)}return s.reverse()}return function(e,r,n,t,i){var o,s,f,u,l,c,a,h,p=e.indexOf("."),g=y,w=N;for(0<=p&&(u=b,b=0,e=e.replace(".",""),c=(h=new _(r)).pow(e.length-p),b=u,h.c=d(X($(c.c),c.e,"0"),10,n,m),h.e=h.c.length),f=u=(a=d(e,r,n,i?(o=A,m):(o=m,A))).length;0==a[--u];a.pop());if(!a[0])return o.charAt(0);if(p<0?--f:(c.c=a,c.e=f,c.s=t,a=(c=v(c,h,g,w,n)).c,l=c.r,f=c.e),p=a[s=f+g+1],u=n/2,l=l||s<0||null!=a[s+1],l=w<4?(null!=p||l)&&(0==w||w==(c.s<0?3:2)):u<p||p==u&&(4==w||l||6==w&&1&a[s-1]||w==(c.s<0?8:7)),s<1||!a[0])e=l?X(o.charAt(1),-g,o.charAt(0)):o.charAt(0);else{if(a.length=s,l)for(--n;++a[--s]>n;)a[s]=0,s||(++f,a=[1].concat(a));for(u=a.length;!a[--u];);for(p=0,e="";p<=u;e+=o.charAt(a[p++]));e=X(e,f,o.charAt(0))}return e}}(),v=function(){function S(e,r,n){var t,i,o,s,f=0,u=e.length,l=r%k,c=r/k|0;for(e=e.slice();u--;)f=((i=l*(o=e[u]%k)+(t=c*o+(s=e[u]/k|0)*l)%k*k+f)/n|0)+(t/k|0)+c*s,e[u]=i%n;return f&&(e=[f].concat(e)),e}function R(e,r,n,t){var i,o;if(n!=t)o=t<n?1:-1;else for(i=o=0;i<n;i++)if(e[i]!=r[i]){o=e[i]>r[i]?1:-1;break}return o}function P(e,r,n,t){for(var i=0;n--;)e[n]-=i,i=e[n]<r[n]?1:0,e[n]=i*t+e[n]-r[n];for(;!e[0]&&1<e.length;e.splice(0,1));}return function(e,r,n,t,i){var o,s,f,u,l,c,a,h,p,g,w,m,d,v,y,N,O,b=e.s==r.s?1:-1,E=e.c,A=r.c;if(!(E&&E[0]&&A&&A[0]))return new _(e.s&&r.s&&(E?!A||E[0]!=A[0]:A)?E&&0==E[0]||!A?0*b:b/0:NaN);for(p=(h=new _(b)).c=[],b=n+(s=e.e-r.e)+1,i||(i=C,s=j(e.e/M)-j(r.e/M),b=b/M|0),f=0;A[f]==(E[f]||0);f++);if(A[f]>(E[f]||0)&&s--,b<0)p.push(1),u=!0;else{for(v=E.length,N=A.length,b+=2,1<(l=T(i/(A[f=0]+1)))&&(A=S(A,l,i),E=S(E,l,i),N=A.length,v=E.length),d=N,w=(g=E.slice(0,N)).length;w<N;g[w++]=0);O=A.slice(),O=[0].concat(O),y=A[0],A[1]>=i/2&&y++;do{if(l=0,(o=R(A,g,N,w))<0){if(m=g[0],N!=w&&(m=m*i+(g[1]||0)),1<(l=T(m/y)))for(i<=l&&(l=i-1),a=(c=S(A,l,i)).length,w=g.length;1==R(c,g,a,w);)l--,P(c,N<a?O:A,a,i),a=c.length,o=1;else 0==l&&(o=l=1),a=(c=A.slice()).length;if(a<w&&(c=[0].concat(c)),P(g,c,w,i),w=g.length,-1==o)for(;R(A,g,N,w)<1;)l++,P(g,N<w?O:A,w,i),w=g.length}else 0===o&&(l++,g=[0]);p[f++]=l,g[0]?g[w++]=E[d]||0:(g=[E[d]],w=1)}while((d++<v||null!=g[0])&&b--);u=null!=g[0],p[0]||p.splice(0,1)}if(i==C){for(f=1,b=p[0];10<=b;b/=10,f++);x(h,n+(h.e=f+s*M-1)+1,t,u)}else h.e=s,h.r=+u;return h}}(),s=/^(-?)0([xbo])(?=\w[\w.]*$)/i,f=/^([^.]+)\.$/,u=/^\.([^.]+)$/,c=/^-?(Infinity|NaN)$/,p=/^\s*\+(?=[\w.])|^\s+|\s+$/g,h=function(e,r,n,t){var i,o=n?r:r.replace(p,"");if(c.test(o))e.s=isNaN(o)?null:o<0?-1:1,e.c=e.e=null;else{if(!n&&(o=o.replace(s,function(e,r,n){return i="x"==(n=n.toLowerCase())?16:"b"==n?2:8,t&&t!=i?e:r}),t&&(i=t,o=o.replace(f,"$1").replace(u,"0.$1")),r!=o))return new _(o,i);if(_.DEBUG)throw Error(U+"Not a"+(t?" base "+t:"")+" number: "+r);e.c=e.e=e.s=null}},t.absoluteValue=t.abs=function(){var e=new _(this);return e.s<0&&(e.s=1),e},t.comparedTo=function(e,r){return z(this,new _(e,r))},t.decimalPlaces=t.dp=function(e,r){var n,t,i;if(null!=e)return H(e,0,q),null==r?r=N:H(r,0,8),x(new _(this),e+this.e+1,r);if(!(n=this.c))return null;if(t=((i=n.length-1)-j(this.e/M))*M,i=n[i])for(;i%10==0;i/=10,t--);return t<0&&(t=0),t},t.dividedBy=t.div=function(e,r){return v(this,new _(e,r),y,N)},t.dividedToIntegerBy=t.idiv=function(e,r){return v(this,new _(e,r),0,1)},t.exponentiatedBy=t.pow=function(e,r){var n,t,i,o,s,f,u,l,c=this;if((e=new _(e)).c&&!e.isInteger())throw Error(U+"Exponent not an integer: "+D(e));if(null!=r&&(r=new _(r)),s=14<e.e,!c.c||!c.c[0]||1==c.c[0]&&!c.e&&1==c.c.length||!e.c||!e.c[0])return l=new _(Math.pow(+D(c),s?2-V(e):+D(e))),r?l.mod(r):l;if(f=e.s<0,r){if(r.c?!r.c[0]:!r.s)return new _(NaN);(t=!f&&c.isInteger()&&r.isInteger())&&(c=c.mod(r))}else{if(9<e.e&&(0<c.e||c.e<-1||(0==c.e?1<c.c[0]||s&&24e7<=c.c[1]:c.c[0]<8e13||s&&c.c[0]<=9999975e7)))return o=c.s<0&&V(e)?-0:0,-1<c.e&&(o=1/o),new _(f?1/o:o);b&&(o=B(b/M+2))}for(u=s?(n=new _(.5),f&&(e.s=1),V(e)):(i=Math.abs(+D(e)))%2,l=new _(w);;){if(u){if(!(l=l.times(c)).c)break;o?l.c.length>o&&(l.c.length=o):t&&(l=l.mod(r))}if(i){if(0===(i=T(i/2)))break;u=i%2}else if(x(e=e.times(n),e.e+1,1),14<e.e)u=V(e);else{if(0==(i=+D(e)))break;u=i%2}c=c.times(c),o?c.c&&c.c.length>o&&(c.c.length=o):t&&(c=c.mod(r))}return t?l:(f&&(l=w.div(l)),r?l.mod(r):o?x(l,b,N,void 0):l)},t.integerValue=function(e){var r=new _(this);return null==e?e=N:H(e,0,8),x(r,r.e+1,e)},t.isEqualTo=t.eq=function(e,r){return 0===z(this,new _(e,r))},t.isFinite=function(){return!!this.c},t.isGreaterThan=t.gt=function(e,r){return 0<z(this,new _(e,r))},t.isGreaterThanOrEqualTo=t.gte=function(e,r){return 1===(r=z(this,new _(e,r)))||0===r},t.isInteger=function(){return!!this.c&&j(this.e/M)>this.c.length-2},t.isLessThan=t.lt=function(e,r){return z(this,new _(e,r))<0},t.isLessThanOrEqualTo=t.lte=function(e,r){return-1===(r=z(this,new _(e,r)))||0===r},t.isNaN=function(){return!this.s},t.isNegative=function(){return this.s<0},t.isPositive=function(){return 0<this.s},t.isZero=function(){return!!this.c&&0==this.c[0]},t.minus=function(e,r){var n,t,i,o,s=this,f=s.s;if(r=(e=new _(e,r)).s,!f||!r)return new _(NaN);if(f!=r)return e.s=-r,s.plus(e);var u=s.e/M,l=e.e/M,c=s.c,a=e.c;if(!u||!l){if(!c||!a)return c?(e.s=-r,e):new _(a?s:NaN);if(!c[0]||!a[0])return a[0]?(e.s=-r,e):new _(c[0]?s:3==N?-0:0)}if(u=j(u),l=j(l),c=c.slice(),f=u-l){for((i=(o=f<0)?(f=-f,c):(l=u,a)).reverse(),r=f;r--;i.push(0));i.reverse()}else for(t=(o=(f=c.length)<(r=a.length))?f:r,f=r=0;r<t;r++)if(c[r]!=a[r]){o=c[r]<a[r];break}if(o&&(i=c,c=a,a=i,e.s=-e.s),0<(r=(t=a.length)-(n=c.length)))for(;r--;c[n++]=0);for(r=C-1;f<t;){if(c[--t]<a[t]){for(n=t;n&&!c[--n];c[n]=r);--c[n],c[t]+=C}c[t]-=a[t]}for(;0==c[0];c.splice(0,1),--l);return c[0]?P(e,c,l):(e.s=3==N?-1:1,e.c=[e.e=0],e)},t.modulo=t.mod=function(e,r){var n,t,i=this;return e=new _(e,r),!i.c||!e.s||e.c&&!e.c[0]?new _(NaN):!e.c||i.c&&!i.c[0]?new _(i):(9==o?(t=e.s,e.s=1,n=v(i,e,0,3),e.s=t,n.s*=t):n=v(i,e,0,o),(e=i.minus(n.times(e))).c[0]||1!=o||(e.s=i.s),e)},t.multipliedBy=t.times=function(e,r){var n,t,i,o,s,f,u,l,c,a,h,p,g,w,m,d=this,v=d.c,y=(e=new _(e,r)).c;if(!(v&&y&&v[0]&&y[0]))return!d.s||!e.s||v&&!v[0]&&!y||y&&!y[0]&&!v?e.c=e.e=e.s=null:(e.s*=d.s,v&&y?(e.c=[0],e.e=0):e.c=e.e=null),e;for(t=j(d.e/M)+j(e.e/M),e.s*=d.s,(u=v.length)<(a=y.length)&&(g=v,v=y,y=g,i=u,u=a,a=i),i=u+a,g=[];i--;g.push(0));for(w=C,m=k,i=a;0<=--i;){for(n=0,h=y[i]%m,p=y[i]/m|0,o=i+(s=u);i<o;)n=((l=h*(l=v[--s]%m)+(f=p*l+(c=v[s]/m|0)*h)%m*m+g[o]+n)/w|0)+(f/m|0)+p*c,g[o--]=l%w;g[o]=n}return n?++t:g.splice(0,1),P(e,g,t)},t.negated=function(){var e=new _(this);return e.s=-e.s||null,e},t.plus=function(e,r){var n,t=this,i=t.s;if(r=(e=new _(e,r)).s,!i||!r)return new _(NaN);if(i!=r)return e.s=-r,t.minus(e);var o=t.e/M,s=e.e/M,f=t.c,u=e.c;if(!o||!s){if(!f||!u)return new _(i/0);if(!f[0]||!u[0])return u[0]?e:new _(f[0]?t:0*i)}if(o=j(o),s=j(s),f=f.slice(),i=o-s){for((n=0<i?(s=o,u):(i=-i,f)).reverse();i--;n.push(0));n.reverse()}for((i=f.length)-(r=u.length)<0&&(n=u,u=f,f=n,r=i),i=0;r;)i=(f[--r]=f[r]+u[r]+i)/C|0,f[r]=C===f[r]?0:f[r]%C;return i&&(f=[i].concat(f),++s),P(e,f,s)},t.precision=t.sd=function(e,r){var n,t,i;if(null!=e&&e!==!!e)return H(e,1,q),null==r?r=N:H(r,0,8),x(new _(this),e,r);if(!(n=this.c))return null;if(t=(i=n.length-1)*M+1,i=n[i]){for(;i%10==0;i/=10,t--);for(i=n[0];10<=i;i/=10,t++);}return e&&this.e+1>t&&(t=this.e+1),t},t.shiftedBy=function(e){return H(e,-G,G),this.times("1e"+e)},t.squareRoot=t.sqrt=function(){var e,r,n,t,i,o=this,s=o.c,f=o.s,u=o.e,l=y+4,c=new _("0.5");if(1!==f||!s||!s[0])return new _(!f||f<0&&(!s||s[0])?NaN:s?o:1/0);if((n=0==(f=Math.sqrt(+D(o)))||f==1/0?(((r=$(s)).length+u)%2==0&&(r+="0"),f=Math.sqrt(+r),u=j((u+1)/2)-(u<0||u%2),new _(r=f==1/0?"1e"+u:(r=f.toExponential()).slice(0,r.indexOf("e")+1)+u)):new _(f+"")).c[0])for((f=(u=n.e)+l)<3&&(f=0);;)if(i=n,n=c.times(i.plus(v(o,i,l,1))),$(i.c).slice(0,f)===(r=$(n.c)).slice(0,f)){if(n.e<u&&--f,"9999"!=(r=r.slice(f-3,f+1))&&(t||"4999"!=r)){+r&&(+r.slice(1)||"5"!=r.charAt(0))||(x(n,n.e+y+2,1),e=!n.times(n).eq(o));break}if(!t&&(x(i,i.e+y+2,0),i.times(i).eq(o))){n=i;break}l+=4,f+=4,t=1}return x(n,n.e+y+1,N,e)},t.toExponential=function(e,r){return null!=e&&(H(e,0,q),e++),S(this,e,r,1)},t.toFixed=function(e,r){return null!=e&&(H(e,0,q),e=e+this.e+1),S(this,e,r)},t.toFormat=function(e,r,n){var t;if(null==n)null!=e&&r&&"object"==typeof r?(n=r,r=null):e&&"object"==typeof e?(n=e,e=r=null):n=E;else if("object"!=typeof n)throw Error(U+"Argument not an object: "+n);if(t=this.toFixed(e,r),this.c){var i,o=t.split("."),s=+n.groupSize,f=+n.secondaryGroupSize,u=n.groupSeparator||"",l=o[0],c=o[1],a=this.s<0,h=a?l.slice(1):l,p=h.length;if(f&&(i=s,s=f,p-=f=i),0<s&&0<p){for(i=p%s||s,l=h.substr(0,i);i<p;i+=s)l+=u+h.substr(i,s);0<f&&(l+=u+h.slice(i)),a&&(l="-"+l)}t=c?l+(n.decimalSeparator||"")+((f=+n.fractionGroupSize)?c.replace(new RegExp("\\d{"+f+"}\\B","g"),"$&"+(n.fractionGroupSeparator||"")):c):l}return(n.prefix||"")+t+(n.suffix||"")},t.toFraction=function(e){var r,n,t,i,o,s,f,u,l,c,a,h,p=this,g=p.c;if(null!=e&&(!(f=new _(e)).isInteger()&&(f.c||1!==f.s)||f.lt(w)))throw Error(U+"Argument "+(f.isInteger()?"out of range: ":"not an integer: ")+D(f));if(!g)return new _(p);for(r=new _(w),l=n=new _(w),t=u=new _(w),h=$(g),o=r.e=h.length-p.e-1,r.c[0]=F[(s=o%M)<0?M+s:s],e=!e||0<f.comparedTo(r)?0<o?r:l:f,s=d,d=1/0,f=new _(h),u.c[0]=0;c=v(f,r,0,1),1!=(i=n.plus(c.times(t))).comparedTo(e);)n=t,t=i,l=u.plus(c.times(i=l)),u=i,r=f.minus(c.times(i=r)),f=i;return i=v(e.minus(n),t,0,1),u=u.plus(i.times(l)),n=n.plus(i.times(t)),u.s=l.s=p.s,a=v(l,t,o*=2,N).minus(p).abs().comparedTo(v(u,n,o,N).minus(p).abs())<1?[l,t]:[u,n],d=s,a},t.toNumber=function(){return+D(this)},t.toPrecision=function(e,r){return null!=e&&H(e,1,q),S(this,e,r,2)},t.toString=function(e){var r,n=this.s,t=this.e;return null===t?n?(r="Infinity",n<0&&(r="-"+r)):r="NaN":(r=$(this.c),r=null==e?t<=g||i<=t?W(r,t):X(r,t,"0"):(H(e,2,A.length,"Base"),a(X(r,t,"0"),10,e,n,!0)),n<0&&this.c[0]&&(r="-"+r)),r},t.valueOf=t.toJSON=function(){return D(this)},t._isBigNumber=!0,"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator&&(t[Symbol.toStringTag]="BigNumber",t[Symbol.for("nodejs.util.inspect.custom")]=t.valueOf),null!=r&&_.set(r),_}()).default=r.BigNumber=r,"function"==typeof define&&define.amd?define(function(){return r}):"undefined"!=typeof module&&module.exports?module.exports=r:(e||(e="undefined"!=typeof self&&self?self:window),e.BigNumber=r)}(this);

function calcPayAmount(action, currency, payment, amount) {
	currencyInfo = globalCurrencies[currency];
	payCurrencyInfo = globalCurrencies[globalPayments[payment]['currency']];
	config = currencyInfo['config'][action+'_payments'][payment];
	if (payCurrencyInfo['currency'] == currencyInfo['currency'] || ( currencyInfo['usd_equal'] && payCurrencyInfo['usd_equal']) ) {
		rate = 1;
		payAmount = BigNumber(amount).dividedBy(rate).toFixed(payCurrencyInfo['round_payment']);
	} else if (currencyInfo['usd_equal']) {
		rate = globalRates[payCurrencyInfo['currency']];
		payAmount = BigNumber(amount).dividedBy(rate).toFixed(payCurrencyInfo['round_payment']);
	} else if (payCurrencyInfo['usd_equal']) {
		rate = globalRates[currencyInfo['currency']];
		payAmount = BigNumber(amount).multipliedBy(rate).toFixed(payCurrencyInfo['round_payment']);
	} else {
		rate = globalRates[currencyInfo['currency']];
		amountInUsd = BigNumber(amount).multipliedBy(rate).toFixed(8);
		rate = globalRates[payCurrencyInfo['currency']];
		payAmount = BigNumber(amountInUsd).dividedBy(rate).toFixed(payCurrencyInfo['round_payment']);
	}
	percentFeeValue = BigNumber(config['fee_percent']).dividedBy(100).toFixed(6);
	percentFee = BigNumber(payAmount).multipliedBy(percentFeeValue).toFixed(payCurrencyInfo['round_payment']);
	totalFee = BigNumber('0').plus(percentFee);
	totalFee = BigNumber(totalFee).plus(config['fee_amount']).toFixed(payCurrencyInfo['round_payment']);
	if (action == 'withdraw') {
		payAmount = BigNumber(payAmount).minus(config['fee_amount']).toFixed(payCurrencyInfo['round_payment']);
		payAmount = BigNumber(payAmount).minus(percentFee).toFixed(payCurrencyInfo['round_payment']);
	} else {
		payAmount = BigNumber(payAmount).plus(config['fee_amount']).toFixed(payCurrencyInfo['round_payment']);
		payAmount = BigNumber(payAmount).plus(percentFee).toFixed(payCurrencyInfo['round_payment']);
	}
	return {
		'pay_amount' : parseFloat(payAmount),
		'total_fee' : parseFloat(totalFee)
	};
}

function convert(n) {
        var sign = +n < 0 ? "-" : "",
            toStr = n.toString();
        if (!/e/i.test(toStr)) {
            return n;
        }
        var [lead, decimal, pow] = n.toString()
        .replace(/^-/, "")
        .replace(/^([0-9]+)(e.*)/, "$1.$2")
        .split(/e|\./);
        return +pow < 0
        ? sign + "0." + "0".repeat(Math.max(Math.abs(pow) - 1 || 0, 0)) + lead + decimal
        : sign + lead + (+pow >= decimal.length ? (decimal + "0".repeat(Math.max(+pow - decimal.length || 0, 0))) : (decimal.slice(0, +pow) + "." + decimal.slice(+pow)))
    }

function calcInvestReturn(currency, amount, plan, fixPrincipal=0, fixEarnings=0) {
        planInfo = globalPlans[plan];
        currencyInfo = globalCurrencies[currency];
        amount = BigNumber(amount).toFixed(currencyInfo['round_payment']);
        highTotalReturn = 0;
        highPercentInfo = null;
        highPercentIndex = null;
        if (planInfo) {
            $.each(planInfo['percents_array'], function(index, value) {
                fit = false;
                if (currencyInfo['currency'] == 'usd') {
                    if (BigNumber(amount).isGreaterThanOrEqualTo(value['min_usd']) &&
                    (
                    (BigNumber(amount).isLessThanOrEqualTo(value['max_usd']) && BigNumber(value['max_usd']).isGreaterThan(0))
                    ||
                    (BigNumber(value['max_usd']).isEqualTo(0))
                    )
                    ) {
                        fit = true;
                    }
                } else {
                    if (BigNumber(amount).isGreaterThanOrEqualTo(value['min_max']['min_'+currency]) &&
                    (

                    (BigNumber(amount).isLessThanOrEqualTo(value['min_max']['max_'+currency ]) && BigNumber(value['min_max']['max_'+currency ]).isGreaterThan(0))
                    ||
                    (BigNumber(value['min_max']['max_'+currency ]).isEqualTo(0))

                    )

                    ) {
                        fit = true;
                    }
                }
                if (fit &&
                (
                (BigNumber(value.max_total_return).isGreaterThan(highTotalReturn) && BigNumber(value.max_total_return).isGreaterThan(0))
                || ( (BigNumber(value.max_total_return).isEqualTo(0) || value.max_total_return == null )  && BigNumber(value.max_percent).isGreaterThan(highTotalReturn))
                )
                ) {

                    if (BigNumber(value.max_total_return).isGreaterThan(0))
                        highTotalReturn = value.max_total_return;
                    else
                        highTotalReturn = value.max_percent;

                    highPercentInfo = value;
                    highPercentIndex = index;
                }
            });
        }
        max_percent = 0;
        max_total_return = 0;
        max_percent_amount = 0;
        max_total_return_amount = 0;
        max_net_return = 0;
        max_net_return_amount = 0;
        max_percent_amount_text = 'N/A';
        max_total_return_amount_text = 'N/A';
        max_net_return_amount_text = 'N/A';
        percent_id = null;
        calculated = false;

        if (currencyInfo.usd_equal) {
            fixPrincipal = 0;
            fixEarnings = 0;
        }

        fixPrincipal = fixPrincipal.toString();
        fixEarnings = fixEarnings.toString();

        if (highPercentInfo) {
            max_percent = highPercentInfo['max_percent'];
            max_total_return = highPercentInfo['max_total_return'];
            max_net_return = highPercentInfo['max_net_return'];
            max_percent_amount = BigNumber(amount).multipliedBy(BigNumber(max_percent).dividedBy(100)).toFixed(currencyInfo['round']);
            max_total_return_amount = BigNumber(amount).multipliedBy(BigNumber(max_total_return).dividedBy(100)).toFixed(currencyInfo['round']);
            max_net_return_amount = BigNumber(max_total_return_amount).minus(amount).toFixed(currencyInfo['round']);
            max_percent_amount_text = parseFloat(max_percent_amount).toString() + ' ' + currencyInfo['currency'].toUpperCase();
            max_total_return_amount_text = parseFloat(max_total_return_amount).toString() + ' ' + currencyInfo['currency'].toUpperCase();
            max_net_return_amount_text = parseFloat(max_net_return_amount).toString() + ' ' + currencyInfo['currency'].toUpperCase();
            calculated = true;
            percent_id = highPercentIndex;
        }

        array = [];
        array['max_percent'] = max_percent;
        array['max_total_return'] = max_total_return;
        array['max_net_return'] = max_net_return;
        array['max_percent_amount'] = max_percent_amount;
        array['max_total_return_amount'] = max_total_return_amount;
        array['max_net_return_amount'] = max_net_return_amount;
        array['max_percent_amount_text'] = max_percent_amount_text;
        array['max_total_return_amount_text'] = max_total_return_amount_text;
        array['max_net_return_amount_text'] = max_net_return_amount_text;
        array['calculated'] = calculated;
        array['percent_id'] = percent_id;

        return array;
    }

function getAmount(amount) {
	if (amount == '')return 0;
	amount = amount.toString();
	amount = amount.replace(/[^.,\d]/g, '');
	amount = amount.replace(',', '.');
	amount = parseFloat(amount);
	if (isNaN(amount) || amount < 0)amount = 0;
	return amount;
}

function getCount(amount) {
	amount = amount.toString();
	amount = amount.replace(/[^.,\d]/g, '');
	amount = amount.replace(',', '.');
	amount = parseInt(amount);
	if (isNaN(amount) || amount < 0)amount = 0;
	return amount;
}

function randomIntFromInterval(min, max) {
    return Math.floor(Math.random() * (max - min + 1) + min)
}

function isJson(str) {
    try {
        JSON.parse(str);
    } catch (e) {
        return false;
    }
    return true;
}

function resetCaptcha(form = null) {


if (form) {
	hcaptcha.reset(form.find('.h-captcha iframe').data('hcaptcha-widget-id'));
} else {
	$('.h-captcha').each(function() {
		hcaptcha.reset($(this).find('iframe').data('hcaptcha-widget-id'));
	})
}



}

globalCurrencies = {"usdt":{"title":"USDT","currency":"usdt","usd_equal":true,"only_payments":null,"config":{"up_payments":{"tether_bep-20_usdt":{"fee_percent":"0.00","fee_amount":"0.00"},"tether_erc-20_usdt":{"fee_percent":"0.00","fee_amount":"0.00"},"tether_trc-20_usdt":{"fee_percent":"0.00","fee_amount":"0.00"}},"withdraw_payments":{"tether_bep-20_usdt":{"fee_percent":"0","fee_amount":"0.11"},"tether_erc-20_usdt":{"fee_percent":"0","fee_amount":"0.24"},"tether_trc-20_usdt":{"fee_percent":"0.00","fee_amount":"4"}},"data":{"icon":"usdt"},"up_min":"10","up_max":"1000000000","withdraw_min":"1","withdraw_max":"1000000000"},"round_payment":2,"round":8},"btc":{"title":"BTC","currency":"btc","usd_equal":false,"only_payments":null,"config":{"up_payments":{"bitcoin_main_btc":{"fee_percent":"0.00","fee_amount":"0.00000000"},"bitcoin_bep-20_btcb":{"fee_percent":"0.00","fee_amount":"0.00000000"}},"withdraw_payments":{"bitcoin_main_btc":{"fee_percent":"0.00","fee_amount":"0.00002"},"bitcoin_bep-20_btcb":{"fee_percent":"0","fee_amount":"0.00000159"}},"data":{"icon":"btcb"},"up_min":"0.0005","up_max":"1000000000","withdraw_min":"0.0001","withdraw_max":"1000000000"},"round_payment":8,"round":8},"eth":{"title":"ETH","currency":"eth","usd_equal":false,"only_payments":null,"config":{"up_payments":{"ethereum_main_eth":{"fee_percent":"0.00","fee_amount":"0.00000000"},"ethereum_bep-20_eth":{"fee_percent":"0.00","fee_amount":"0.00000000"}},"withdraw_payments":{"ethereum_main_eth":{"fee_percent":"0","fee_amount":"0.00000854"},"ethereum_bep-20_eth":{"fee_percent":"0","fee_amount":"0.00004619"}},"data":{"icon":"eth"},"up_min":"0.005","up_max":"100000000","withdraw_min":"0.005","withdraw_max":"100000000"},"round_payment":8,"round":8},"bnb":{"title":"BNB","currency":"bnb","usd_equal":false,"only_payments":null,"config":{"up_payments":{"binance_bsc_bnb":{"fee_percent":"0.00","fee_amount":"0.00000000"}},"withdraw_payments":{"binance_bsc_bnb":{"fee_percent":"0","fee_amount":"0.000021"}},"data":{"icon":"wbnb"},"up_min":"0.05","up_max":"100000000","withdraw_min":"0.002","withdraw_max":"100000000"},"round_payment":8,"round":8},"sol":{"title":"SOL","currency":"sol","usd_equal":false,"only_payments":null,"config":{"up_payments":{"solana_main_sol":{"fee_percent":"0.00","fee_amount":"0.00000000"}},"withdraw_payments":{"solana_main_sol":{"fee_percent":"0.00","fee_amount":"0.00010000"}},"data":{"icon":"sol"},"up_min":null,"up_max":"1000000000","withdraw_min":"0.001","withdraw_max":"100000000"},"round_payment":8,"round":8},"trx":{"title":"TRX","currency":"trx","usd_equal":false,"only_payments":null,"config":{"up_payments":{"tron_main_trx":{"fee_percent":"0.00","fee_amount":"0.0000"}},"withdraw_payments":{"tron_main_trx":{"fee_percent":"0.00","fee_amount":"1.268"}},"data":{"icon":"trx-old"},"up_min":"200","up_max":"100000000","withdraw_min":"20","withdraw_max":"100000000"},"round_payment":4,"round":6},"xrp":{"title":"XRP","currency":"xrp","usd_equal":false,"only_payments":null,"config":{"up_payments":{"ripple_main_xrp":{"fee_percent":"0.00","fee_amount":"0.000000"}},"withdraw_payments":{"ripple_main_xrp":{"fee_percent":"0.00","fee_amount":"0.00002"}},"data":{"icon":"xrpup"},"up_min":"20","up_max":"100000000","withdraw_min":"2","withdraw_max":"100000000"},"round_payment":6,"round":6}};
globalPayments = {"tether_bep-20_usdt":{"name":"Tether BEP20","title":"Tether BEP20","currency":"usdt","need_tag":null,"placeholder":"0xYourTetherBEP20Wallet","placeholder_tag":null,"tag_is_memo":null,"network":"BSC","confirmations":10,"config":{"instant_withdraw":false},"data":{"icon":""}},"tether_erc-20_usdt":{"name":"Tether ERC20","title":"Tether ERC20","currency":"usdt","need_tag":null,"placeholder":"0xYourTetherERC20Wallet","placeholder_tag":null,"tag_is_memo":null,"network":"ERC-20","confirmations":10,"config":{"instant_withdraw":false},"data":{"icon":""}},"tether_trc-20_usdt":{"name":"Tether TRC20","title":"Tether TRC20","currency":"usdt","need_tag":null,"placeholder":"TYourTetherTRC20Wallet","placeholder_tag":null,"tag_is_memo":null,"network":"TRC-20","confirmations":10,"config":{"instant_withdraw":false},"data":{"icon":""}},"bitcoin_main_btc":{"name":"Bitcoin BTC","title":"Bitcoin BTC","currency":"btc","need_tag":null,"placeholder":"YourBitcoinWallet","placeholder_tag":null,"tag_is_memo":null,"network":"BTC","confirmations":1,"config":{"instant_withdraw":false},"data":{"icon":""}},"bitcoin_bep-20_btcb":{"name":"Bitcoin BEP20","title":"Bitcoin BEP20","currency":"btc","need_tag":null,"placeholder":"0xYourBitcoinBEP20Wallet","placeholder_tag":null,"tag_is_memo":null,"network":"BEP-20","confirmations":10,"config":{"instant_withdraw":false},"data":{"icon":""}},"ethereum_main_eth":{"name":"Ethereum ETH","title":"Ethereum ETH","currency":"eth","need_tag":null,"placeholder":"0xYourEthereumWallet","placeholder_tag":null,"tag_is_memo":null,"network":"ETH","confirmations":10,"config":{"instant_withdraw":false},"data":{"icon":""}},"ethereum_bep-20_eth":{"name":"Ethereum BEP20","title":"Ethereum BEP20","currency":"eth","need_tag":null,"placeholder":"0xYourEthereumBEP20Wallet","placeholder_tag":null,"tag_is_memo":null,"network":"BEP-20","confirmations":10,"config":{"instant_withdraw":false},"data":{"icon":""}},"binance_bsc_bnb":{"name":"Binance Coin BSC","title":"Binance Coin BSC","currency":"bnb","need_tag":null,"placeholder":"0xYourBinanceCoinBSCWallet","placeholder_tag":null,"tag_is_memo":null,"network":"BSC","confirmations":10,"config":{"instant_withdraw":false},"data":{"icon":""}},"solana_main_sol":{"name":"Solana SOL","title":"Solana SOL","currency":"sol","need_tag":null,"placeholder":"YourSolanaWallet","placeholder_tag":null,"tag_is_memo":null,"network":"SOL","confirmations":1,"config":{"instant_withdraw":false},"data":{"icon":""}},"tron_main_trx":{"name":"Tron TRX","title":"Tron TRX","currency":"trx","need_tag":null,"placeholder":"TYourTronWallet","placeholder_tag":null,"tag_is_memo":null,"network":"TRX","confirmations":10,"config":{"instant_withdraw":false},"data":{"icon":""}},"ripple_main_xrp":{"name":"Ripple XRP","title":"Ripple XRP","currency":"xrp","need_tag":true,"placeholder":"rYourRippleWallet","placeholder_tag":"123456789","tag_is_memo":null,"network":"XRP","confirmations":1,"config":{"instant_withdraw":false},"data":{"icon":""}}};
globalPlans = {"1":{"id":"1","title":"Staking","percents_array":[{"min_percent":null,"max_percent":"1","min_usd":"50","max_usd":"2499","min_max":{"min_btc":"0.0005","max_btc":"0.02499","min_eth":"0.125","max_eth":"0.6249","min_bnb":"0.07","max_bnb":"3.499","min_trx":"200","max_trx":"9999","min_xrp":"20","max_xrp":"999.9","min_usdt":"50","min_usdt_text":"50 USDT","max_usdt":"2499","max_usdt_text":"2499 USDT","min_btc_text":"0.0005 BTC","max_btc_text":"0.02499 BTC","min_eth_text":"0.125 ETH","max_eth_text":"0.6249 ETH","min_bnb_text":"0.07 BNB","max_bnb_text":"3.499 BNB","min_sol":"0.3042","min_sol_text":"0.3042 SOL","max_sol":"15.2081","max_sol_text":"15.2081 SOL","min_trx_text":"200 TRX","max_trx_text":"9999 TRX","min_xrp_text":"20 XRP","max_xrp_text":"999.9 XRP"},"min_usd_text":"$50","max_usd_text":"$2499","max_total_return":"200","min_total_return":null,"max_net_return":"100"}],"return":"0","return_fee":"0.00","type":"daily","param":"200","pay_business":"0"},"2":{"id":"2","title":"Staking","percents_array":[{"min_percent":null,"max_percent":"1.1","min_usd":"2500","max_usd":"9999","min_max":{"min_btc":"0.025","max_btc":"0.09999","min_eth":"0.625","max_eth":"2.4999","min_bnb":"3.5","max_bnb":"14.299","min_trx":"10000","max_trx":"39999","min_xrp":"1000","max_xrp":"3999.9","min_usdt":"2500","min_usdt_text":"2500 USDT","max_usdt":"9999","max_usdt_text":"9999 USDT","min_btc_text":"0.025 BTC","max_btc_text":"0.09999 BTC","min_eth_text":"0.625 ETH","max_eth_text":"2.4999 ETH","min_bnb_text":"3.5 BNB","max_bnb_text":"14.299 BNB","min_sol":"15.2142","min_sol_text":"15.2142 SOL","max_sol":"60.8507","max_sol_text":"60.8507 SOL","min_trx_text":"10000 TRX","max_trx_text":"39999 TRX","min_xrp_text":"1000 XRP","max_xrp_text":"3999.9 XRP"},"min_usd_text":"$2500","max_usd_text":"$9999","max_total_return":"200.2","min_total_return":null,"max_net_return":"100.2"}],"return":"0","return_fee":"0.00","type":"daily","param":"182","pay_business":"0"},"3":{"id":"3","title":"Staking","percents_array":[{"min_percent":null,"max_percent":"1.2","min_usd":"10000","max_usd":"24999","min_max":{"min_btc":"0.1","max_btc":"0.24999","min_eth":"2.5","max_eth":"6.2499","min_bnb":"14.3","max_bnb":"35.709","min_trx":"40000","max_trx":"99999","min_xrp":"4000","max_xrp":"9999.9","min_usdt":"10000","min_usdt_text":"10000 USDT","max_usdt":"24999","max_usdt_text":"24999 USDT","min_btc_text":"0.1 BTC","max_btc_text":"0.24999 BTC","min_eth_text":"2.5 ETH","max_eth_text":"6.2499 ETH","min_bnb_text":"14.3 BNB","max_bnb_text":"35.709 BNB","min_sol":"60.8568","min_sol_text":"60.8568 SOL","max_sol":"152.136","max_sol_text":"152.136 SOL","min_trx_text":"40000 TRX","max_trx_text":"99999 TRX","min_xrp_text":"4000 XRP","max_xrp_text":"9999.9 XRP"},"min_usd_text":"$10000","max_usd_text":"$24999","max_total_return":"200.4","min_total_return":null,"max_net_return":"100.4"}],"return":"0","return_fee":"0.00","type":"daily","param":"167","pay_business":"0"},"4":{"id":"4","title":"Staking","percents_array":[{"min_percent":null,"max_percent":"1.3","min_usd":"25000","max_usd":"49999","min_max":{"min_btc":"0.25","max_btc":"0.49999","min_eth":"6.25","max_eth":"12.4999","min_bnb":"35.71","max_bnb":"71.399","min_trx":"100000","max_trx":"199999","min_xrp":"10000","max_xrp":"19999.9","min_usdt":"25000","min_usdt_text":"25000 USDT","max_usdt":"49999","max_usdt_text":"49999 USDT","min_btc_text":"0.25 BTC","max_btc_text":"0.49999 BTC","min_eth_text":"6.25 ETH","max_eth_text":"12.4999 ETH","min_bnb_text":"35.71 BNB","max_bnb_text":"71.399 BNB","min_sol":"152.1421","min_sol_text":"152.1421 SOL","max_sol":"304.2782","max_sol_text":"304.2782 SOL","min_trx_text":"100000 TRX","max_trx_text":"199999 TRX","min_xrp_text":"10000 XRP","max_xrp_text":"19999.9 XRP"},"min_usd_text":"$25000","max_usd_text":"$49999","max_total_return":"200.2","min_total_return":null,"max_net_return":"100.2"}],"return":"0","return_fee":"0.00","type":"daily","param":"154","pay_business":"0"},"5":{"id":"5","title":"Staking","percents_array":[{"min_percent":null,"max_percent":"1.4","min_usd":"50000","max_usd":"99999","min_max":{"min_btc":"0.5","max_btc":"0.99999","min_eth":"12.5","max_eth":"24.9999","min_bnb":"71.4","max_bnb":"142.799","min_trx":"200000","max_trx":"399999","min_xrp":"20000","max_xrp":"39999.9","min_usdt":"50000","min_usdt_text":"50000 USDT","max_usdt":"99999","max_usdt_text":"99999 USDT","min_btc_text":"0.5 BTC","max_btc_text":"0.99999 BTC","min_eth_text":"12.5 ETH","max_eth_text":"24.9999 ETH","min_bnb_text":"71.4 BNB","max_bnb_text":"142.799 BNB","min_sol":"304.2843","min_sol_text":"304.2843 SOL","max_sol":"608.5625","max_sol_text":"608.5625 SOL","min_trx_text":"200000 TRX","max_trx_text":"399999 TRX","min_xrp_text":"20000 XRP","max_xrp_text":"39999.9 XRP"},"min_usd_text":"$50000","max_usd_text":"$99999","max_total_return":"200.2","min_total_return":null,"max_net_return":"100.2"}],"return":"0","return_fee":"0.00","type":"daily","param":"143","pay_business":"0"},"6":{"id":"6","title":"Staking","percents_array":[{"min_percent":null,"max_percent":"1.5","min_usd":"100000","max_usd":"100000000000","min_max":{"min_btc":"1","max_btc":"100000000000","min_eth":"25","max_eth":"100000000000","min_bnb":"142.8","max_bnb":"100000000000","min_trx":"400000","max_trx":"100000000000","min_xrp":"40000","max_xrp":"100000000000","min_usdt":"100000","min_usdt_text":"100000 USDT","max_usdt":"100000000000","max_usdt_text":"100000000000 USDT","min_btc_text":"1 BTC","max_btc_text":"100000000000 BTC","min_eth_text":"25 ETH","max_eth_text":"100000000000 ETH","min_bnb_text":"142.8 BNB","max_bnb_text":"100000000000 BNB","min_sol":"608.5686","min_sol_text":"608.5686 SOL","max_sol":"608568646.5433","max_sol_text":"608568646.5433 SOL","min_trx_text":"400000 TRX","max_trx_text":"100000000000 TRX","min_xrp_text":"40000 XRP","max_xrp_text":"100000000000 XRP"},"min_usd_text":"$100000","max_usd_text":"$100000000000","max_total_return":"201","min_total_return":null,"max_net_return":"101"}],"return":"0","return_fee":"0.00","type":"daily","param":"134","pay_business":"0"}};
globalRates = {"usdt":"1","btc":"103280.59000000","eth":"2379.50000000","bnb":"637.39000000","sol":"164.32000000","trx":"0.26180000","xrp":"2.34620000"};
globalCountries = {"af":{"id":"251","active":"1","code":"AF","name":"Afghanistan","flag":"Afghanistan","masks":"[\"+93-##-###-####\"]","placeholder":"+93","masks_placeholder":"[\"+93\"]","stat_count":"2"},"al":{"id":"253","active":"1","code":"AL","name":"Albania","flag":"Albania","masks":"[\"+355(###)###-###\"]","placeholder":"+355","masks_placeholder":"[\"+355\"]","stat_count":"0"},"dz":{"id":"254","active":"1","code":"DZ","name":"Algeria","flag":"Algeria","masks":"[\"+213-##-###-####\"]","placeholder":"+213","masks_placeholder":"[\"+213\"]","stat_count":"0"},"ad":{"id":"256","active":"1","code":"AD","name":"Andorra","flag":"Andorra","masks":"[\"+376-###-###\"]","placeholder":"+376","masks_placeholder":"[\"+376\"]","stat_count":"0"},"ao":{"id":"257","active":"1","code":"AO","name":"Angola","flag":"Angola","masks":"[\"+244(###)###-###\"]","placeholder":"+244","masks_placeholder":"[\"+244\"]","stat_count":"0"},"ag":{"id":"260","active":"1","code":"AG","name":"Antigua and barbuda","flag":"antigua-and-barbuda","masks":"[\"+1(268)###-####\"]","placeholder":"+1268","masks_placeholder":"[\"+1268\"]","stat_count":"0"},"ar":{"id":"261","active":"1","code":"AR","name":"Argentina","flag":"Argentina","masks":"[\"+54(###)###-####\"]","placeholder":"+54","masks_placeholder":"[\"+54\"]","stat_count":"0"},"am":{"id":"262","active":"1","code":"AM","name":"Armenia","flag":"Armenia","masks":"[\"+374-##-###-###\"]","placeholder":"+374","masks_placeholder":"[\"+374\"]","stat_count":"0"},"aw":{"id":"263","active":"1","code":"AW","name":"Aruba","flag":"Aruba","masks":"[\"+297-###-####\"]","placeholder":"+297","masks_placeholder":"[\"+297\"]","stat_count":"0"},"au":{"id":"264","active":"1","code":"AU","name":"Australia","flag":"Australia","masks":"[\"+61-#-####-####\"]","placeholder":"+61","masks_placeholder":"[\"+61\"]","stat_count":"0"},"at":{"id":"265","active":"1","code":"AT","name":"Austria","flag":"Austria","masks":"[\"+43(###)###-####\"]","placeholder":"+43","masks_placeholder":"[\"+43\"]","stat_count":"1"},"az":{"id":"266","active":"1","code":"AZ","name":"Azerbaijan","flag":"Azerbaijan","masks":"[\"+994-##-###-##-##\"]","placeholder":"+994","masks_placeholder":"[\"+994\"]","stat_count":"0"},"bs":{"id":"267","active":"1","code":"BS","name":"Bahamas","flag":"Bahamas","masks":"[\"+1(242)###-####\"]","placeholder":"+1242","masks_placeholder":"[\"+1242\"]","stat_count":"0"},"bh":{"id":"268","active":"1","code":"BH","name":"Bahrain","flag":"Bahrain","masks":"[\"+973-####-####\"]","placeholder":"+973","masks_placeholder":"[\"+973\"]","stat_count":"0"},"bd":{"id":"269","active":"1","code":"BD","name":"Bangladesh","flag":"Bangladesh","masks":"[\"+880-##-###-###\"]","placeholder":"+880","masks_placeholder":"[\"+880\"]","stat_count":"0"},"bb":{"id":"270","active":"1","code":"BB","name":"Barbados","flag":"Barbados","masks":"[\"+1(246)###-####\"]","placeholder":"+1246","masks_placeholder":"[\"+1246\"]","stat_count":"0"},"by":{"id":"271","active":"1","code":"BY","name":"Belarus","flag":"Belarus","masks":"[\"+375(##)###-##-##\"]","placeholder":"+375","masks_placeholder":"[\"+375\"]","stat_count":"0"},"be":{"id":"272","active":"1","code":"BE","name":"Belgium","flag":"Belgium","masks":"[\"+32(###)###-###\"]","placeholder":"+32","masks_placeholder":"[\"+32\"]","stat_count":"0"},"bz":{"id":"273","active":"1","code":"BZ","name":"Belize","flag":"Belize","masks":"[\"+501-###-####\"]","placeholder":"+501","masks_placeholder":"[\"+501\"]","stat_count":"12"},"bj":{"id":"274","active":"1","code":"BJ","name":"Benin","flag":"Benin","masks":"[\"+229-##-##-####\"]","placeholder":"+229","masks_placeholder":"[\"+229\"]","stat_count":"0"},"bt":{"id":"276","active":"1","code":"BT","name":"Bhutan","flag":"Bhutan","masks":"[\"+975-#-###-###\",\"+975-17-###-###\"]","placeholder":"+975","masks_placeholder":"[\"+975\",\"+97517\"]","stat_count":"0"},"bo":{"id":"277","active":"1","code":"BO","name":"Bolivia","flag":"Bolivia","masks":"[\"+591-#-###-####\"]","placeholder":"+591","masks_placeholder":"[\"+591\"]","stat_count":"0"},"ba":{"id":"279","active":"1","code":"BA","name":"Bosnia and Herzegovina","flag":"bosnia-and-herzegovina","masks":"[\"+387-##-####\",\"+387-##-#####\"]","placeholder":"+387","masks_placeholder":"[\"+387\"]","stat_count":"0"},"bw":{"id":"280","active":"1","code":"BW","name":"Botswana","flag":"Botswana","masks":"[\"+267-##-###-###\"]","placeholder":"+267","masks_placeholder":"[\"+267\"]","stat_count":"0"},"br":{"id":"282","active":"1","code":"BR","name":"Brazil","flag":"Brazil","masks":"[\"+55(##)9####-####\",\"+55(##)####-####\",\"+55(##)7###-####\"]","placeholder":"+55","masks_placeholder":"[\"+55\"]","stat_count":"0"},"bn":{"id":"285","active":"1","code":"BN","name":"Brunei","flag":"brunei-darussalam","masks":"[\"+673-###-####\"]","placeholder":"+673","masks_placeholder":"[\"+673\"]","stat_count":"0"},"bg":{"id":"286","active":"1","code":"BG","name":"Bulgaria","flag":"Bulgaria","masks":"[\"+359(###)###-###\"]","placeholder":"+359","masks_placeholder":"[\"+359\"]","stat_count":"0"},"bf":{"id":"287","active":"1","code":"BF","name":"Burkina Faso","flag":"burkina-faso","masks":"[\"+226-##-##-####\"]","placeholder":"+226","masks_placeholder":"[\"+226\"]","stat_count":"0"},"bi":{"id":"288","active":"1","code":"BI","name":"Burundi","flag":"Burundi","masks":"[\"+257-##-##-####\"]","placeholder":"+257","masks_placeholder":"[\"+257\"]","stat_count":"0"},"kh":{"id":"289","active":"1","code":"KH","name":"Cambodia","flag":"Cambodia","masks":"[\"+855-##-###-###\"]","placeholder":"+855","masks_placeholder":"[\"+855\"]","stat_count":"0"},"cm":{"id":"290","active":"1","code":"CM","name":"Cameroon","flag":"Cameroon","masks":"[\"+237-####-####\"]","placeholder":"+237","masks_placeholder":"[\"+237\"]","stat_count":"0"},"ca":{"id":"291","active":"1","code":"CA","name":"Canada","flag":"Canada","masks":"[\"+1(###)###-####\"]","placeholder":"+1","masks_placeholder":"[\"+1\"]","stat_count":"0"},"cv":{"id":"292","active":"1","code":"CV","name":"Cape Verde","flag":"Cape-verde","masks":"[\"+238(###)##-##\"]","placeholder":"+238","masks_placeholder":"[\"+238\"]","stat_count":"0"},"cf":{"id":"294","active":"1","code":"CF","name":"Central African Republic","flag":"Central-african-republic","masks":"[\"+236-##-##-####\"]","placeholder":"+236","masks_placeholder":"[\"+236\"]","stat_count":"0"},"td":{"id":"295","active":"1","code":"TD","name":"Chad","flag":"Chad","masks":"[\"+235-##-##-##-##\"]","placeholder":"+235","masks_placeholder":"[\"+235\"]","stat_count":"0"},"cl":{"id":"296","active":"1","code":"CL","name":"Chile","flag":"Chile","masks":"[\"+56-#-####-####\"]","placeholder":"+56","masks_placeholder":"[\"+56\"]","stat_count":"0"},"cn":{"id":"297","active":"1","code":"CN","name":"China","flag":"China","masks":"[\"+86-##-#####-#####\",\"+86(###)####-####\",\"+86(###)####-###\"]","placeholder":"+86","masks_placeholder":"[\"+86\"]","stat_count":"0"},"co":{"id":"300","active":"1","code":"CO","name":"Colombia","flag":"Colombia","masks":"[\"+57(###)###-####\"]","placeholder":"+57","masks_placeholder":"[\"+57\"]","stat_count":"0"},"km":{"id":"301","active":"1","code":"KM","name":"Comoros","flag":"Comoros","masks":"[\"+269-##-#####\"]","placeholder":"+269","masks_placeholder":"[\"+269\"]","stat_count":"0"},"cr":{"id":"303","active":"1","code":"CR","name":"Costa Rica","flag":"Costa-rica","masks":"[\"+506-####-####\"]","placeholder":"+506","masks_placeholder":"[\"+506\"]","stat_count":"0"},"hr":{"id":"304","active":"1","code":"HR","name":"Croatia","flag":"Croatia","masks":"[\"+385-##-###-###\"]","placeholder":"+385","masks_placeholder":"[\"+385\"]","stat_count":"0"},"cu":{"id":"305","active":"1","code":"CU","name":"Cuba","flag":"Cuba","masks":"[\"+53-#-###-####\"]","placeholder":"+53","masks_placeholder":"[\"+53\"]","stat_count":"0"},"cw":{"id":"306","active":"1","code":"CW","name":"Curacao","flag":"Curacao","masks":"[\"+599-###-####\"]","placeholder":"+599","masks_placeholder":"[\"+599\"]","stat_count":"0"},"cy":{"id":"307","active":"1","code":"CY","name":"Cyprus","flag":"Cyprus","masks":"[\"+357-##-###-###\"]","placeholder":"+357","masks_placeholder":"[\"+357\"]","stat_count":"0"},"cz":{"id":"308","active":"1","code":"CZ","name":"Czech Republic","flag":"Czech-republic","masks":"[\"+420(###)###-###\"]","placeholder":"+420","masks_placeholder":"[\"+420\"]","stat_count":"0"},"cd":{"id":"309","active":"1","code":"CD","name":"Democratic Republic of the Congo","flag":"dr-congo","masks":"[\"+243(###)###-###\"]","placeholder":"+243","masks_placeholder":"[\"+243\"]","stat_count":"0"},"dk":{"id":"310","active":"1","code":"DK","name":"Denmark","flag":"Denmark","masks":"[\"+45-##-##-##-##\"]","placeholder":"+45","masks_placeholder":"[\"+45\"]","stat_count":"0"},"dj":{"id":"311","active":"1","code":"DJ","name":"Djibouti","flag":"Djibouti","masks":"[\"+253-##-##-##-##\"]","placeholder":"+253","masks_placeholder":"[\"+253\"]","stat_count":"0"},"dm":{"id":"312","active":"1","code":"DM","name":"Dominica","flag":"Dominica","masks":"[\"+1(767)###-####\"]","placeholder":"+1767","masks_placeholder":"[\"+1767\"]","stat_count":"0"},"do":{"id":"313","active":"1","code":"DO","name":"Dominican Republic","flag":"dominican-republic","masks":"[\"+1(849)###-####\",\"+1(809)###-####\",\"+1(829)###-####\"]","placeholder":"+18","masks_placeholder":"[\"+1849\",\"+1809\",\"+1829\"]","stat_count":"0"},"tl":{"id":"314","active":"1","code":"TL","name":"East Timor","flag":"timor-leste","masks":"[\"+670-78#-#####\",\"+670-###-####\",\"+670-77#-#####\"]","placeholder":"+670","masks_placeholder":"[\"+67078\",\"+670\",\"+67077\"]","stat_count":"0"},"ec":{"id":"315","active":"1","code":"EC","name":"Ecuador","flag":"Ecuador","masks":"[\"+593-#-###-####\",\"+593-##-###-####\"]","placeholder":"+593","masks_placeholder":"[\"+593\"]","stat_count":"0"},"eg":{"id":"316","active":"1","code":"EG","name":"Egypt","flag":"Egypt","masks":"[\"+20(###)###-####\"]","placeholder":"+20","masks_placeholder":"[\"+20\"]","stat_count":"0"},"sv":{"id":"317","active":"1","code":"SV","name":"El Salvador","flag":"El-salvador","masks":"[\"+503-##-##-####\"]","placeholder":"+503","masks_placeholder":"[\"+503\"]","stat_count":"0"},"gq":{"id":"318","active":"1","code":"GQ","name":"Equatorial Guinea","flag":"equatorial-guinea","masks":"[\"+240-##-###-####\"]","placeholder":"+240","masks_placeholder":"[\"+240\"]","stat_count":"0"},"er":{"id":"319","active":"1","code":"ER","name":"Eritrea","flag":"Eritrea","masks":"[\"+291-#-###-###\"]","placeholder":"+291","masks_placeholder":"[\"+291\"]","stat_count":"0"},"ee":{"id":"320","active":"1","code":"EE","name":"Estonia","flag":"Estonia","masks":"[\"+372-###-####\",\"+372-####-####\"]","placeholder":"+372","masks_placeholder":"[\"+372\"]","stat_count":"4"},"et":{"id":"321","active":"1","code":"ET","name":"Ethiopia","flag":"Ethiopia","masks":"[\"+251-##-###-####\"]","placeholder":"+251","masks_placeholder":"[\"+251\"]","stat_count":"0"},"fj":{"id":"324","active":"1","code":"FJ","name":"Fiji","flag":"Fiji","masks":"[\"+679-##-#####\"]","placeholder":"+679","masks_placeholder":"[\"+679\"]","stat_count":"0"},"fi":{"id":"325","active":"1","code":"FI","name":"Finland","flag":"Finland","masks":"[\"+358(###)###-##-##\"]","placeholder":"+358","masks_placeholder":"[\"+358\"]","stat_count":"1"},"fr":{"id":"326","active":"1","code":"FR","name":"France","flag":"France","masks":"[\"+590(###)###-###\",\"+262-#####-####\",\"+33(###)###-###\",\"+508-##-####\"]","placeholder":"+","masks_placeholder":"[\"+590\",\"+262\",\"+33\",\"+508\"]","stat_count":"6"},"ga":{"id":"330","active":"1","code":"GA","name":"Gabon","flag":"Gabon","masks":"[\"+241-#-##-##-##\"]","placeholder":"+241","masks_placeholder":"[\"+241\"]","stat_count":"0"},"gm":{"id":"331","active":"1","code":"GM","name":"Gambia","flag":"Gambia","masks":"[\"+220(###)##-##\"]","placeholder":"+220","masks_placeholder":"[\"+220\"]","stat_count":"0"},"ge":{"id":"332","active":"1","code":"GE","name":"Georgia","flag":"Georgia","masks":"[\"+995(###)###-###\"]","placeholder":"+995","masks_placeholder":"[\"+995\"]","stat_count":"0"},"de":{"id":"333","active":"1","code":"DE","name":"Germany","flag":"Germany","masks":"[\"+49-###-###\",\"+49(####)###-####\",\"+49(###)###-####\",\"+49(###)##-####\",\"+49(###)##-###\",\"+49(###)##-##\"]","placeholder":"+49","masks_placeholder":"[\"+49\"]","stat_count":"9"},"gh":{"id":"334","active":"1","code":"GH","name":"Ghana","flag":"Ghana","masks":"[\"+233(###)###-###\"]","placeholder":"+233","masks_placeholder":"[\"+233\"]","stat_count":"0"},"gr":{"id":"336","active":"1","code":"GR","name":"Greece","flag":"Greece","masks":"[\"+30(###)###-####\"]","placeholder":"+30","masks_placeholder":"[\"+30\"]","stat_count":"0"},"gd":{"id":"338","active":"1","code":"GD","name":"Grenada","flag":"Grenada","masks":"[\"+1(473)###-####\"]","placeholder":"+1473","masks_placeholder":"[\"+1473\"]","stat_count":"0"},"gt":{"id":"341","active":"1","code":"GT","name":"Guatemala","flag":"Guatemala","masks":"[\"+502-#-###-####\"]","placeholder":"+502","masks_placeholder":"[\"+502\"]","stat_count":"0"},"gn":{"id":"343","active":"1","code":"GN","name":"Guinea","flag":"Guinea","masks":"[\"+224-##-###-###\"]","placeholder":"+224","masks_placeholder":"[\"+224\"]","stat_count":"0"},"gw":{"id":"344","active":"1","code":"GW","name":"Guinea-Bissau","flag":"Guinea-bissau","masks":"[\"+245-#-######\"]","placeholder":"+245","masks_placeholder":"[\"+245\"]","stat_count":"0"},"ht":{"id":"346","active":"1","code":"HT","name":"Haiti","flag":"Haiti","masks":"[\"+509-##-##-####\"]","placeholder":"+509","masks_placeholder":"[\"+509\"]","stat_count":"0"},"hn":{"id":"348","active":"1","code":"HN","name":"Honduras","flag":"Honduras","masks":"[\"+504-####-####\"]","placeholder":"+504","masks_placeholder":"[\"+504\"]","stat_count":"0"},"hk":{"id":"349","active":"1","code":"HK","name":"Hong Kong","flag":"Hong-kong","masks":"[\"+852-####-####\"]","placeholder":"+852","masks_placeholder":"[\"+852\"]","stat_count":"0"},"hu":{"id":"350","active":"1","code":"HU","name":"Hungary","flag":"Hungary","masks":"[\"+36(###)###-###\"]","placeholder":"+36","masks_placeholder":"[\"+36\"]","stat_count":"0"},"is":{"id":"351","active":"1","code":"IS","name":"Iceland","flag":"Iceland","masks":"[\"+354-###-####\"]","placeholder":"+354","masks_placeholder":"[\"+354\"]","stat_count":"0"},"in":{"id":"352","active":"1","code":"IN","name":"India","flag":"India","masks":"[\"+91(####)###-###\"]","placeholder":"+91","masks_placeholder":"[\"+91\"]","stat_count":"0"},"id":{"id":"353","active":"1","code":"ID","name":"Indonesia","flag":"Indonesia","masks":"[\"+62(8##)###-##-###\",\"+62(8##)###-####\",\"+62-##-###-##\",\"+62-##-###-###\",\"+62-##-###-####\",\"+62(8##)###-###\"]","placeholder":"+62","masks_placeholder":"[\"+628\",\"+62\"]","stat_count":"0"},"ir":{"id":"354","active":"1","code":"IR","name":"Iran","flag":"Iran","masks":"[\"+98(###)###-####\"]","placeholder":"+98","masks_placeholder":"[\"+98\"]","stat_count":"0"},"iq":{"id":"355","active":"1","code":"IQ","name":"Iraq","flag":"Iraq","masks":"[\"+964(###)###-####\"]","placeholder":"+964","masks_placeholder":"[\"+964\"]","stat_count":"0"},"ie":{"id":"356","active":"1","code":"IE","name":"Ireland","flag":"Ireland","masks":"[\"+353(###)###-###\"]","placeholder":"+353","masks_placeholder":"[\"+353\"]","stat_count":"0"},"il":{"id":"358","active":"1","code":"IL","name":"Israel","flag":"Israel","masks":"[\"+972-#-###-####\",\"+972-5#-###-####\"]","placeholder":"+972","masks_placeholder":"[\"+972\",\"+9725\"]","stat_count":"0"},"it":{"id":"359","active":"1","code":"IT","name":"Italy","flag":"Italy","masks":"[\"+39(###)####-###\"]","placeholder":"+39","masks_placeholder":"[\"+39\"]","stat_count":"0"},"ci":{"id":"360","active":"1","code":"CI","name":"Ivory Coast","flag":"ivory-coast","masks":"[\"+225-##-###-###\"]","placeholder":"+225","masks_placeholder":"[\"+225\"]","stat_count":"0"},"jm":{"id":"361","active":"1","code":"JM","name":"Jamaica","flag":"Jamaica","masks":"[\"+1(876)###-####\"]","placeholder":"+1876","masks_placeholder":"[\"+1876\"]","stat_count":"0"},"jp":{"id":"362","active":"1","code":"JP","name":"Japan","flag":"Japan","masks":"[\"+81(###)###-###\",\"+81-##-####-####\"]","placeholder":"+81","masks_placeholder":"[\"+81\"]","stat_count":"0"},"jo":{"id":"364","active":"1","code":"JO","name":"Jordan","flag":"Jordan","masks":"[\"+962-#-####-####\"]","placeholder":"+962","masks_placeholder":"[\"+962\"]","stat_count":"0"},"kz":{"id":"365","active":"1","code":"KZ","name":"Kazakhstan","flag":"Kazakhstan","masks":"[\"+7(7##)###-##-##\",\"+7(6##)###-##-##\"]","placeholder":"+7","masks_placeholder":"[\"+77\",\"+76\"]","stat_count":"0"},"ke":{"id":"366","active":"1","code":"KE","name":"Kenya","flag":"Kenya","masks":"[\"+254-###-######\"]","placeholder":"+254","masks_placeholder":"[\"+254\"]","stat_count":"0"},"ki":{"id":"367","active":"1","code":"KI","name":"Kiribati","flag":"Kiribati","masks":"[\"+686-##-###\"]","placeholder":"+686","masks_placeholder":"[\"+686\"]","stat_count":"0"},"kw":{"id":"369","active":"1","code":"KW","name":"Kuwait","flag":"Kuwait","masks":"[\"+965-####-####\"]","placeholder":"+965","masks_placeholder":"[\"+965\"]","stat_count":"0"},"kg":{"id":"370","active":"1","code":"KG","name":"Kyrgyzstan","flag":"Kyrgyzstan","masks":"[\"+996(###)###-###\"]","placeholder":"+996","masks_placeholder":"[\"+996\"]","stat_count":"0"},"la":{"id":"371","active":"1","code":"LA","name":"Laos","flag":"Laos","masks":"[\"+856-##-###-###\",\"+856(20##)###-###\"]","placeholder":"+856","masks_placeholder":"[\"+856\",\"+85620\"]","stat_count":"0"},"lv":{"id":"372","active":"1","code":"LV","name":"Latvia","flag":"Latvia","masks":"[\"+371-##-###-###\"]","placeholder":"+371","masks_placeholder":"[\"+371\"]","stat_count":"4"},"lb":{"id":"373","active":"1","code":"LB","name":"Lebanon","flag":"Lebanon","masks":"[\"+961-#-###-###\",\"+961-##-###-###\"]","placeholder":"+961","masks_placeholder":"[\"+961\"]","stat_count":"0"},"ls":{"id":"374","active":"1","code":"LS","name":"Lesotho","flag":"Lesotho","masks":"[\"+266-#-###-####\"]","placeholder":"+266","masks_placeholder":"[\"+266\"]","stat_count":"0"},"lr":{"id":"375","active":"1","code":"LR","name":"Liberia","flag":"Liberia","masks":"[\"+231-##-###-###\"]","placeholder":"+231","masks_placeholder":"[\"+231\"]","stat_count":"0"},"ly":{"id":"376","active":"1","code":"LY","name":"Libya","flag":"Libya","masks":"[\"+218-21-###-####\",\"+218-##-###-###\"]","placeholder":"+218","masks_placeholder":"[\"+21821\",\"+218\"]","stat_count":"0"},"li":{"id":"377","active":"1","code":"LI","name":"Liechtenstein","flag":"Liechtenstein","masks":"[\"+423(###)###-####\"]","placeholder":"+423","masks_placeholder":"[\"+423\"]","stat_count":"0"},"lt":{"id":"378","active":"1","code":"LT","name":"Lithuania","flag":"Lithuania","masks":"[\"+370(###)##-###\"]","placeholder":"+370","masks_placeholder":"[\"+370\"]","stat_count":"0"},"lu":{"id":"379","active":"1","code":"LU","name":"Luxembourg","flag":"Luxembourg","masks":"[\"+352(###)###-###\"]","placeholder":"+352","masks_placeholder":"[\"+352\"]","stat_count":"0"},"mk":{"id":"381","active":"1","code":"MK","name":"Macedonia","flag":"north-macedonia","masks":"[\"+389-##-###-###\"]","placeholder":"+389","masks_placeholder":"[\"+389\"]","stat_count":"0"},"mg":{"id":"382","active":"1","code":"MG","name":"Madagascar","flag":"Madagascar","masks":"[\"+261-##-##-#####\"]","placeholder":"+261","masks_placeholder":"[\"+261\"]","stat_count":"0"},"mw":{"id":"383","active":"1","code":"MW","name":"Malawi","flag":"Malawi","masks":"[\"+265-#-####-####\",\"+265-1-###-###\"]","placeholder":"+265","masks_placeholder":"[\"+265\",\"+2651\"]","stat_count":"0"},"my":{"id":"384","active":"1","code":"MY","name":"Malaysia","flag":"malaysia","masks":"[\"+60-#-###-###\",\"+60-##-###-####\",\"+60(###)###-###\",\"+60-##-###-###\"]","placeholder":"+60","masks_placeholder":"[\"+60\"]","stat_count":"0"},"mv":{"id":"385","active":"1","code":"MV","name":"Maldives","flag":"Maldives","masks":"[\"+960-###-####\"]","placeholder":"+960","masks_placeholder":"[\"+960\"]","stat_count":"0"},"ml":{"id":"386","active":"1","code":"ML","name":"Mali","flag":"Mali","masks":"[\"+223-##-##-####\"]","placeholder":"+223","masks_placeholder":"[\"+223\"]","stat_count":"0"},"mt":{"id":"387","active":"1","code":"MT","name":"Malta","flag":"Malta","masks":"[\"+356-####-####\"]","placeholder":"+356","masks_placeholder":"[\"+356\"]","stat_count":"0"},"mh":{"id":"388","active":"1","code":"MH","name":"Marshall Islands","flag":"marshall-islands","masks":"[\"+692-###-####\"]","placeholder":"+692","masks_placeholder":"[\"+692\"]","stat_count":"0"},"mr":{"id":"390","active":"1","code":"MR","name":"Mauritania","flag":"Mauritania","masks":"[\"+222-##-##-####\"]","placeholder":"+222","masks_placeholder":"[\"+222\"]","stat_count":"0"},"mu":{"id":"391","active":"1","code":"MU","name":"Mauritius","flag":"Mauritius","masks":"[\"+230-###-####\"]","placeholder":"+230","masks_placeholder":"[\"+230\"]","stat_count":"0"},"mx":{"id":"393","active":"1","code":"MX","name":"Mexico","flag":"Mexico","masks":"[\"+52-##-##-####\",\"+52(###)###-####\"]","placeholder":"+52","masks_placeholder":"[\"+52\"]","stat_count":"0"},"fm":{"id":"394","active":"1","code":"FM","name":"Micronesia","flag":"Micronesia","masks":"[\"+691-###-####\"]","placeholder":"+691","masks_placeholder":"[\"+691\"]","stat_count":"0"},"md":{"id":"395","active":"1","code":"MD","name":"Moldova","flag":"Moldova","masks":"[\"+373-####-####\"]","placeholder":"+373","masks_placeholder":"[\"+373\"]","stat_count":"0"},"mc":{"id":"396","active":"1","code":"MC","name":"Monaco","flag":"Monaco","masks":"[\"+377-##-###-###\",\"+377(###)###-###\"]","placeholder":"+377","masks_placeholder":"[\"+377\"]","stat_count":"0"},"mn":{"id":"397","active":"1","code":"MN","name":"Mongolia","flag":"Mongolia","masks":"[\"+976-##-##-####\"]","placeholder":"+976","masks_placeholder":"[\"+976\"]","stat_count":"0"},"me":{"id":"398","active":"1","code":"ME","name":"Montenegro","flag":"Montenegro","masks":"[\"+382-##-###-###\"]","placeholder":"+382","masks_placeholder":"[\"+382\"]","stat_count":"0"},"ma":{"id":"400","active":"1","code":"MA","name":"Morocco","flag":"Morocco","masks":"[\"+212-##-####-###\"]","placeholder":"+212","masks_placeholder":"[\"+212\"]","stat_count":"0"},"mz":{"id":"401","active":"1","code":"MZ","name":"Mozambique","flag":"Mozambique","masks":"[\"+258-##-###-###\"]","placeholder":"+258","masks_placeholder":"[\"+258\"]","stat_count":"0"},"mm":{"id":"402","active":"1","code":"MM","name":"Myanmar","flag":"Myanmar","masks":"[\"+95-###-###\",\"+95-##-###-###\",\"+95-#-###-###\"]","placeholder":"+95","masks_placeholder":"[\"+95\"]","stat_count":"0"},"na":{"id":"403","active":"1","code":"NA","name":"Namibia","flag":"Namibia","masks":"[\"+264-##-###-####\"]","placeholder":"+264","masks_placeholder":"[\"+264\"]","stat_count":"0"},"nr":{"id":"404","active":"1","code":"NR","name":"Nauru","flag":"Nauru","masks":"[\"+674-###-####\"]","placeholder":"+674","masks_placeholder":"[\"+674\"]","stat_count":"0"},"np":{"id":"405","active":"1","code":"NP","name":"Nepal","flag":"Nepal","masks":"[\"+977-##-###-###\"]","placeholder":"+977","masks_placeholder":"[\"+977\"]","stat_count":"0"},"nl":{"id":"406","active":"1","code":"NL","name":"Netherlands","flag":"Netherlands","masks":"[\"+31-##-###-####\"]","placeholder":"+31","masks_placeholder":"[\"+31\"]","stat_count":"0"},"nz":{"id":"408","active":"1","code":"NZ","name":"New Zealand","flag":"New-zealand","masks":"[\"+64(###)###-####\",\"+64(###)###-###\",\"+64-##-###-###\"]","placeholder":"+64","masks_placeholder":"[\"+64\"]","stat_count":"0"},"ni":{"id":"409","active":"1","code":"NI","name":"Nicaragua","flag":"Nicaragua","masks":"[\"+505-####-####\"]","placeholder":"+505","masks_placeholder":"[\"+505\"]","stat_count":"0"},"ne":{"id":"410","active":"1","code":"NE","name":"Niger","flag":"Niger","masks":"[\"+227-##-##-####\"]","placeholder":"+227","masks_placeholder":"[\"+227\"]","stat_count":"0"},"ng":{"id":"411","active":"1","code":"NG","name":"Nigeria","flag":"Nigeria","masks":"[\"+234(###)###-####\",\"+234-##-###-###\",\"+234-##-###-##\"]","placeholder":"+234","masks_placeholder":"[\"+234\"]","stat_count":"0"},"kp":{"id":"414","active":"1","code":"KP","name":"North Korea","flag":"North-korea","masks":"[\"+850-####-#############\",\"+850-191-###-####\",\"+850-##-###-###\",\"+850-###-####-###\",\"+850-###-###\",\"+850-####-####\"]","placeholder":"+850","masks_placeholder":"[\"+850\",\"+850191\"]","stat_count":"0"},"no":{"id":"416","active":"1","code":"NO","name":"Norway","flag":"Norway","masks":"[\"+47(###)##-###\"]","placeholder":"+47","masks_placeholder":"[\"+47\"]","stat_count":"0"},"om":{"id":"417","active":"1","code":"OM","name":"Oman","flag":"Oman","masks":"[\"+968-##-###-###\"]","placeholder":"+968","masks_placeholder":"[\"+968\"]","stat_count":"0"},"pk":{"id":"418","active":"1","code":"PK","name":"Pakistan","flag":"Pakistan","masks":"[\"+92(###)###-####\"]","placeholder":"+92","masks_placeholder":"[\"+92\"]","stat_count":"0"},"pw":{"id":"419","active":"1","code":"PW","name":"Palau","flag":"Palau","masks":"[\"+680-###-####\"]","placeholder":"+680","masks_placeholder":"[\"+680\"]","stat_count":"0"},"ps":{"id":"420","active":"1","code":"PS","name":"Palestinian Territory","flag":"Palestine","masks":"[\"+970-##-###-####\"]","placeholder":"+970","masks_placeholder":"[\"+970\"]","stat_count":"0"},"pa":{"id":"421","active":"1","code":"PA","name":"Panama","flag":"Panama","masks":"[\"+507-###-####\"]","placeholder":"+507","masks_placeholder":"[\"+507\"]","stat_count":"0"},"pg":{"id":"422","active":"1","code":"PG","name":"Papua New Guinea","flag":"Papua-new-guinea","masks":"[\"+675(###)##-###\"]","placeholder":"+675","masks_placeholder":"[\"+675\"]","stat_count":"0"},"py":{"id":"423","active":"1","code":"PY","name":"Paraguay","flag":"Paraguay","masks":"[\"+595(###)###-###\"]","placeholder":"+595","masks_placeholder":"[\"+595\"]","stat_count":"0"},"pe":{"id":"424","active":"1","code":"PE","name":"Peru","flag":"Peru","masks":"[\"+51(###)###-###\"]","placeholder":"+51","masks_placeholder":"[\"+51\"]","stat_count":"0"},"ph":{"id":"425","active":"1","code":"PH","name":"Philippines","flag":"Philippines","masks":"[\"+63(###)###-####\"]","placeholder":"+63","masks_placeholder":"[\"+63\"]","stat_count":"0"},"pl":{"id":"427","active":"1","code":"PL","name":"Poland","flag":"Poland","masks":"[\"+48(###)###-###\"]","placeholder":"+48","masks_placeholder":"[\"+48\"]","stat_count":"4"},"pt":{"id":"428","active":"1","code":"PT","name":"Portugal","flag":"Portugal","masks":"[\"+351-##-###-####\"]","placeholder":"+351","masks_placeholder":"[\"+351\"]","stat_count":"0"},"qa":{"id":"430","active":"1","code":"QA","name":"Qatar","flag":"Qatar","masks":"[\"+974-####-####\"]","placeholder":"+974","masks_placeholder":"[\"+974\"]","stat_count":"0"},"cg":{"id":"431","active":"1","code":"CG","name":"Republic of the Congo","flag":"congo","masks":"[\"+242-##-###-####\"]","placeholder":"+242","masks_placeholder":"[\"+242\"]","stat_count":"0"},"ro":{"id":"433","active":"1","code":"RO","name":"Romania","flag":"Romania","masks":"[\"+40-##-###-####\"]","placeholder":"+40","masks_placeholder":"[\"+40\"]","stat_count":"0"},"ru":{"id":"434","active":"1","code":"RU","name":"Russia","flag":"Russia","masks":"[\"+7(###)###-##-##\"]","placeholder":"+7","masks_placeholder":"[\"+7\"]","stat_count":"15"},"rw":{"id":"435","active":"1","code":"RW","name":"Rwanda","flag":"Rwanda","masks":"[\"+250(###)###-###\"]","placeholder":"+250","masks_placeholder":"[\"+250\"]","stat_count":"0"},"ws":{"id":"443","active":"1","code":"WS","name":"Samoa","flag":"Samoa","masks":"[\"+685-##-####\"]","placeholder":"+685","masks_placeholder":"[\"+685\"]","stat_count":"0"},"sm":{"id":"444","active":"1","code":"SM","name":"San Marino","flag":"San-marino","masks":"[\"+378-####-######\"]","placeholder":"+378","masks_placeholder":"[\"+378\"]","stat_count":"0"},"st":{"id":"445","active":"1","code":"ST","name":"Sao Tome and Principe","flag":"sao-tome-and-principe","masks":"[\"+239-##-#####\"]","placeholder":"+239","masks_placeholder":"[\"+239\"]","stat_count":"0"},"sa":{"id":"446","active":"1","code":"SA","name":"Saudi Arabia","flag":"Saudi-arabia","masks":"[\"+966-#-###-####\",\"+966-5-####-####\"]","placeholder":"+966","masks_placeholder":"[\"+966\",\"+9665\"]","stat_count":"0"},"sn":{"id":"447","active":"1","code":"SN","name":"Senegal","flag":"Senegal","masks":"[\"+221-##-###-####\"]","placeholder":"+221","masks_placeholder":"[\"+221\"]","stat_count":"0"},"rs":{"id":"448","active":"1","code":"RS","name":"Serbia","flag":"Serbia","masks":"[\"+381-##-###-####\"]","placeholder":"+381","masks_placeholder":"[\"+381\"]","stat_count":"0"},"sc":{"id":"449","active":"1","code":"SC","name":"Seychelles","flag":"Seychelles","masks":"[\"+248-#-###-###\"]","placeholder":"+248","masks_placeholder":"[\"+248\"]","stat_count":"0"},"sl":{"id":"450","active":"1","code":"SL","name":"Sierra Leone","flag":"Sierra-leone","masks":"[\"+232-##-######\"]","placeholder":"+232","masks_placeholder":"[\"+232\"]","stat_count":"0"},"sg":{"id":"451","active":"1","code":"SG","name":"Singapore","flag":"Singapore","masks":"[\"+65-####-####\"]","placeholder":"+65","masks_placeholder":"[\"+65\"]","stat_count":"0"},"sx":{"id":"452","active":"1","code":"SX","name":"Sint Maarten","flag":"Sint-maarten","masks":"[\"+1(721)###-####\"]","placeholder":"+1721","masks_placeholder":"[\"+1721\"]","stat_count":"0"},"sk":{"id":"453","active":"1","code":"SK","name":"Slovakia","flag":"Slovakia","masks":"[\"+421(###)###-###\"]","placeholder":"+421","masks_placeholder":"[\"+421\"]","stat_count":"0"},"si":{"id":"454","active":"1","code":"SI","name":"Slovenia","flag":"Slovenia","masks":"[\"+386-##-###-###\"]","placeholder":"+386","masks_placeholder":"[\"+386\"]","stat_count":"0"},"sb":{"id":"455","active":"1","code":"SB","name":"Solomon Islands","flag":"Solomon-islands","masks":"[\"+677-#####\",\"+677-###-####\"]","placeholder":"+677","masks_placeholder":"[\"+677\"]","stat_count":"0"},"so":{"id":"456","active":"1","code":"SO","name":"Somalia","flag":"Somalia","masks":"[\"+252-#-###-###\",\"+252-##-###-###\"]","placeholder":"+252","masks_placeholder":"[\"+252\"]","stat_count":"0"},"za":{"id":"457","active":"1","code":"ZA","name":"South Africa","flag":"South-africa","masks":"[\"+27-##-###-####\"]","placeholder":"+27","masks_placeholder":"[\"+27\"]","stat_count":"0"},"kr":{"id":"459","active":"1","code":"KR","name":"South Korea","flag":"South-korea","masks":"[\"+82-##-###-####\"]","placeholder":"+82","masks_placeholder":"[\"+82\"]","stat_count":"0"},"ss":{"id":"460","active":"1","code":"SS","name":"South Sudan","flag":"South-sudan","masks":"[\"+211-##-###-####\"]","placeholder":"+211","masks_placeholder":"[\"+211\"]","stat_count":"0"},"es":{"id":"461","active":"1","code":"ES","name":"Spain","flag":"Spain","masks":"[\"+34(###)###-###\"]","placeholder":"+34","masks_placeholder":"[\"+34\"]","stat_count":"0"},"lk":{"id":"462","active":"1","code":"LK","name":"Sri Lanka","flag":"Sri-lanka","masks":"[\"+94-##-###-####\"]","placeholder":"+94","masks_placeholder":"[\"+94\"]","stat_count":"0"},"sd":{"id":"463","active":"1","code":"SD","name":"Sudan","flag":"Sudan","masks":"[\"+249-##-###-####\"]","placeholder":"+249","masks_placeholder":"[\"+249\"]","stat_count":"0"},"sr":{"id":"464","active":"1","code":"SR","name":"Suriname","flag":"Suriname","masks":"[\"+597-###-###\",\"+597-###-####\"]","placeholder":"+597","masks_placeholder":"[\"+597\"]","stat_count":"0"},"se":{"id":"467","active":"1","code":"SE","name":"Sweden","flag":"Sweden","masks":"[\"+46-##-###-####\"]","placeholder":"+46","masks_placeholder":"[\"+46\"]","stat_count":"11"},"ch":{"id":"468","active":"1","code":"CH","name":"Switzerland","flag":"Switzerland","masks":"[\"+41-##-###-####\"]","placeholder":"+41","masks_placeholder":"[\"+41\"]","stat_count":"8"},"sy":{"id":"469","active":"1","code":"SY","name":"Syria","flag":"syrian-arab-republic","masks":"[\"+963-##-####-###\"]","placeholder":"+963","masks_placeholder":"[\"+963\"]","stat_count":"0"},"tj":{"id":"471","active":"1","code":"TJ","name":"Tajikistan","flag":"Tajikistan","masks":"[\"+992-##-###-####\"]","placeholder":"+992","masks_placeholder":"[\"+992\"]","stat_count":"0"},"tz":{"id":"472","active":"1","code":"TZ","name":"Tanzania","flag":"Tanzania","masks":"[\"+255-##-###-####\"]","placeholder":"+255","masks_placeholder":"[\"+255\"]","stat_count":"0"},"th":{"id":"473","active":"1","code":"TH","name":"Thailand","flag":"Thailand","masks":"[\"+66-##-###-###\",\"+66-##-###-####\"]","placeholder":"+66","masks_placeholder":"[\"+66\"]","stat_count":"0"},"tg":{"id":"474","active":"1","code":"TG","name":"Togo","flag":"Togo","masks":"[\"+228-##-###-###\"]","placeholder":"+228","masks_placeholder":"[\"+228\"]","stat_count":"0"},"to":{"id":"476","active":"1","code":"TO","name":"Tonga","flag":"Tonga","masks":"[\"+676-#####\"]","placeholder":"+676","masks_placeholder":"[\"+676\"]","stat_count":"0"},"tt":{"id":"477","active":"1","code":"TT","name":"Trinidad and Tobago","flag":"Trinidad-and-tobago","masks":"[\"+1(868)###-####\"]","placeholder":"+1868","masks_placeholder":"[\"+1868\"]","stat_count":"0"},"tn":{"id":"478","active":"1","code":"TN","name":"Tunisia","flag":"Tunisia","masks":"[\"+216-##-###-###\"]","placeholder":"+216","masks_placeholder":"[\"+216\"]","stat_count":"0"},"tr":{"id":"479","active":"1","code":"TR","name":"Turkey","flag":"Turkey","masks":"[\"+90(###)###-####\"]","placeholder":"+90","masks_placeholder":"[\"+90\"]","stat_count":"0"},"tm":{"id":"480","active":"1","code":"TM","name":"Turkmenistan","flag":"Turkmenistan","masks":"[\"+993-#-###-####\"]","placeholder":"+993","masks_placeholder":"[\"+993\"]","stat_count":"0"},"tv":{"id":"482","active":"1","code":"TV","name":"Tuvalu","flag":"Tuvalu","masks":"[\"+688-2####\",\"+688-90####\"]","placeholder":"+688","masks_placeholder":"[\"+6882\",\"+68890\"]","stat_count":"0"},"vi":{"id":"483","active":"1","code":"VI","name":"U.S. Virgin Islands","flag":"Virgin-islands","masks":"[\"+1(340)###-####\"]","placeholder":"+1340","masks_placeholder":"[\"+1340\"]","stat_count":"0"},"ug":{"id":"484","active":"1","code":"UG","name":"Uganda","flag":"Uganda","masks":"[\"+256(###)###-###\"]","placeholder":"+256","masks_placeholder":"[\"+256\"]","stat_count":"0"},"ua":{"id":"485","active":"1","code":"UA","name":"Ukraine","flag":"Ukraine","masks":"[\"+380(##)###-##-##\"]","placeholder":"+380","masks_placeholder":"[\"+380\"]","stat_count":"0"},"ae":{"id":"486","active":"1","code":"AE","name":"United Arab Emirates","flag":"United-arab-emirates","masks":"[\"+971-#-###-####\",\"+971-5#-###-####\"]","placeholder":"+971","masks_placeholder":"[\"+971\",\"+9715\"]","stat_count":"56"},"gb":{"id":"487","active":"1","code":"GB","name":"United Kingdom","flag":"United-kingdom","masks":"[\"+44-##-####-####\"]","placeholder":"+44","masks_placeholder":"[\"+44\"]","stat_count":"0"},"us":{"id":"488","active":"1","code":"US","name":"United States","flag":"united-states-of-america","masks":"[\"+1(###)###-####\"]","placeholder":"+1","masks_placeholder":"[\"+1\"]","stat_count":"3"},"uy":{"id":"490","active":"1","code":"UY","name":"Uruguay","flag":"Uruguay","masks":"[\"+598-#-###-##-##\"]","placeholder":"+598","masks_placeholder":"[\"+598\"]","stat_count":"0"},"uz":{"id":"491","active":"1","code":"UZ","name":"Uzbekistan","flag":"Uzbekistan","masks":"[\"+998-##-###-####\"]","placeholder":"+998","masks_placeholder":"[\"+998\"]","stat_count":"0"},"vu":{"id":"492","active":"1","code":"VU","name":"Vanuatu","flag":"vanuaty","masks":"[\"+678-#####\",\"+678-##-#####\"]","placeholder":"+678","masks_placeholder":"[\"+678\"]","stat_count":"0"},"ve":{"id":"494","active":"1","code":"VE","name":"Venezuela","flag":"Venezuela","masks":"[\"+58(###)###-####\"]","placeholder":"+58","masks_placeholder":"[\"+58\"]","stat_count":"0"},"vn":{"id":"495","active":"1","code":"VN","name":"Vietnam","flag":"Vietnam","masks":"[\"+84(###)####-###\",\"+84-##-####-###\"]","placeholder":"+84","masks_placeholder":"[\"+84\"]","stat_count":"0"},"ye":{"id":"498","active":"1","code":"YE","name":"Yemen","flag":"Yemen","masks":"[\"+967-##-###-###\",\"+967-###-###-###\",\"+967-#-###-###\"]","placeholder":"+967","masks_placeholder":"[\"+967\"]","stat_count":"0"},"zm":{"id":"499","active":"1","code":"ZM","name":"Zambia","flag":"Zambia","masks":"[\"+260-##-###-####\"]","placeholder":"+260","masks_placeholder":"[\"+260\"]","stat_count":"0"},"zw":{"id":"500","active":"1","code":"ZW","name":"Zimbabwe","flag":"Zimbabwe","masks":"[\"+263-#-######\"]","placeholder":"+263","masks_placeholder":"[\"+263\"]","stat_count":"0"}};
globalFolder = 'cy';

$(document).ready(function () {
	if ($('#pay_form').length) {
		$('#pay_form').submit();
	}
    $("form[action='/register/'], form[action='/auth/'], form[action='/trade/'], form[action='/password/'], form[action='/balance/'], form[action='/invest/'], form[action='/withdraw/'], form[action='/2fa/'], form[action='/change_password/'], form[action='/change_wallets/'], form[action='/add_review/'], form[action='/message/'], form[action='/release/'], form[action='/pin/'], form[action='/check_confirm/'], form[action='/change_strategy/'], form[action='/lotteries_add/'], form[action='/lotteries_buy/'], form[action='/remove_session/'], form[action='/games_add/'], form[action='/games_seed_rotate/']").on('submit',function() {
		form = $(this);
		beforeSubmitForm(form);
		$.ajax({
		  	url: form.attr("action"),
		  	type: "POST",
		  	data: form.serialize(),
		  	error: function() {
			  	setTimeout(function() {form.submit()}, 1000);
		  	},
		  	success: function (responseText) {
			  	if (isJson(responseText)){
				  	button.removeAttr("disabled");
				  	ans = JSON.parse(responseText);
				  	afterSubmitForm(form, ans);
				  	if (ans.captcha_reset) {
					  	resetCaptcha(form);
				  	}
				  	if (ans.reset) {
					  	form[0].reset();
				  	}
				  	
				  					  	
				  	if (ans.location) {
					  	document.location.href = ans.location;
				  	}
				} else {
					setTimeout(function() {form.submit()}, 1000);
				}
				return false;
		  }
		});
		return false;
	});
})



</script> 
<script src="https://js.hcaptcha.com/1/api.js" async defer></script> 
<script src="https://js.pusher.com/8.2.0/pusher.min.js"></script> 
<script type="text/javascript">
	var pusher = new Pusher('8857d7c3e2c6a32c8ca1', {
      cluster: 'eu'
    });
    var publicChannel = pusher.subscribe('public');
    publicChannel.bind('end', function(data) {
	    processPusherPublic(data);
    });
    </script> 
<script defer src="assets/cy/libs/priority-navigation-master/dist/priority-nav.min.js?vs=100"></script> 
<script src="assets/cy/libs/jquery/jquery-3.6.0.min.js?vs=100" defer></script> 
<script defer src="assets/cy/libs/autosize/autosize.min.js?vs=100"></script> 
<script defer src="assets/cy/libs/swiper/swiper-bundle.min.js?vs=100"></script> 
<script defer src="assets/cy/libs/bootstrap/js/bootstrap.bundle.min.js?vs=100"></script> 
<script defer src="assets/cy/libs/bootstrap-select-1.14.0/dist/js/bootstrap-select.min.js?vs=100"></script> 
<script defer src="assets/cy/libs/bootstrap-select-1.14.0/dist/js/i18n/defaults-en_US.min.js?vs=100"></script> 
<script defer src="assets/cy/libs/clipboard.js-master/dist/clipboard.min.js?vs=100"></script> 
<script defer src="assets/cy/libs/ion.rangeSlider-master/js/ion.rangeSlider.min.js?vs=100"></script> 
<script defer src="assets/cy/libs/plyr/dist/plyr.polyfilled.min.js?vs=100"></script> 
<script defer src="assets/cy/libs/chart.js-3.7.1/dist/chart.min.js?vs=100"></script> 
<script defer src="assets/cy/libs/toastr-master/build/toastr.min.js?vs=100"></script> 
<script src="assets/cy/js/common.js?vs=100" defer></script> 
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8-beta.17/jquery.inputmask.min.js"></script> 
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.js"></script> 
<script type="text/javascript">
			$(document).ready(function() {
				$('.openUserInfo').on('click',function() {
					modal = $('#modalUser');
					startProgress();
					$.ajax({
					  	url: "/games_lotteries_user/",
					  	type: "POST",
					  	data: "login="+$(this).data('user-login'),
					  	error: function() {
						  	
					  	},
					  	success: function (responseText) {
						  	if (isJson(responseText)){
							  	stopProgress();
							  	ans = JSON.parse(responseText);
							  	if (ans.location == '/login/') {
								  	$('#modalLogin').modal('show');
							  	} else {
								  	modal.find('.modalUserPhoto').attr('src','assets/'+globalFolder+'/users/'+ans.user_info.photo+'.jpg');
								  	modal.find('.modalUserLogin').html(ans.user_info.login);
								  	modal.find('.modalUserDateCreate').html(ans.user_info.date_create_text);
								  	modal.find('.modalUserGameCurrency').addClass('d-none');
								  	modal.find('.modalUserGameAmount').html(modal.find('.modalUserGameAmount:first').data('no-data'));
								  	$.each(ans.user_info.big_wins,function(index,value) {
									  	currencyInfo = globalCurrencies[value.currency];
									  	modal.find('.modalUserGame[data-game="'+index+'"] .modalUserGameAmount').html(value.amount);
									  	modal.find('.modalUserGame[data-game="'+index+'"] .modalUserGameCurrency').removeClass('d-none');
									  	modal.find('.modalUserGame[data-game="'+index+'"] .modalUserGameCurrencyIcon').attr('src','assets/'+globalFolder+'/images/svg/payment/'+currencyInfo.config.data.icon+'.svg');
								  	})
								  	modal.modal('show');
							  	}
							}
							
						}
					});
				})
			})
	</script> 
<script type="text/javascript">
		
		
				
		var langMinimumIs = "Minimum is [number]";
		var langMaximumIs = "Maximum is [number]";
		var langDice = "Dice";
		var langLimbo = "Limbo";
		var langNewPaymentNotConfirmed = "Your payment of [amount] will be credited to your balance after confirmation by blockchain.";
		var langNewPaymentConfirmed = "Your balance has been successfully topped up by [amount].";
		
		
		
		
		
		
		
		
		function beforeSubmitForm(form, noDisabled = false) {
			form.find('.field-block').find('.field').removeClass('field-error');
			if (form.attr('action') != '/games_seed_rotate/')
			form.find('.field-message-block').remove();
			button = form.find('button[type="submit"]');
			if (!noDisabled)
			{
				button.attr("disabled","disabled");
				startProgress();
			}
		}
		function afterSubmitForm(form,ans) {
			stopProgress();
			button = form.find('button[type="submit"]');
			button.removeAttr("disabled");
			
			if (ans.errors) {
	            focused = false;
	            firstBlock = null;
	            errorsText = '';
	            errorsModalText = '';
	            $.each(ans.errors, function(index, value) {
	                input = form.find('[name="' + index + '"]');
	                block = input.closest('.field-block');
	                field = input.closest('.field');
	                field.addClass('field-error');
	                if (!block.length || !field.length) {
		                errorsModalText = errorsModalText + '<p>'+value+'</p>';
	                } else if (!block.find('.field-message-block').length) {
	                    block.append('<div class="field-message-block"><div class="field-message field-message--error"><div class="field-message__icon"></div><div class="field-message__text">' + value + '</div></div></div>');
	                }
	            });
	            if (errorsModalText) {
		            $('#modalError').find('.modal-info__descr').html(errorsModalText);
					$('#modalError').modal('show');
	            }
	        }
			
			if (ans.success && !ans.password_restored) {
				$('#modalSuccess').find('.modal-info__descr').html(ans.success);
	            $('#modalSuccess').modal('show');
			}
			
			
			if (ans.new_twofa == 'off') {
				ans.reset = false;
				
			}
			
			if (ans.action == 'confirm') {
				if (ans.confirm_type == '2fa') {
					confirmModal = $('#modalConfirm');
				} else {
					confirmModal = $('#modalConfirmEmail');
				}
				beforeSubmitForm(confirmModal.find('form'),true);
				confirmModal.find('input').val('');
				confirmModal.find('input[name=code]').val(ans.code);
				confirmModal.modal('show');
				ans.location = null;
				if (form.attr('action') == '/change_wallets/') {
					$('#modalAddWallet').modal('hide');
				}
				if (form.attr('action') == '/auth/') {
					$('#modalLogin').modal('hide');
				}
			}
			
			if (ans.action == 'confirmed' && ans.success) {
				$('#modalConfirm').modal('hide');
				$('#modalConfirmEmail').modal('hide');
				ans.location = null;
			}
			if (ans.action == 'not_confirmed' || ans.action == 'confirm_not_found') {
				$('#modalConfirm').modal('hide');
				$('#modalConfirmEmail').modal('hide');
				ans.location = null;
			}
			
			if (ans.wallets_changed) {
				$('#modalAddWallet').modal('hide');
			}
			if (ans.session_deleted_id) {
				$('.sessionItem[data-session='+ans.session_deleted_id+']').remove();
			}
			if (ans.new_pin || ans.new_twofa || ans.wallets_and_tags || ans.new_seed || ans.new_payment_not_confirmed || ans.new_payment_confirmed) {
				processPusher(ans);
			}
			twofaForm = $('form[action="/2fa/"]');
			if (ans.twofa_secret && twofaForm.length) {
				twofaForm.find('input[name=secret_code]').val(ans.twofa_secret);
				twofaForm.find('.authenticatorSecretCodeCopy').data('clipboard-text',ans.twofa_secret);
				twofaForm.find('.authenticatorSecretCodeCopy').attr('data-clipboard-text',ans.twofa_secret);
			}
			if (ans.twofa_image && twofaForm.length) {
				twofaForm.find('.authenticatorQr').attr('src',"");
				twofaForm.find('.authenticatorQr').attr('src',ans.twofa_image);
			}
			if (ans.location == '/login/') {
				if (!ans.password_changed_after_restore)
				$('#modalLogin').modal('show');
				ans.location = null;
			}
			if (ans.password_restored) {
				$('#modalRecovery').modal('hide');
				$('#modalPasswordSet').modal('show');
				ans.location = null;
			}
			if (ans.password_changed_after_restore) {
				$('#modalPasswordSet').modal('hide');
			}
			if (ans.need_money) {
				modal = $('#modalFastDeposit');
				firstPayment = null;
				if (ans.currency) {
					prepareTopupBlock(modal,ans.currency);
				} else {
					prepareTopupBlock(modal);
				}
				if (!modal.data('loaded')) {
					modal.find('select[name=currency]').on('change',function() {
						modal = $(this).closest('.modal');
						newPayment = changePaymentsByCurrency(modal, $(this).val());
						getDepositAddress(newPayment,$(this).val(),modal);
					});
					modal.find('select[name=payment]').on('change',function() {
						modal = $(this).closest('.modal');
						getDepositAddress($(this).val(),modal.find('select[name=currency]').val(),modal);
					})
					modal.data('loaded',1);
				}
				if (ans.need_amount_text) {
					modalFastDepositText = modal.find('.modalFastDepositText').data('text-1')+'<br /><br />'+modal.find('.modalFastDepositText').data('text-2');
					modalFastDepositText = modalFastDepositText.replace('[amount]','<strong>'+ans.need_amount_text+'</strong>');
					modal.find('.modalFastDepositText').html(modalFastDepositText);
				}
				modal.modal('show');
			}
		}
		
		function processPusherPublic(ans) {
			if (ans.new_lottery || ans.buy_ticket) {
				if ($('#sectionLotteries').length) {
					currentLotteriesFilterPage = $('#sectionLotteries').find('.sectionLotteriesFilter').find('input[name="page"]');
					currentLotteriesFilterPageValue = currentLotteriesFilterPage.val();
					currentLotteriesFilterLimit = $('#sectionLotteries').find('.sectionLotteriesFilter').find('input[name="limit"]');
					currentLotteriesFilterLimitValue = currentLotteriesFilterLimit.val();
					currentLotteriesFilterPage.val('1');
					currentLotteriesFilterLimit.val(parseInt(currentLotteriesFilterPageValue) * parseInt(currentLotteriesFilterLimitValue));
					sectionLotteries($('#sectionLotteries'));
					currentLotteriesFilterPage.val(currentLotteriesFilterPageValue);
					currentLotteriesFilterLimit.val(currentLotteriesFilterLimitValue);
				}
				if (ans.buy_ticket && $('#sectionLottery').length) {
					if (ans.buy_ticket.id == $('#sectionLottery').data('lottery')) {
						sectionLottery($('#sectionLottery'),ans.buy_ticket);
					}
				}
			}
			if (ans.new_game) {
				initSectionsGames();
			}
		}
		
		function sectionLottery(lotteryBlock,lotteryInfo) {
			if (lotteryInfo.date_end > 0) {
				lotteryBlock.find('.sectionLotteryFinished').removeClass('d-none');
				lotteryBlock.find('.sectionLotteryProgress').addClass('d-none');
				lotteryBlock.find('.sectionLotteryBuyTickets').addClass('d-none');
				lotteryBlock.find('.sectionLotteryWinner').removeClass('d-none');
				lotteryBlock.find('.sectionLotteryWinnerPhoto').data('user-login',lotteryInfo.winner_login);
				lotteryBlock.find('.sectionLotteryWinnerPhotoImg').attr('src','assets/cy/users/'+lotteryInfo.winner_photo+'.jpg');
				lotteryBlock.find('.sectionLotteryWinnerTicketId').html(lotteryInfo.winner_ticket_id);
				lotteryBlock.find('.sectionLotteryWinnerLogin').html(lotteryInfo.winner_login);
				lotteryBlock.find('.sectionLotteryPasswordText').addClass('d-none');
				lotteryBlock.find('.sectionLotteryPasswordInput').removeClass('d-none');
				lotteryBlock.find('.sectionLotteryPasswordInput textarea').html(lotteryInfo.password);
				lotteryBlock.find('.sectionLotteryPasswordInput .copy-field-btn').data('clipboard-text',lotteryInfo.password);
				lotteryBlock.find('.sectionLotteryPasswordInput .copy-field-btn').attr('data-clipboard-text',lotteryInfo.password);
			}
			lotteryBlock.find('.sectionLotteryProgress .sectionLotteryProgressWidth').attr('style','width: '+lotteryInfo.progress+'%;');
			lotteryBlock.find('.sectionLotteryTicketsFree').html(lotteryInfo.tickets_free);
			lotteryBlock.find('.sectionLotteryBuyTickets input[name=tickets]').attr('max',lotteryInfo.tickets_free);
			if (getCount(lotteryBlock.find('.sectionLotteryBuyTickets input[name=tickets]').val()) > lotteryInfo.tickets_free) {
				lotteryBlock.find('.sectionLotteryBuyTickets input[name=tickets]').val(lotteryInfo.tickets_free);
			}
			lotteryBlock.find('.sectionLotteryParticipants').html(lotteryInfo.participants);
			sectionLotteryTicketsPreviewHtml = '';
			$.each(lotteryInfo.tickets_preview_array,function(index,ticketPreview) {
				sectionLotteryTicketsPreviewHtml += '<div class="lottery-detail-participants__image"><img class="image" src="assets/cy/users/'+ticketPreview+'.jpg" alt=""></div>';
			})
			lotteryBlock.find('.sectionLotteryTicketsPreview').html(sectionLotteryTicketsPreviewHtml);
			lotteryBlock.find('.sectionLotteryTicketsItem').addClass('d-none');
			count = 0;
			$.each(lotteryInfo.tickets_array,function(index,ticket) {
				ticketLotteryBlock = lotteryBlock.find('.sectionLotteryTicketsItem').eq(count);
				ticketLotteryBlock.find('.sectionLotteryTicketsItemUser').data('user-login',ticket.l);
				ticketLotteryBlock.find('.sectionLotteryTicketsItemUserPhoto').attr('src','assets/cy/users/'+ticket.p+'.jpg');
				ticketLotteryBlock.find('.sectionLotteryTicketsItemUserLogin').html(ticket.l);
				ticketLotteryBlock.find('.sectionLotteryTicketsItemTicketsCount').html(ticket.t.length);
				ticketLotteryNumbers = '';
				$.each(ticket.t, function(index, ticketNumber) {
					ticketLotteryNumbers += ticketNumber.toString();
					if (index < ticket.t.length - 1) {
						ticketLotteryNumbers += ', ';
					}
				});
				ticketLotteryBlock.find('.sectionLotteryTicketsItemTicketsNumbers').html(ticketLotteryNumbers);
				ticketLotteryBlock.removeClass('d-none');
				count = count + 1;
			});
		}
		
		function convertToPercentDice(num) {
			var percent = (num - 0) / (100 - 0) * 100;
			return percent;
		}
		
		function processPusher(ans) {
			pinForm = $('form[action="/pin/"]');
			walletsForm = $('form[action="/change_wallets/"]');
			withdrawForm = $('form[action="/withdraw/"]');
			twofaForm = $('form[action="/2fa/"]');
			rotateSeedForm = $('form[action="/games_seed_rotate/"]');
			if (ans.new_twofa) {
				if (ans.new_twofa == 'on') {
					$('.authenticatorBlock').removeClass('d-none');
					$('.authenticatorBlockInv').addClass('d-none');
					if (twofaForm.length) {
						twofaForm.find('button[type=submit]').html(twofaForm.find('button[type=submit]').data('deactivate'));
					}
				} else {
					$('.authenticatorBlock').addClass('d-none');
					$('.authenticatorBlockInv').removeClass('d-none');
					if (twofaForm.length) {
						twofaForm.find('button[type=submit]').html(twofaForm.find('button[type=submit]').data('activate'));
						twofaForm[0].reset();
					}
				}
			}
			if (ans.wallets_and_tags) {
				updateWallets(ans.wallets_and_tags);
			}
			if (ans.new_result) {
				if ($('#sectionGame').length) {
					if ($('#sectionGame').data('game') == ans.new_result.game) {
						
						if ($('#sectionGame').data('game') == 'dice') {
							$('#sectionGame').find('.irs-with-grid .game-dice-bet').remove();
							if (BigNumber(ans.new_result.winner_amount).isGreaterThanOrEqualTo(ans.new_result.price)) {
								diceResultColor = 'green';
							} else {
								diceResultColor = 'red';
							}
							$('#sectionGame').find('.irs-with-grid').append('<div class="game-dice-bet game-dice-bet--'+diceResultColor+'" style="left: ' + convertToPercentDice(ans.new_result.result_array.number) + '%"><div class="game-dice-bet__count">' + ans.new_result.result_array.number + '</div></div>');
						}
						
						if ($('#sectionGame').data('game') == 'limbo') {
							if (BigNumber(ans.new_result.winner_amount).isGreaterThanOrEqualTo(ans.new_result.price)) {
								limboResultColor = '#2ee57a';
							} else {
								limboResultColor = '#ebf4ff';
							}
							$('#sectionGame').find('.game-limbo-ratio').css('color',limboResultColor);
							$('#sectionGame').find('.game-limbo-ratio').html(ans.new_result.result_array.win_factor+'x');
						}
						
						$($('#sectionGame .sectionGameResultItemsItem').get().reverse()).each(function() {
							if ($(this).next()) {
								if ($(this).data('loaded')) {
									$(this).next().data('loaded',1);
									$(this).next().data('result',$(this).data('result'));
									$(this).next().data('winner_amount',$(this).data('winner_amount'));
									$(this).next().data('price',$(this).data('price'));
									$(this).next().find('button').html($(this).find('button').html());
									if (BigNumber($(this).next().data('winner_amount')).isGreaterThanOrEqualTo($(this).next().data('price'))) {
										$(this).next().find('button').addClass('game-bet-slide-item--green');
									} else {
										$(this).next().find('button').removeClass('game-bet-slide-item--green');
									}
									$(this).next().removeClass('d-none');
									
									
									$(this).next().find('button').data('bet-id',$(this).find('button').data('bet-id'));
									$(this).next().find('button').data('game',$(this).find('button').data('game'));
									$(this).next().find('button').data('user-login',$(this).find('button').data('user-login'));
									$(this).next().find('button').data('user-photo',$(this).find('button').data('user-photo'));
									$(this).next().find('button').data('date-time-create-text',$(this).find('button').data('date-time-create-text'));
									$(this).next().find('button').data('price-text-short',$(this).find('button').data('price-text-short'));
									$(this).next().find('button').data('currency',$(this).find('button').data('currency'));
									$(this).next().find('button').data('currency-icon',$(this).find('button').data('currency-icon'));
									$(this).next().find('button').data('winner-amount',$(this).find('button').data('winner-amount'));
									$(this).next().find('button').data('winner-amount-text-short',$(this).find('button').data('winner-amount-text-short'));
									$(this).next().find('button').data('winner-factor',$(this).find('button').data('winner-factor'));
									$(this).next().find('button').data('info',$(this).find('button').data('info'));
									$(this).next().find('button').data('result',$(this).find('button').data('result'));
								}
								
							}
						})
						currencyInfo = globalCurrencies[ans.new_result.currency];
						firstItem = $('#sectionGame .sectionGameResultItemsItem:first');
						firstItem.data('loaded',1);
						if (ans.new_result.game == 'limbo') {
							firstItem.data('result',ans.new_result.result_array.win_factor);
							firstItem.find('button').html(ans.new_result.result_array.win_factor);
						} else {
							firstItem.data('result',ans.new_result.result_array.number);
							firstItem.find('button').html(ans.new_result.result_array.number);
						}
						firstItem.data('winner_amount',ans.new_result.winner_amount);
						firstItem.data('price',ans.new_result.price);
						
						if (BigNumber(ans.new_result.winner_amount).isGreaterThanOrEqualTo(0)) {
							firstItem.find('button').addClass('game-bet-slide-item--green');
						} else {
							firstItem.find('button').removeClass('game-bet-slide-item--green');
						}
						firstItem.removeClass('d-none');
						
						firstItem.find('button').data('bet-id',ans.new_result.id);
						firstItem.find('button').data('game',ans.new_result.game);
						firstItem.find('button').data('user-login',ans.new_result.user_login);
						firstItem.find('button').data('user-photo','assets/'+globalFolder+'/users/'+ans.new_result.user_photo+'.jpg');
						firstItem.find('button').data('date-time-create-text',ans.new_result.date_time_create_text);
						firstItem.find('button').data('price-text-short',ans.new_result.price_text_short);
						firstItem.find('button').data('currency',ans.new_result.currency);
						firstItem.find('button').data('currency-icon','assets/'+globalFolder+'/images/svg/payment/'+currencyInfo.config.data.icon+'.svg');
						firstItem.find('button').data('winner-amount',ans.new_result.winner_amount);
						firstItem.find('button').data('winner-amount-text-short',ans.new_result.winner_amount_text_short);
						firstItem.find('button').data('winner-factor',ans.new_result.winner_factor);
						firstItem.find('button').data('info',ans.new_result.info);
						firstItem.find('button').data('result',ans.new_result.result);
						
					}
				}
			}
			if (ans.new_seed) {
				modal = $('#modalGameActiveFair');
				modal.find('.modalGameActiveFairUrlInput').val(ans.new_seed.full_password_url);
				modal.find('.modalGameActiveFairUrlButtonDownload').attr('href',ans.new_seed.password_url).removeClass('d-none');
				modal.find('.modalGameActiveFairSeedIdInput').val(ans.new_seed.id);
				modal.find('.modalGameActiveFairSeedIdCopy').data('clipboard-text',ans.new_seed.seed);
				modal.find('.modalGameActiveFairSeedIdCopy').attr('data-clipboard-text',ans.new_seed.seed);
				modal.find('.modalGameActiveFairNonceInput').val(ans.new_seed.nonce);
				modal.find('.modalGameActiveFairNonceCopy').data('clipboard-text',ans.new_seed.nonce);
				modal.find('.modalGameActiveFairNonceCopy').attr('data-clipboard-text',ans.new_seed.nonce);
				modal.find('.modalGameActiveFairRotateInfo').removeClass('d-none');
				modal.find('.modalGameActiveFairRotateButton').addClass('d-none');
			}
			if (ans.user_info) {
				if (ans.user_info.total_balance_usd_text) {
					$('.userInfoBalanceTotalUsdText').html(ans.user_info.total_balance_usd_text);
				}
				if (ans.user_info) {
					$.each(ans.user_info.balances_text,function(index, value) {
						$('.userInfoBalanceText[data-currency="'+index+'"]').html(value);
					})
					$.each(ans.user_info.balances,function(index, value) {
						$('.userInfoBalance[data-currency="'+index+'"]').html(value);
					})
				}
			}
			if (ans.new_payment_not_confirmed) {
				toastr["info"](langNewPaymentNotConfirmed.replace('[amount]',ans.new_payment_not_confirmed.amount_text));
			}
			if (ans.new_payment_confirmed) {
				toastr["success"](langNewPaymentConfirmed.replace('[amount]',ans.new_payment_confirmed.amount_text));
			}
		}
		
		function getDepositAddress(payment,currency,block) {
			block.find('textarea[name=wallet]').html(block.find('textarea[name=wallet]').data('waiting'));
			block.find('input[name=tag]').val('');
			block.find('input[name=tag]').closest('.field-block').addClass('d-none');
			startProgress();
			$.ajax({
			  	url: "/get_address/",
			  	type: "POST",
			  	data: "payment="+payment+"&currency="+currency,
			  	error: function() {
				  	
			  	},
			  	success: function (responseText) {
				  	if (isJson(responseText)){
					  	stopProgress();
					  	ans = JSON.parse(responseText);
					  	if (ans.wallet) {
						  	block.find('textarea[name=wallet]').html(ans.wallet);
						  	blockFieldWallet = block.find('textarea[name=wallet]').closest('.field-block');
						  	blockFieldWallet.find('.copy-field-btn').data('clipboard-text',ans.wallet);
						  	blockFieldWallet.find('.copy-field-btn').attr('data-clipboard-text',ans.wallet);
					  	}
					  	if (ans.tag) {
						  	block.find('input[name=tag]').val(ans.tag);
						  	blockFieldTag = block.find('input[name=tag]').closest('.field-block');
						  	blockFieldTag.find('.copy-field-btn').data('clipboard-text',ans.tag);
						  	blockFieldTag.find('.copy-field-btn').attr('data-clipboard-text',ans.tag);
						  	blockFieldTag.removeClass('d-none');
					  	}
					  	currencyInfo = globalCurrencies[currency];
					  	paymentInfo = globalPayments[payment];
					  	networkName = null;
					  	switch(paymentInfo.network) {
						  	case 'BEP-20':
						  	case 'BSC':
						  	networkName = 'BSC';
						  	break;
						  	case 'XRP':
						  	networkName = 'XRP';
						  	break;
						  	case 'TRX':
						  	case 'TRC-20':
						  	networkName = 'TRON';
						  	break;
						  	case 'POL':
						  	networkName = 'POL';
						  	break;
						  	case 'DOGE':
						  	networkName = 'Dogecoin';
						  	break;
						  	case 'LTC':
						  	networkName = 'Litecoin';
						  	break;
						  	case 'SOL':
						  	networkName = 'Solana';
						  	break;
						  	case 'ETH':
						  	case 'ERC-20':
						  	networkName = 'Ethereum';
						  	break;
						  	case 'BTC':
						  	networkName = 'Bitcoin';
						  	break;
					  	}
					  	sendOnlyNetworkInfo = block.find('.sendOnlyNetworkInfo').data('message');
					  	sendOnlyNetworkInfo = sendOnlyNetworkInfo.replace('[currency]','<strong>'+currencyInfo.currency.toUpperCase()+'</strong>');
					  	sendOnlyNetworkInfo = sendOnlyNetworkInfo.replace('[network]','<strong>'+networkName+'</strong>');
					  	block.find('.sendOnlyNetworkInfo .field-message__text').html(sendOnlyNetworkInfo);
					  	if (networkName)
					  	block.find('.sendOnlyNetworkInfo').removeClass('d-none');
					  	else
					  	block.find('.sendOnlyNetworkInfo').addClass('d-none');
					  	
					  	sendConfirmations = block.find('.sendConfirmations').data('message');
					  	sendConfirmations = sendConfirmations.replace('[conf]','<strong>'+paymentInfo.confirmations+'</strong>');
					  	block.find('.sendConfirmations .field-message__text').html(sendConfirmations);
					  	if (paymentInfo.confirmations)
					  	block.find('.sendConfirmations').removeClass('d-none');
					  	else
					  	block.find('.sendConfirmations').addClass('d-none');
					  	
					}
					
				}
			});
		}
		
		function block2FACodeToInput(block) {
			realInput = block.find('input[type="hidden"]');
			inputs = block.find('.block2FACodeInputs input');
			realValue = '';
			inputs.each(function() {
				realValue = realValue + $(this).val();
			})
			realInput.val(realValue);
		}
		
		function block2FACode(block) {
			realInput = block.find('input[type="hidden"]');
			inputs = block.find('.block2FACodeInputs input');
			inputsLength = inputs.length;
			inputs.each(function(index) {
				$(this).on('keydown',function() {
					$(this).data('old-value',$(this).val());
				});
				if (index == 0) {
					$(this).on('keyup',function() {
						if ($(this).val().length >= $(this).attr('maxlength')) {
							inputs.eq(index + 1).focus();
						}
						block2FACodeToInput(block);
					})
				} else if (index == inputsLength - 1) {
					$(this).on('keyup',function() {
						if ($(this).val().length < $(this).attr('maxlength')) {
							if (!$(this).data('old-value').length) {
								inputs.eq(index - 1).val('');
								inputs.eq(index - 1).focus();
							}
						}
						block2FACodeToInput(block);
					})
				} else {
					$(this).on('keyup',function() {
						if ($(this).val().length >= $(this).attr('maxlength')) {
							inputs.eq(index + 1).focus();
						} else {
							if (!$(this).data('old-value').length) {
								inputs.eq(index - 1).val('');
								inputs.eq(index - 1).focus();
							}
						}
						block2FACodeToInput(block);
					})
				}
				
			})
		}
		
		function updateWallets(wallets_and_tags) {
			$('.walletItem').addClass('d-none');
			$('.walletItemWallet').html('');
			$('.walletItemTag').html('');
			addWalletAvailable = false;
			$.each(wallets_and_tags,function(index,wt) {
				if (wt.wallet) {
					$('.walletItem[data-payment="'+index+'"]').removeClass('d-none');
					$('.walletItem[data-payment="'+index+'"]').find('.walletItemWallet').html(wt.wallet);
					if (wt.tag) {
						$('.walletItem[data-payment="'+index+'"]').find('.walletItemTag').html(wt.tag);
					} else {
						$('.walletItem[data-payment="'+index+'"]').find('.walletItemTag').html("");
					}
				} else {
					addWalletAvailable = true;
				}
			})
			if (addWalletAvailable) {
				$(".walletAdd").removeClass('d-none');
			} else {
				$(".walletAdd").addClass('d-none');
			}
		}
		
		function setValueSelectPicker(select,value) {
			select.val(value);
			select.selectpicker('destroy');
			select.selectpicker();
		}
		
		function prepareTopupBlock(block, firstCurrency = null ) {
				firstPayment = null;
				availableCurrencies = [];
				block.find('select[name=payment] option').each(function() {
					if($.inArray($(this).data('currency'), availableCurrencies) != -1) {
							
					} else {
						availableCurrencies.push($(this).data('currency'));
					}
					if (!firstPayment) {
						firstPayment = $(this).val();
					}
				});
				if (firstPayment) {
					firstPaymentInfo = globalPayments[firstPayment];
					block.find('select[name=currency] option').each(function() {
						if ( $.inArray($(this).val(),availableCurrencies) != -1 ) {
							$(this).removeAttr('disabled');
							if (!firstCurrency) {
								firstCurrency = $(this).val();
							}
						} else {
							$(this).attr('disabled',true);
						}
					})
					setValueSelectPicker(block.find('select[name=currency]'),firstCurrency);
					newPayment = changePaymentsByCurrency(block,firstCurrency);
					getDepositAddress(newPayment,firstCurrency,block);
				} else {
					return false;
				}
		}
		
		function prepareWithdrawalBlock(block) {
				firstPayment = null;
				availableCurrencies = [];
				block.find('select[name=payment] option').each(function() {
					if($.inArray($(this).data('currency'), availableCurrencies) != -1) {
							
					} else {
						availableCurrencies.push($(this).data('currency'));
					}
					if (!firstPayment) {
						firstPayment = $(this).val();
					}
				});
				if (firstPayment) {
					firstPaymentInfo = globalPayments[firstPayment];
					firstCurrency = null;
					block.find('select[name=currency] option').each(function() {
						if ( $.inArray($(this).val(),availableCurrencies) != -1 ) {
							$(this).removeAttr('disabled');
							if (!firstCurrency) {
								firstCurrency = $(this).val();
							}
						} else {
							$(this).attr('disabled',true);
						}
					})
					setValueSelectPicker(block.find('select[name=currency]'),firstCurrency);
					newPayment = changePaymentsByCurrency(block,firstCurrency);
					changeWalletForWithdrawal(block,newPayment);
				} else {
					return false;
				}
		}
		
		function prepareWalletForm(editPayment = null) {
			modal = $('#modalAddWallet');
			form = modal.find('form');
			beforeSubmitForm(form,true);
			form.find('input[name=wallet]').val("");
			form.find('input[name=tag]').val("");
			
			if (editPayment) {
				modal.find('.modal-title').html(modal.find('.modal-title').data('title-edit'));
				form.find('select[name=payment_old]').attr('name','payment');
				form.find('input[name=payment]').attr('name','payment_old');
				editPaymentInfo = globalPayments[editPayment];
				form.find('select[name=currency]').attr('disabled',true);
				setValueSelectPicker(form.find('select[name=currency]'),editPaymentInfo.currency);
				form.find('select[name=payment]').attr('disabled',true);
				setValueSelectPicker(form.find('select[name=payment]'),editPayment);
				form.find('input[name=wallet]').val($('.walletItem[data-payment="'+editPayment+'"] .walletItemWallet').html());
				form.find('input[name=tag]').val($('.walletItem[data-payment="'+editPayment+'"] .walletItemTag').html());
				form.find('select[name=payment]').attr('name','payment_old');
				form.find('input[name=payment_old]').attr('name','payment').val(editPayment);
				selectedPaymentInfo = globalPayments[form.find('input[name=payment]').val()];
				if (selectedPaymentInfo.need_tag) {
					form.find('input[name=tag]').closest('.field-block').removeClass('d-none');
				} else {
					form.find('input[name=tag]').closest('.field-block').addClass('d-none');
				}
				$('#modalAddWallet').modal('show');
			} else {
				modal.find('.modal-title').html(modal.find('.modal-title').data('title-add'));
				form.find('select[name=payment_old]').attr('name','payment');
				form.find('input[name=payment]').attr('name','payment_old');
				form.find('select[name=currency]').removeAttr('disabled');
				form.find('select[name=payment]').removeAttr('disabled');
				form.find('input[name=wallet]').val();
				form.find('input[name=tag]').val();
				firstPayment = null;
				availableCurrencies = [];
				form.find('select[name=payment] option').each(function() {
					if (!$('.walletItem[data-payment="'+$(this).val()+'"] .walletItemWallet').html() ) {
						if($.inArray($(this).data('currency'), availableCurrencies) != -1) {
							
						} else {
							availableCurrencies.push($(this).data('currency'));
						}
						if (!firstPayment) {
							firstPayment = $(this).val();
						}
					}
				});
				if (firstPayment) {
					firstPaymentInfo = globalPayments[firstPayment];
					firstCurrency = null;
					form.find('select[name=currency] option').each(function() {
						if ( $.inArray($(this).val(),availableCurrencies) != -1 ) {
							$(this).removeAttr('disabled');
							if (!firstCurrency) {
								firstCurrency = $(this).val();
							}
						} else {
							$(this).attr('disabled',true);
						}
					})
					setValueSelectPicker(form.find('select[name=currency]'),firstCurrency);
					changePaymentsByCurrency(form,firstCurrency);
					modal.modal('show');
				} else {
					return false;
				}
				
			}
			
		}
		
		function changePaymentsByCurrency(form, newCurrency = null) {
			if (!newCurrency) {
				newCurrency = form.find('select[name=currency]').val();
			}
			firstPaymentIsSelected = false;
			form.find('select[name=payment] option').each(function( index ) {
				if ($(this).data('currency') == newCurrency) {
					if (form.attr('action') == '/change_wallets/') {
						if ($('.walletItem[data-payment="'+$(this).val()+'"] .walletItemWallet').html()) {
							$(this).attr('disabled',true);
						} else {
							$(this).removeAttr('disabled');
							if (!firstPaymentIsSelected) {
								form.find('select[name=payment]').val($(this).val());
								firstPaymentIsSelected = true;
							}
						}
					} else {
						$(this).removeAttr('disabled');
						if (!firstPaymentIsSelected) {
							form.find('select[name=payment]').val($(this).val());
							firstPaymentIsSelected = true;
						}
					}
					
				} else {
					$(this).attr('disabled',true);
				}
			});
			form.find('select[name=payment]').selectpicker('destroy');
			form.find('select[name=payment]').selectpicker();
			if (form.attr('action') == '/change_wallets/' || form.attr('action') == '/withdraw/' || form.hasClass('blockTopupBalance')) {
				selectedPaymentInfo = globalPayments[form.find('select[name=payment]').val()];
				if (selectedPaymentInfo.need_tag) {
					form.find('input[name=tag]').closest('.field-block').removeClass('d-none');
				} else {
					form.find('input[name=tag]').closest('.field-block').addClass('d-none');
				}
			}
			currencyInfo = globalCurrencies[newCurrency];
			if (form.attr('action') == '/withdraw/') {
				form.find('.blockWithdrawBalanceCurrency').html(currencyInfo.currency.toUpperCase());
				calcWithdraw(form);
			}
			
			return form.find('select[name=payment]').val();
		}
		
		function changeWalletForWithdrawal(form, payment) {
			form.find('input[name=amount]').val('');
			if (form.find('input[name=wallet]').data(payment+'-wallet')) {
				form.find('input[name=wallet]').val(form.find('input[name=wallet]').data(payment+'-wallet'));
				form.find('input[name=wallet]').attr('readonly',true);
			} else {
				form.find('input[name=wallet]').val('');
				form.find('input[name=wallet]').removeAttr('readonly');
			}
			if (form.find('input[name=tag]').data(payment+'-tag')) {
				form.find('input[name=tag]').val(form.find('input[name=tag]').data(payment+'-tag'));
				form.find('input[name=tag]').attr('readonly',true);
			} else {
				form.find('input[name=tag]').val('');
				form.find('input[name=tag]').removeAttr('readonly');
			}
		}
		
		
		$(document).ready(function() {
			
			var getUrlParameter = function getUrlParameter(sParam) {
			    var sPageURL = window.location.search.substring(1),
			        sURLVariables = sPageURL.split('&'),
			        sParameterName,
			        i;
			
			    for (i = 0; i < sURLVariables.length; i++) {
			        sParameterName = sURLVariables[i].split('=');
			
			        if (sParameterName[0] === sParam) {
			            return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
			        }
			    }
			    return false;
			};
			
			if ($('#devicesPagination').length) {
				if (getUrlParameter('page') > 0) {
					$('#devicesTab:first').click();
				}
			}
			
			if ($('.blockTopupBalance').length) {
				$('.blockTopupBalance').each(function() {
					block = $(this);
					prepareTopupBlock(block);
					$(this).find('select[name=currency]').on('change',function() {
						block = $(this).closest('.blockTopupBalance');
						newPayment = changePaymentsByCurrency(block, $(this).val());
						getDepositAddress(newPayment,$(this).val(),block);
					});
					$(this).find('select[name=payment]').on('change',function() {
						block = $(this).closest('.blockTopupBalance');
						getDepositAddress($(this).val(),block.find('select[name=currency]').val(),block);
					})
				})
			}
			
			if ($('.blockWithdrawBalance').length) {
				$('.blockWithdrawBalance').each(function() {
					block = $(this);
					prepareWithdrawalBlock(block);
					$(this).find('select[name=currency]').on('change',function() {
						block = $(this).closest('.blockWithdrawBalance');
						newPayment = changePaymentsByCurrency(block, $(this).val());
						changeWalletForWithdrawal(block,newPayment);
					});
					$(this).find('select[name=payment]').on('change',function() {
						block = $(this).closest('.blockWithdrawBalance');
						changeWalletForWithdrawal(block,$(this).val());
						calcWithdraw(block);
					})
					$(this).find('input[name=amount]').on('change',function() {
						block = $(this).closest('.blockWithdrawBalance');
						calcWithdraw(block);
					})
					$(this).find('input[name=amount]').on('keyup',function() {
						block = $(this).closest('.blockWithdrawBalance');
						calcWithdraw(block);
					})
				})
			}
			
			if ($('#modalAddWallet form').length) {
				$('#modalAddWallet form').find('select[name=currency]').on('change',function() {
					changePaymentsByCurrency($('#modalAddWallet form'), $(this).val());
				})
			}
			/*
			if ($('.block2FACode').length) {
				$('.block2FACode').each(function() {
					block2FACode($(this));
				})
			}
			*/
			if ($('#sectionCreateLottery').length) {
				$('#sectionCreateLottery select[name="currency"]').on('change',function() {
					sectionCreateLottery($('#sectionCreateLottery'),true, $(this).val());
				});
				$('#sectionCreateLottery input[name="ticket_price"]').on('keyup',function() {
					sectionCreateLottery($('#sectionCreateLottery'));
				});
				$('#sectionCreateLottery input[name="ticket_price"]').on('change',function() {
					sectionCreateLottery($('#sectionCreateLottery'));
				});
				$('#sectionCreateLottery input[name="tickets"]').on('keyup',function() {
					sectionCreateLottery($('#sectionCreateLottery'));
				});
				$('#sectionCreateLottery input[name="tickets"]').on('change',function() {
					sectionCreateLottery($('#sectionCreateLottery'));
				});
		        sectionCreateLottery($('#sectionCreateLottery'));
		    }
		    
		    
		    if ($('#sectionLotteries').length) {
			    
				$('#sectionLotteries select[name=currency]').on('change',function() {
					$('#sectionLotteries').find('input[name="price_from"]').val('');
					$('#sectionLotteries').find('input[name="price_to"]').val('');
					$('#sectionLotteries').find('input[name="winner_amount_from"]').val('');
					sectionLotteries($('#sectionLotteries'));
				});
				sectionLotteriesPriceFrom = '';
				sectionLotteriesPriceTo = '';
				sectionLotteriesWinnerAmountFrom = '';
				$('#sectionLotteries input[name="price_from"]').on('keyup',function() {
					if ($(this).val() != sectionLotteriesPriceFrom) {
						sectionLotteriesPriceFrom = $(this).val();
						sectionLotteries($('#sectionLotteries'));
					}
				});
				$('#sectionLotteries input[name="price_from"]').on('change',function() {
					if ($(this).val() != sectionLotteriesPriceFrom) {
						sectionLotteriesPriceFrom = $(this).val();
						sectionLotteries($('#sectionLotteries'));
					}
				});
				$('#sectionLotteries input[name="price_to"]').on('keyup',function() {
					if ($(this).val() != sectionLotteriesPriceTo) {
						sectionLotteriesPriceTo = $(this).val();
						sectionLotteries($('#sectionLotteries'));
					}
				});
				$('#sectionLotteries input[name="price_to"]').on('change',function() {
					if ($(this).val() != sectionLotteriesPriceTo) {
						sectionLotteriesPriceTo = $(this).val();
						sectionLotteries($('#sectionLotteries'));
					}
				});
				$('#sectionLotteries input[name="winner_amount_from"]').on('keyup',function() {
					if ($(this).val() != sectionLotteriesWinnerAmountFrom) {
						sectionLotteriesWinnerAmountFrom = $(this).val();
						sectionLotteries($('#sectionLotteries'));
					}
				});
				$('#sectionLotteries input[name="winner_amount_from"]').on('change',function() {
					if ($(this).val() != sectionLotteriesWinnerAmountFrom) {
						sectionLotteriesWinnerAmountFrom = $(this).val();
						sectionLotteries($('#sectionLotteries'));
					}
				});
				$('#sectionLotteries input[name="participant"]').on('change',function() {
					sectionLotteries($('#sectionLotteries'));
				});
				$('#sectionLotteries input[name="only_active"]').on('change',function() {
					sectionLotteries($('#sectionLotteries'));
				});
				$('#sectionLotteries input[name="only_my"]').on('change',function() {
					sectionLotteries($('#sectionLotteries'));
				});
				
				$('#sectionLotteries .sectionLotteriesResetFilter').on('click',function() {
					$('#sectionLotteries select[name="currency"]').selectpicker('destroy');
					$('#sectionLotteries select[name="currency"]').val('');
					$('#sectionLotteries select[name="currency"]').selectpicker();
					$('#sectionLotteries input[name="price_from"]').val('');
					$('#sectionLotteries input[name="price_to"]').val('');
					$('#sectionLotteries input[name="winner_amount_from"]').val('');
					$('#sectionLotteries input[name="participant"]').prop('checked',false);
					$('#sectionLotteries input[name="only_active"]').prop('checked',false);
					sectionLotteries($('#sectionLotteries'));
				})
				
				$('#sectionLotteries .sectionLotteriesItemsShowMoreButton').on('click',function() {
					sectionLotteries($('#sectionLotteries'),true);
				})
				
		        sectionLotteries($('#sectionLotteries'));
		    }
		    
		    if ($('.sectionGamesTable').length) {
			    
			    $('.sectionGamesNotTop select[name="limit"]').on('change',function() {
					initSectionsGames();
				});
				$('.sectionGamesTab').on('click',function() {
					initSectionsGames();
				})
				$('.sectionGamesTopTab').on('click',function() {
					initSectionsGames(true);
				})
				$('.sectionGamesTopArrow').on('click',function() {
					if (!$(this).data('opened')) {
						$(this).data('opened',1);
						sectionGames($('.sectionGamesOnlyTop .sectionGamesTable:first'));
					}
				})
				initSectionsGames();
		    }
		    
		    if ($('#sectionGameDice').length) {
			    
			    var diceRange = $('#sectionGameDice').find(".game-dice-slider").data("ionRangeSlider");
			    
				$('#sectionGameDice select[name="currency"]').on('change',function() {
					sectionGameDice($('#sectionGameDice'));
				});
				$('#sectionGameDice input[name="amount"]').on('keyup',function() {
					sectionGameDice($('#sectionGameDice'));
				});
				$('#sectionGameDice input[name="amount"]').on('change',function() {
					sectionGameDice($('#sectionGameDice'));
				});
				$('#sectionGameDice input[name="check_number"]').on('keyup',function() {
					sectionGameDice($('#sectionGameDice'));
				});
				$('#sectionGameDice input[name="check_number"]').on('change',function() {
					sectionGameDice($('#sectionGameDice'));
				});
				$('#sectionGameDice .sectionGameDiceRollChange').on('click',function() {
					if ($('#sectionGameDice').find('input[name=mode]').val() == 'less') {
						newMode = 'more';
					} else {
						newMode = 'less';
					}
					$('#sectionGameDice').find('input[name=mode]').val(newMode);
					$(this).closest('.field-block').find('.field-title').html($(this).closest('.field-block').find('.field-title').data('roll-'+newMode));
					checkNumber = $('#sectionGameDice').find('input[name=check_number]').val();
					newCheckNumber = BigNumber('100').minus(checkNumber);
					$('#sectionGameDice').find('input[name=check_number]').val(newCheckNumber);
					diceRange.update({
						from: parseFloat(newCheckNumber)
					});
					sectionGameDice($('#sectionGameDice'));
				});
				$('#sectionGameDice input[name="win_factor"]').on('keyup',function() {
					sectionGameDice($('#sectionGameDice'),false, true);
				});
				$('#sectionGameDice input[name="win_factor"]').on('change',function() {
					sectionGameDice($('#sectionGameDice'),true, true);
				});
				
				$('#sectionGameDice input[name="win_chance"]').on('keyup',function() {
					sectionGameDice($('#sectionGameDice'),false, false, true);
				});
				$('#sectionGameDice input[name="win_chance"]').on('change',function() {
					sectionGameDice($('#sectionGameDice'),true, false, true);
				});
				
		        sectionGameDice($('#sectionGameDice'));
		        initGameDiceSlider($('#sectionGameDice'));
		    }
		    
		    function initGameDiceSlider(block) {
			    
			    diceRange.update({
			        min: 0,
			        max: 100,
			        step: 1,
			        from: 50.5,
			        from_min: 2,
			        from_max: 98,
			        onChange: function (data) {
				        checkNumber = data.from;
				        block.find("input[name=check_number]").val(checkNumber);
						sectionGameDice(block);
			        }
			    });
		    }
		    
		    function sectionGameDice(block, isChanged = false, calcByWinFactor = false, calcByWinChance = false) {
			    beforeSubmitForm(block,true);
			    block.find('input[name=winner_amount]').val('');
			    
			    
			    currency = block.find('select[name=currency]').val();
				currencyInfo = globalCurrencies[currency];
				block.find('.sectionGameCurrencyImage').attr('src','assets/'+globalFolder+'/images/svg/payment/'+currencyInfo.config.data.icon+'.svg');
			    
			    
			    amount = getAmount(block.find('input[name=amount]').val());
			    
			    mode = block.find('input[name=mode]').val();
			    
			    if (mode == 'less') {
				    block.find('.irs-bar--single').css('background-color','#2ee57a');
				    block.find('.irs-line').css('background-color','#fc5757');
			    } else {
				    block.find('.irs-bar--single').css('background-color','#fc5757');
				    block.find('.irs-line').css('background-color','#2ee57a');
			    }
			    
			    if (calcByWinChance) {
				    block.find('input[name=check_number]').val('');
				    block.find('input[name=win_factor]').val('');
				    
				    winChance = getAmount(block.find('input[name=win_chance]').val());
					winChance = BigNumber(winChance).toFixed(2);
					
					if (isChanged) {
						block.find('input[name=win_chance]').val(winChance);
					}
					
					if (BigNumber(winChance).isLessThan('0.01') || BigNumber(winChance).isGreaterThan('98')) {
						input = block.find('input[name=win_chance]');
						field = input.closest('.field');
						fieldBlock = field.closest('.field-block');
						field.addClass('field-error');
						if (BigNumber(winChance).isGreaterThan('98')) {
							fieldBlock.append('<div class="field-message-block"><div class="field-message field-message--error"><div class="field-message__icon"></div><div class="field-message__text">' + langMaximumIs.replace('[number]', '98') + '</div></div></div>');
						} else {
	                    	fieldBlock.append('<div class="field-message-block"><div class="field-message field-message--error"><div class="field-message__icon"></div><div class="field-message__text">' + langMinimumIs.replace('[number]', '0.01') + '</div></div></div>');
	                    }
					} else {
						if (mode == 'less') {
							checkNumber = winChance;
							winFactor = BigNumber('99').dividedBy(checkNumber);
						} else {
							checkNumber = BigNumber('100').minus(winChance);
							winFactor = BigNumber('99').dividedBy(BigNumber('100').minus(checkNumber));
						}
						
						winFactor = BigNumber(winFactor).toFixed(4);
						checkNumber = BigNumber(checkNumber).toFixed(2);
						winnerAmount = BigNumber(amount).multipliedBy(winFactor).minus(amount);
					
						block.find('input[name=win_factor]').val(convert(parseFloat(winFactor)));
						block.find('input[name=check_number]').val(convert(parseFloat(checkNumber)));
						block.find('input[name=winner_amount]').val(convert(parseFloat(winnerAmount)));
						
						diceRange.update({
							from: parseFloat(checkNumber)
						});
						if (isChanged)
						sectionGameDice(block);
					}
				    
					
			    } else if (calcByWinFactor) {
				    block.find('input[name=check_number]').val('');
				    block.find('input[name=win_chance]').val('');
				    
				    winFactor = getAmount(block.find('input[name=win_factor]').val());
					winFactor = BigNumber(winFactor).toFixed(4);
					
					if (isChanged) {
						block.find('input[name=win_factor]').val(winFactor);
					}
					
					if (BigNumber(winFactor).isLessThan('1.0102') || BigNumber(winFactor).isGreaterThan('9900')) {
						input = block.find('input[name=win_factor]');
						field = input.closest('.field');
						fieldBlock = field.closest('.field-block');
						field.addClass('field-error');
						if (BigNumber(winFactor).isGreaterThan('9900')) {
							fieldBlock.append('<div class="field-message-block"><div class="field-message field-message--error"><div class="field-message__icon"></div><div class="field-message__text">' + langMaximumIs.replace('[number]', '9900') + '</div></div></div>');
						} else {
	                    	fieldBlock.append('<div class="field-message-block"><div class="field-message field-message--error"><div class="field-message__icon"></div><div class="field-message__text">' + langMinimumIs.replace('[number]', '1.0102') + '</div></div></div>');
	                    }
					} else {
						if (mode == 'less') {
							checkNumber = BigNumber('99').dividedBy(winFactor).toFixed(2);
							winChance = checkNumber;
						} else {
							checkNumber = BigNumber('100').minus(BigNumber('99').dividedBy(winFactor)).toFixed(2);
							winChance = BigNumber('100').minus(checkNumber);
						}
						
						winChance = BigNumber(winChance).toFixed(4);
						winnerAmount = BigNumber(amount).multipliedBy(winFactor).minus(amount);
						
						block.find('input[name=win_chance]').val(convert(parseFloat(winChance)));
						block.find('input[name=check_number]').val(convert(parseFloat(checkNumber)));
						block.find('input[name=winner_amount]').val(convert(parseFloat(winnerAmount)));
						
						diceRange.update({
							from: parseFloat(checkNumber)
						});
						if (isChanged)
						sectionGameDice(block);
					}
				    
					
			    } else {
				    block.find('input[name=win_factor]').val('');
				    
				    checkNumber = getAmount(block.find('input[name=check_number]').val());
					checkNumber = BigNumber(checkNumber).toFixed(2);
				    
				    if (mode == 'less') {
						winFactor = BigNumber('99').dividedBy(checkNumber);
						winChance = checkNumber;
					} else {
						winFactor = BigNumber('99').dividedBy(BigNumber('100').minus(checkNumber));
						winChance = BigNumber('100').minus(checkNumber);
					}
					winFactor = BigNumber(winFactor).toFixed(4);
					winChance = BigNumber(winChance).toFixed(4);
					winnerAmount = BigNumber(amount).multipliedBy(winFactor).minus(amount);
					
					block.find('input[name=win_factor]').val(convert(parseFloat(winFactor)));
					block.find('input[name=win_chance]').val(convert(parseFloat(winChance)));
					block.find('input[name=winner_amount]').val(convert(parseFloat(winnerAmount)));
			    }
			    
			    
				
				
		    }
		    
		    
		    
		    
		    if ($('#sectionGameLimbo').length) {
			    
				$('#sectionGameLimbo select[name="currency"]').on('change',function() {
					sectionGameLimbo($('#sectionGameLimbo'));
				});
				$('#sectionGameLimbo input[name="amount"]').on('keyup',function() {
					sectionGameLimbo($('#sectionGameLimbo'));
				});
				$('#sectionGameLimbo input[name="amount"]').on('change',function() {
					sectionGameLimbo($('#sectionGameLimbo'));
				});
				$('#sectionGameLimbo input[name="target_win_factor"]').on('keyup',function() {
					sectionGameLimbo($('#sectionGameLimbo'));
				});
				$('#sectionGameLimbo input[name="target_win_factor"]').on('change',function() {
					sectionGameLimbo($('#sectionGameLimbo'),true);
				});
				
				$('#sectionGameLimbo input[name="win_chance"]').on('keyup',function() {
					sectionGameLimbo($('#sectionGameLimbo'),false, true);
				});
				$('#sectionGameLimbo input[name="win_chance"]').on('change',function() {
					sectionGameLimbo($('#sectionGameLimbo'),true);
				});
				
		        sectionGameLimbo($('#sectionGameLimbo'));
		    }
		    
		    
		    
		    function sectionGameLimbo(block, isChanged = false, calcByWinChance = false) {
			    beforeSubmitForm(block,true);
			    block.find('input[name=winner_amount]').val('');
			    
			    
			    currency = block.find('select[name=currency]').val();
				currencyInfo = globalCurrencies[currency];
				block.find('.sectionGameCurrencyImage').attr('src','assets/'+globalFolder+'/images/svg/payment/'+currencyInfo.config.data.icon+'.svg');
			    
			    
			    amount = getAmount(block.find('input[name=amount]').val());
			    
			    
			    if (calcByWinChance) {
				    block.find('input[name=target_win_factor]').val('');
				    
				    winChance = getAmount(block.find('input[name=win_chance]').val());
					winChance = BigNumber(winChance).toFixed(8);
					
					if (isChanged) {
						block.find('input[name=win_chance]').val(winChance);
					}
					
					if (BigNumber(winChance).isLessThan('0.000099') || BigNumber(winChance).isGreaterThan('98.01980198')) {
						input = block.find('input[name=win_chance]');
						field = input.closest('.field');
						fieldBlock = field.closest('.field-block');
						field.addClass('field-error');
						if (BigNumber(winChance).isGreaterThan('98')) {
							fieldBlock.append('<div class="field-message-block"><div class="field-message field-message--error"><div class="field-message__icon"></div><div class="field-message__text">' + langMaximumIs.replace('[number]', '98.01980198') + '</div></div></div>');
						} else {
	                    	fieldBlock.append('<div class="field-message-block"><div class="field-message field-message--error"><div class="field-message__icon"></div><div class="field-message__text">' + langMinimumIs.replace('[number]', '0.000099') + '</div></div></div>');
	                    }
					} else {
						
						
						winChance = BigNumber(winChance).dividedBy('99');
						checkNumber = BigNumber('100000000000000').minus(BigNumber(winChance).multipliedBy('100000000000000'));
						targetWinFactor = BigNumber('100000000000000').dividedBy(BigNumber('100000000000000').minus(checkNumber)).toFixed(2);
						
						block.find('input[name=target_win_factor]').val(convert(parseFloat(targetWinFactor)));
						
						winnerAmount = BigNumber(amount).multipliedBy(targetWinFactor).minus(amount);
						block.find('input[name=winner_amount]').val(convert(parseFloat(winnerAmount)));
						
						
					}
				    
					
			    } else {
				    block.find('input[name=win_chance]').val('');
				    
				    targetWinFactor = getAmount(block.find('input[name=target_win_factor]').val());
					targetWinFactor = BigNumber(targetWinFactor).toFixed(2);
					
					if (isChanged) {
						block.find('input[name=target_win_factor]').val(targetWinFactor);
					}
					
					if (BigNumber(targetWinFactor).isLessThan('1.01') || BigNumber(targetWinFactor).isGreaterThan('1000000')) {
						input = block.find('input[name=target_win_factor]');
						field = input.closest('.field');
						fieldBlock = field.closest('.field-block');
						field.addClass('field-error');
						if (BigNumber(targetWinFactor).isGreaterThan('1000000')) {
							fieldBlock.append('<div class="field-message-block"><div class="field-message field-message--error"><div class="field-message__icon"></div><div class="field-message__text">' + langMaximumIs.replace('[number]', '1000000') + '</div></div></div>');
						} else {
	                    	fieldBlock.append('<div class="field-message-block"><div class="field-message field-message--error"><div class="field-message__icon"></div><div class="field-message__text">' + langMinimumIs.replace('[number]', '1.01') + '</div></div></div>');
	                    }
					} else {
						
						checkNumber = BigNumber('100000000000000').minus(BigNumber('100000000000000').dividedBy(targetWinFactor)); 
						winChance = BigNumber(BigNumber('100000000000000').minus(checkNumber)).dividedBy('100000000000000');
						winChance = BigNumber(winChance).multipliedBy('99').toFixed(8);
						
						
						block.find('input[name=win_chance]').val(convert(parseFloat(winChance)));
						
						winnerAmount = BigNumber(amount).multipliedBy(targetWinFactor).minus(amount);
						block.find('input[name=winner_amount]').val(convert(parseFloat(winnerAmount)));
						
						
					}
				    
					
			    }
			    
			    
				
				
		    }
			
			
	        if ($('.blockDeposit').length) {
		        
		        $('.blockDeposit').each(function() {
					block = $(this);
					block.find('input[name=currency]').on('change',function() {
						block = $(this).closest('.blockDeposit');
						prepareDepositBlock(block,true, $(this).val());
						calcDepositBlock(block);
					});
					
					block.find('select[name=currency]').on('change',function() {
						block = $(this).closest('.blockDeposit');
						if (block.find('#selectCalc').length) {
							setValueSelectPicker(block.find('#selectCalc'),$(this).val());
						}
						prepareDepositBlock(block,true, $(this).val());
						calcDepositBlock(block);
					});
					
					block.find('input[name=plan]').on('change',function() {
						block = $(this).closest('.blockDeposit');
						prepareDepositBlock(block,false, null, true);
						calcDepositBlock(block);
					});
					
					block.find('select[name=plan]').on('change',function() {
						block = $(this).closest('.blockDeposit');
						prepareDepositBlock(block,false, null, true);
						calcDepositBlock(block);
					});
					
					block.find('input[name=amount]').on('keyup',function() {
						block = $(this).closest('.blockDeposit');
						calcDepositBlock(block);
					});
					
					block.find('input[name=amount]').on('change',function() {
						block = $(this).closest('.blockDeposit');
						calcDepositBlock(block);
					});
			        
			        prepareDepositBlock(block);
			        calcDepositBlock(block);
			        
		        })
				
				if ($('#selectCalc').length) {
					$('#selectCalc').on('change',function() {
						block = $('.blockDeposit:first');
						setValueSelectPicker(block.find('select[name=currency]'),$(this).val());
						prepareDepositBlock(block,true, $(this).val());
						calcDepositBlock(block);
					})
					
				}
		        
		        
	        }
	        
	        if ($('form[action="/photo/"]').length) {
		        $('form[action="/photo/"]').on('submit',function() {
			        startProgress();
			        $.ajax({
					  	url: $(this).attr('action'),
					  	type: "POST",
					  	data: new FormData(this),
					  	cache:false,
					  	contentType: false,
					  	processData: false,
					  	error: function() {
						  	//setTimeout(function() {form.submit()}, 1000);
					  	},
					  	success: function (responseText) {
						  	if (isJson(responseText)){
							  	stopProgress();
							  	ans = JSON.parse(responseText);
							  	if (ans.errors) {
						            errorsModalText = '';
						            $.each(ans.errors, function(index, value) {
						                errorsModalText = errorsModalText + '<p>'+value+'</p>';
						            });
						            if (errorsModalText) {
							            $('#modalError').find('.modal-info__descr').html(errorsModalText);
										$('#modalError').modal('show');
						            }
						        }
								
								if (ans.success) {
									$('#modalSuccess').find('.modal-info__descr').html(ans.success);
						            $('#modalSuccess').modal('show');
								}
								
							}
						}
					});
					return false;
		        })
	        }
	        
			
			if (!$.cookie("cookiesAccept")) {
				$('#modalCookie').css('display','block');
			}
		})
		
		
		function closeCookie() {
			$('#modalCookie').css('display','none');
		}
		
		function acceptCookie() {
			$.cookie("cookiesAccept", "1", { path: '/' });
			closeCookie();
		}
		
		function showFaq(button, faq) {
			hc = button.closest('.helpCenter');
			min = 1;
			max = 1000000;
			if (faq == 'games') {
				min = 1;
				max = 4;
			}
			if (faq == 'lottery') {
				min = 5;
				max = 10;
			}
			if (faq == 'staking') {
				min = 11;
				max = 13;
			}
			if (faq == 'affiliate') {
				min = 14;
				max = 22;
			}
			if (faq == 'dashboard') {
				min = 23;
				max = 37;
			}
			
			hc.find('.helpCenterItems').removeClass('d-none');
			hc.find('.helpCenterSections').addClass('d-none');
			hc.find('.helpCenterTitleSection').removeClass('d-none');
			hc.find('.helpCenterTitleSectionTitle').html(button.find('.faq-category-item__title').html());
			hc.find('.helpCenterTitleSectionText').html(button.find('.faq-category-item__descr').html());
			removeClassStartingWith(hc.find('.helpCenterTitleSection .faq-category-title'), "faq-category-title--");
			hc.find('.helpCenterItems .helpCenterItemsItem').removeClass('d-none');
			$.each(hc.find('.helpCenterItems .helpCenterItemsItem'),function() {
				if ($(this).data('id') < min || $(this).data('id') > max) {
					$(this).addClass('d-none');
				}
			})
			hc.find('.helpCenterTitleSection .faq-category-title').addClass("faq-category-title--"+faq);
			$([document.documentElement, document.body]).animate({
		        scrollTop: $(".helpCenterTitleSection").offset().top
		    }, 200);
		}
		
		function hideFaq(button) {
			hc = button.closest('.helpCenter');
			hc.find('.helpCenterItems').addClass('d-none');
			hc.find('.helpCenterSections').removeClass('d-none');
			hc.find('.helpCenterTitleSection').addClass('d-none');
			$([document.documentElement, document.body]).animate({
		        scrollTop: $(".helpCenterSections").offset().top
		    }, 200);
		}
		
		
		
		function prepareDepositBlock(form, currencyChanged = false, newCurrency = null, planChanged = false) {
			
			if (form.find('select[name=currency]').length) {
				currency = form.find('select[name=currency]').val();
			} else {
				currency = form.find('input[name=currency]:checked').val();
			}
			
			
			
			currencyInfo = globalCurrencies[currency];
			
			
			
			
			form.find('.blockDepositCurrencyInPlan').html(form.find('.blockDepositCurrencyInPlan').data(currency+'-min-html'));
			
			form.find('.blockDepositPlanMinAmount').each(function() {
				$(this).html($(this).data(currency+'-min-amount'));
			})
			
			amount = getAmount(form.find('input[name=amount]').val());
			if (currencyChanged) {
				changeAmount(form);
				amount = getAmount(form.find('input[name=amount]').val());
				form.find('input[name=old_currency]').val(currency);
			}
			
			form.find('.blockDepositCurrencyLabel').html(currency.toUpperCase());
			
			if (planChanged) {
				if (form.find('select[name=plan]').length) {
					percentInfo = globalPlans[form.find('select[name=plan]').val()]['percents_array'][0];
				} else {
					percentInfo = globalPlans[form.find('input[name=plan]:checked').val()]['percents_array'][0];
				}
				
				if (BigNumber(amount).isGreaterThan(percentInfo['min_max']['max_'+currency])) {
					form.find('input[name=amount]').val(percentInfo['min_max']['max_'+currency]);
				}
				if (BigNumber(amount).isLessThan(percentInfo['min_max']['min_'+currency])) {
					form.find('input[name=amount]').val(percentInfo['min_max']['min_'+currency]);
				}
			}
		}
		
		
		function calcDepositBlock(form) {
			
			if (form.find('select[name=currency]').length) {
				currency = form.find('select[name=currency]').val();
			} else {
				currency = form.find('input[name=currency]:checked').val();
			}
			
			
			currencyInfo = globalCurrencies[currency];
			
			amount = getAmount(form.find('input[name=amount]').val());
			
			if (form.find('select[name=plan]').length) {
				setValueSelectPicker(form.find('select[name=plan]'),'');
				form.find('select[name=plan] option').each(function(index,value) {
						planInfo = globalPlans[$(this).attr('value')];
						percentInfo = planInfo['percents_array'][0];
						if (BigNumber(amount).isGreaterThanOrEqualTo(percentInfo['min_max']['min_'+currency]) && BigNumber(amount).isLessThanOrEqualTo(percentInfo['min_max']['max_'+currency])) {
							setValueSelectPicker(form.find('select[name=plan]'),$(this).attr('value'));
							
						}
				});
				
				plan =  form.find('select[name=plan]').val();
			} else {
				
				form.find('input[name=plan]').prop('checked',false);
				form.find('input[name=plan]').each(function(index,value) {
						planInfo = globalPlans[$(this).val()];
						percentInfo = planInfo['percents_array'][0];
						
						if (BigNumber(amount).isGreaterThanOrEqualTo(percentInfo['min_max']['min_'+currency]) && BigNumber(amount).isLessThanOrEqualTo(percentInfo['min_max']['max_'+currency])) {
							$(this).prop('checked',true);
							
						}
				});
				
				plan =  form.find('input[name=plan]:checked').val();
				
			}
			
			
			
			res = calcInvestReturn(currency,amount,plan);
			
			
			
			if (res['calculated']) {
					planInfo = globalPlans[plan];
					dailyPercentAmount = BigNumber(res['max_percent_amount']).toFixed(currencyInfo['round']);
					totalReturnAmount = BigNumber(res['max_total_return_amount']).toFixed(currencyInfo['round']);
					totalReturnAmount = BigNumber(amount).multipliedBy('2').toFixed(currencyInfo['round']);
					form.find('.blockDepositDailyReturn').html(convert(parseFloat(dailyPercentAmount)) + ' ' + currencyInfo['currency'].toUpperCase());
					form.find('.blockDepositTotalReturn').html(convert(parseFloat(totalReturnAmount)) + ' ' + currencyInfo['currency'].toUpperCase());
					form.find('.blockDepositDuration').html(planInfo['param']+' '+form.find('.blockDepositDuration').data('days'));
					form.find('.blockDepositROI').html('100%');
					dailyCalcForAPR = BigNumber(BigNumber(100).dividedBy(planInfo['param'])).multipliedBy(365).toFixed(0);
					form.find('.blockDepositAPR').html(dailyCalcForAPR+'%');
				
			} else {
				
				form.find('.blockDepositDailyReturn').html(form.find('.blockDepositDailyReturn').data('no'));
				form.find('.blockDepositTotalReturn').html(form.find('.blockDepositTotalReturn').data('no'));
				form.find('.blockDepositDuration').html(form.find('.blockDepositDuration').data('no'));
				form.find('.blockDepositROI').html(form.find('.blockDepositROI').data('no'));
				form.find('.blockDepositAPR').html(form.find('.blockDepositAPR').data('no'));
				
			}
		}
		
		
		
		function calcWithdraw(form) {
			amount = getAmount(form.find('input[name=amount]').val());
			currency = form.find('select[name=currency]').val();
			payment = form.find('select[name=payment]').val();
			res = calcPayAmount('withdraw', currency, payment, amount);
			if (BigNumber(res.pay_amount).isGreaterThan('0')) {
				form.find('.blockWithdrawBalanceAmountForWithdrawal').html(res.pay_amount + ' ' + currency.toUpperCase());
			} else {
				form.find('.blockWithdrawBalanceAmountForWithdrawal').html('0 ' + currency.toUpperCase());
			}
			form.find('.blockWithdrawBalanceFee').html(res.total_fee + ' ' + currency.toUpperCase());
		}
		
		function changeAmount(form) {
			
			
			oldCurrency = form.find('input[name=old_currency]').val();
			
			if (form.find('select[name=currency]').length) {
				newCurrency = form.find('select[name=currency]').val();
			} else {
				newCurrency = form.find('input[name=currency]:checked').val();
			}
			
			
	        currencyInfo = globalCurrencies[oldCurrency];
	        amount = getAmount(form.find('input[name=amount]').val());
	        amount = BigNumber(amount).toFixed(currencyInfo['round_payment']);
	        amount = getAmount(amount);
	        newCurrencyInfo = globalCurrencies[newCurrency];
	        if (newCurrencyInfo['currency'] == currencyInfo['currency']) {
	            newAmount = amount;
	        } else if (currencyInfo['usd_equal']) {
	            rate = globalRates[newCurrencyInfo['currency']];
	            newAmount = BigNumber(amount).dividedBy(rate).toFixed(newCurrencyInfo['round_payment']);
	        } else if (newCurrencyInfo['usd_equal']) {
	            rate = globalRates[currencyInfo['currency']];
	            newAmount = BigNumber(amount).multipliedBy(rate).toFixed(newCurrencyInfo['round_payment']);
	        } else {
	            rate = globalRates[currencyInfo['currency']];
	            amountInUsd = BigNumber(amount).multipliedBy(rate).toFixed(8);
	            rate = globalRates[newCurrencyInfo['currency']];
	            newAmount = BigNumber(amountInUsd).dividedBy(rate).toFixed(newCurrencyInfo['round_payment']);
	        }
	        form.find('input[name=amount]').val(newAmount);
	    }
		
		
		
		
		function startProgress() {
	        if ($('.xProgress').length) {
	            progressEl = $('.xProgress');
	            progressElLine = progressEl.find('.xProgressPercent');
	            progressElLine.css('width', '0%');
	            progressEl.removeClass('d-none');
	            width = 0;
	            progress_id = setInterval(frameProgress, 10);
	            function frameProgress() {
	                if (width >= 100) {
	                    clearInterval(progress_id);
	                } else {
	                    width = width + 0.5;
	                    progressElLine.css('width', width + '%');
	                }
	            }
	        }
	    }
	
	    function stopProgress() {
	        if ($('.xProgress').length) {
	            clearInterval(progress_id);
	            progressEl.addClass('d-none');
	        }
	    }
	    
	    
	    function sectionCreateLottery(form) {
		    currency = form.find('select[name=currency]').val();
		    currencyInfo = globalCurrencies[currency];
		    currentTicketPriceTitle = form.find('.sectionCreateLotteryTicketPriceTitle').data('mask');
		    form.find('.sectionCreateLotteryTicketPriceTitle').html(currentTicketPriceTitle.replace('[currency]',currencyInfo.currency.toUpperCase()));
		    ticketPrice = getAmount(form.find('input[name=ticket_price]').val());
		    tickets = getCount(form.find('input[name=tickets]').val());
		    winningAmountBeforeFee = BigNumber(ticketPrice).multipliedBy(BigNumber(tickets)).toFixed(currencyInfo['round']);
		    winningAmount = BigNumber(winningAmountBeforeFee).multipliedBy('0.98').toFixed(currencyInfo['round']);
		    form.find('.sectionCreateLotteryWinningAmount').html(convert(parseFloat(winningAmount)));
		    form.find('.sectionCreateLotteryWinningAmountIcon').attr('src','assets/'+globalFolder+'/images/svg/payment/'+currencyInfo.config.data.icon+'.svg');
	    }
	    
	    function sectionLotteries(section, showMore = false) {
		    startProgress()
		    currency = section.find('select[name="currency"]').val();
		    currentFilterPriceTitle = section.find('.sectionLotteriesFilterPriceTitle').data('mask');
		    currentFilterWinningAmountTitle = section.find('.sectionLotteriesFilterWinningAmountTitle').data('mask');
		    if (currentFilterPriceTitle && currency) {
			    currencyInfo = globalCurrencies[currency];
			    section.find('.sectionLotteriesFilterPriceTitle').html(currentFilterPriceTitle.replace('[currency]',currencyInfo.currency.toUpperCase()));
			    section.find('.sectionLotteriesFilterWinningAmountTitle').html(currentFilterWinningAmountTitle.replace('[currency]',currencyInfo.currency.toUpperCase()));
		    } else if (currentFilterPriceTitle) {
			    section.find('.sectionLotteriesFilterPriceTitle').html(currentFilterPriceTitle.replace('[currency]','$'));
			    section.find('.sectionLotteriesFilterWinningAmountTitle').html(currentFilterWinningAmountTitle.replace('[currency]','$'));
		    }
		    
		    inputFilterPage = section.find('.sectionLotteriesFilter').find('input[name="page"]');
		    inputFilterLimit = section.find('.sectionLotteriesFilter').find('input[name="limit"]');
		    
		    section.find('.sectionLotteriesItemsShowMore').addClass('d-none');
		    section.find('.sectionLotteriesItemsSMEmpty').removeClass('d-none');
		    
		    if (showMore) {
			    inputFilterPage.val(parseInt(inputFilterPage.val()) + 1);
		    } else {
			    inputFilterPage.val(inputFilterPage.data('default'));
		    }
		    
		    $.ajax({
			  	url: "/lotteries/",
			  	type: "POST",
			  	data: section.find('.sectionLotteriesFilter').serialize(),
			  	error: function() {
				  	//setTimeout(function() {form.submit()}, 1000);
			  	},
			  	success: function (responseText) {
				  	if (isJson(responseText)){
					  	stopProgress();
					  	ans = JSON.parse(responseText);
					  	if (section.find('.swiper').length) {
						  	swiperMode = true;
					  	} else {
						  	swiperMode = false;
					  	}
					  	if (ans.lotteries) {
						  	section.find('.sectionLotteriesEmpty').addClass('d-none');
						  	
						  	lotteriesItemsHtml = '';
						  	$.each(ans.lotteries, function(index, lottery) {
							  	currencyInfo = globalCurrencies[lottery.currency];
							  	if (swiperMode) {
								  	lotteriesItemsHtml += '<div class="related-lottery-slide swiper-slide">';
							  	} else {
								  	lotteriesItemsHtml += '<div class="lottery-list-item-wrapper">';
							  	}
							  	
							  	
							  	lotteriesItemsHtml += '<a href="/lotteries/'+lottery.id+'/" class="lottery-list-item"><div class="lottery-list-item__inner">';
							  	if (lottery.date_end > 0) {
								  	
								  	
								  	
								  	lotteriesItemsHtml += '<div class="lottery-list-item__winner"><div class="lottery-list-item__winner__title">Winner</div><div class="lottery-list-item__winner__image-block"><div class="lottery-list-item__winner__image  openUserInfo" data-user-login="'+lottery.winner_login+'" data-from="lottery"><img class="image" src="assets/'+globalFolder+'/users/'+lottery.winner_photo+'.jpg" alt=""></div></div><div class="lottery-list-item__winner__name openUserInfo" data-user-login="'+lottery.winner_login+'" data-from="lottery">'+lottery.winner_login+'</div><div class="lottery-list-item__winner__tickets-block"><div class="lottery-list-item__winner__tickets"><div class="lottery-list-item__winner__ticket-wrapper"><div class="lottery-list-item__winner__ticket"><div class="lottery-list-item__winner__ticket__count">'+lottery.tickets_total+'</div><div class="lottery-list-item__winner__ticket__icon"></div><div class="lottery-list-item__winner__ticket__descr">Total</div></div></div><div class="lottery-list-item__winner__ticket-wrapper"><div class="lottery-list-item__winner__ticket lottery-list-item__winner__ticket--sale"><div class="lottery-list-item__winner__ticket__count">'+lottery.winner_ticket_id+'</div><div class="lottery-list-item__winner__ticket__icon"></div></div></div></div></div></div><div class="lottery-list-item__winner__participants-block"><div class="lottery-list-item__winner__participants"><div class="lottery-list-item__winner__participants__left"><div class="lottery-list-item__participants__images-block"><div class="lottery-list-item__participants__images">';
								  	
								  	$.each(lottery.tickets_preview_array, function(index2, preview) {
									  	lotteriesItemsHtml += '<div class="lottery-list-item__participants__image"><img class="image" src="assets/'+globalFolder+'/users/'+preview+'.jpg" alt=""></div>';
								  	})
								  	
								  	lotteriesItemsHtml += '</div></div></div><div class="lottery-list-item__winner__participants__right"><div class="lottery-list-item__winner__participants__title">Participants:</div><div class="lottery-list-item__winner__participants__count">'+lottery.participants+'</div></div></div></div><div class="lottery-list-item__winner__prize-block"><div class="lottery-list-item__winner__prize-title">Winning amount</div><div class="lottery-list-item__winner__prize"><div class="lottery-list-item__winner__prize__amount"><span>'+lottery.winner_amount_text_short+'</span></div><div class="lottery-list-item__winner__prize__currency"><img class="image" src="assets/'+globalFolder+'/images/svg/payment/'+currencyInfo.config.data.icon+'.svg" alt=""></div></div></div><div class="lottery-list-item__finished-block"><div class="lottery-list-item__finished"><div class="lottery-list-item__finished__left"><div class="lottery-list-item__finished__title">Finished</div></div><div class="lottery-list-item__finished__right"><div class="lottery-list-item__finished__date">'+lottery.date_end_text+'</div></div></div></div>';
								  	
								  	
								  	
							  	} else {
								  	
								  	lotteriesItemsHtml += '<div class="lottery-list-item__progress-block"><div class="lottery-list-item__progress"><div class="lottery-list-item__progress__progress" style="width: '+lottery.progress+'%;"></div></div></div>';
								  	
								  	lotteriesItemsHtml += '<div class="lottery-list-item__fund-block"><div class="lottery-list-item__fund"><div class="lottery-list-item__fund-left"><div class="lottery-list-item__fund-title">Winning amount</div></div><div class="lottery-list-item__fund-right"><div class="lottery-list-item__fund-amount">'+lottery.winner_amount_text_short+'</div><div class="lottery-list-item__fund-currency"><img class="image" src="assets/'+globalFolder+'/images/svg/payment/'+currencyInfo.config.data.icon+'.svg" alt=""></div></div></div></div>';
								  	
								  	lotteriesItemsHtml += '<div class="lottery-list-item__ticket-count-items-block"><div class="lottery-list-item__ticket-count-items"><div class="lottery-list-item__ticket-count-item-wrapper"><div class="lottery-list-item__ticket-count-item"><div class="lottery-list-item__ticket-count-item__icon"></div><div class="lottery-list-item__ticket-count-item__content"><div class="lottery-list-item__ticket-count-item__count">'+lottery.tickets_total+'</div><div class="lottery-list-item__ticket-count-item__descr">Total</div></div></div></div><div class="lottery-list-item__ticket-count-item-wrapper"><div class="lottery-list-item__ticket-count-item lottery-list-item__ticket-count-item--saled"><div class="lottery-list-item__ticket-count-item__icon"></div><div class="lottery-list-item__ticket-count-item__content"><div class="lottery-list-item__ticket-count-item__count">'+lottery.tickets_free+'</div><div class="lottery-list-item__ticket-count-item__descr">Available</div></div></div></div></div></div>';
								  	
								  	lotteriesItemsHtml += '<div class="lottery-list-item__participants-block"><div class="lottery-list-item__participants"><div class="lottery-list-item__participants__images-block"><div class="lottery-list-item__participants__images">';
								  	
								  	$.each(lottery.tickets_preview_array, function(index2, preview) {
									  	lotteriesItemsHtml += '<div class="lottery-list-item__participants__image"><img class="image" src="assets/'+globalFolder+'/users/'+preview+'.jpg" alt=""></div>';
								  	})
								  	
								  	lotteriesItemsHtml += '</div></div><div class="lottery-list-item__participants__title-block"><div class="lottery-list-item__participants__title">Participants: '+lottery.participants+'</div></div></div></div>';
								  	
								  	
							  	
								  	lotteriesItemsHtml += '<div class="lottery-list-item__price-block"><div class="lottery-list-item__price"><div class="lottery-list-item__price-left"><div class="lottery-list-item__price-title">Ticket price</div></div><div class="lottery-list-item__price-right"><div class="lottery-list-item__price-amount">'+lottery.price_text_short+'</div><div class="lottery-list-item__price-currency"><img class="image" src="assets/'+globalFolder+'/images/svg/payment/'+currencyInfo.config.data.icon+'.svg" alt=""></div></div></div></div>';
								  	
								  	lotteriesItemsHtml += '<div class="lottery-list-item__buy-btn-block"><div class="lottery-list-item__buy-btn iconed-btn purple-btn"><div class="iconed-btn__text">Buy tickets</div><div class="iconed-btn__icon"></div></div></div><div class="lottery-list-item__bottom-block"><div class="lottery-list-item__bottom"><div class="lottery-list-item__bottom-left"><div class="lottery-list-item__creator-title">Creator</div></div><div class="lottery-list-item__bottom-right"><div class="lottery-list-item__creator openUserInfo" data-user-login="'+lottery.user_login+'" data-from="lottery"><div class="lottery-list-item__creator__name">'+lottery.user_login+'</div><div class="lottery-list-item__creator__image"><img class="image" src="assets/'+globalFolder+'/users/'+lottery.user_photo+'.jpg" alt=""></div></div></div></div></div>';
								  	
								  	
							  	}
							  	
							  	
							  	lotteriesItemsHtml += '</div></a>';
							  	lotteriesItemsHtml += '</div>';
				            });
						  	
						  	if (swiperMode) {
							  	section.find('.sectionLotteriesItems').html(lotteriesItemsHtml);
							  	relSwiper.update();
						  	} else {
							  	if (showMore)
							  	section.find('.sectionLotteriesItems .lottery-list-items').html(section.find('.sectionLotteriesItems .lottery-list-items').html() + lotteriesItemsHtml);
							  	else
							  	section.find('.sectionLotteriesItems .lottery-list-items').html(lotteriesItemsHtml);
							  	
							  	section.find('.sectionLotteriesItems').removeClass('d-none');
							  	section.find('.sectionLotteriesEmpty').addClass('d-none');
							  	
							  	if ( ans.count > parseInt(section.find('.sectionLotteriesFilter').find('input[name="page"]').val()) * parseInt(section.find('.sectionLotteriesFilter').find('input[name="limit"]').val()) ) {
								  	section.find('.sectionLotteriesItemsShowMore').removeClass('d-none');
								  	section.find('.sectionLotteriesItemsSMEmpty').addClass('d-none');
							  	}
						  	}
						  	
						  	
					  	} else {
						  	if (!showMore) {
							  	section.find('.sectionLotteriesEmpty').removeClass('d-none');
							  	section.find('.sectionLotteriesItems').addClass('d-none').html('<div class="lottery-list-items"></div>');
						  	}
					  	}
					} else {
						//setTimeout(function() {form.submit()}, 1000);
					}
					return false;
			  }
			});
	    }
	    
	    function removeClassStartingWith(node, begin) {
		    node.removeClass (function (index, className) {
		        return (className.match ( new RegExp("\\b"+begin+"\\S+", "g") ) || []).join(' ');
		    });
		}
	    
	    
	    function sectionGames(section) {
		    limit = section.closest('.sectionGames').find('[name=limit]').val();
		    //startProgress();
		    $.ajax({
			  	url: "/games/",
			  	type: "POST",
			  	data: "limit="+limit+"&type="+section.find('input[name=type]').val()+"&game="+section.find('input[name=game]').val(),
			  	error: function() {
				  	//setTimeout(function() {form.submit()}, 1000);
			  	},
			  	success: function (responseText) {
				  	if (isJson(responseText)){
					  	//stopProgress();
					  	ans = JSON.parse(responseText);
					  	section.find('.sectionGamesTableGamesItem').addClass('d-none');
					  	if (ans.games) {
						  	$.each(ans.games,function(index,game) {
							  	currencyInfo = globalCurrencies[game.currency];
							  	block = section.find('.sectionGamesTableGamesItem').eq(index);
							  	block.removeClass('d-none');
							  	removeClassStartingWith(block.find('.sectionGamesTableGamesItemButton'), 'games-bet-t-i-game--');
							  	block.find('.sectionGamesTableGamesItemButton').addClass('games-bet-t-i-game--'+game.game);
							  	block.find('.sectionGamesTableGamesItemButton').data('bet-id',game.id);
							  	block.find('.sectionGamesTableGamesItemButton').data('game',game.game);
							  	if (game.game == 'limbo') {
									block.find('.sectionGamesTableGamesItemGame').html(langLimbo);
								} else {
									block.find('.sectionGamesTableGamesItemGame').html(langDice);
								}
							  	
							  	block.find('.sectionGamesTableGamesItemUserName').html(game.user_login);
							  	block.find('.sectionGamesTableGamesItemUser').data('user-login',game.user_login);
							  	block.find('.sectionGamesTableGamesItemButton').data('user-login',game.user_login);
							  	
							  	block.find('.sectionGamesTableGamesItemUserImage').attr('src','assets/'+globalFolder+'/users/'+game.user_photo+'.jpg');
							  	block.find('.sectionGamesTableGamesItemButton').data('user-photo','assets/'+globalFolder+'/users/'+game.user_photo+'.jpg');
							  	
							  	block.find('.sectionGamesTableGamesItemTime').html(game.time_create_text);
							  	block.find('.sectionGamesTableGamesItemDate').html(game.date_create_text);
							  	block.find('.sectionGamesTableGamesItemButton').data('date-time-create-text',game.date_time_create_text);
							  	
							  	block.find('.sectionGamesTableGamesItemPrice').html(game.price_text_short);
							  	block.find('.sectionGamesTableGamesItemButton').data('price-text-short',game.price_text_short);
							  	
							  	block.find('.sectionGamesTableGamesItemCurrencyImage').attr('src','assets/'+globalFolder+'/images/svg/payment/'+currencyInfo.config.data.icon+'.svg');
							  	block.find('.sectionGamesTableGamesItemButton').data('currency',game.currency);
							  	block.find('.sectionGamesTableGamesItemButton').data('currency-icon','assets/'+globalFolder+'/images/svg/payment/'+currencyInfo.config.data.icon+'.svg');
							  	
							  	block.find('.sectionGamesTableGamesItemWinnerAmountText').html(game.winner_amount_text_short);
							  	block.find('.sectionGamesTableGamesItemButton').data('winner-amount',game.winner_amount);
							  	block.find('.sectionGamesTableGamesItemButton').data('winner-amount-text-short',game.winner_amount_text_short);
							  	
							  	block.find('.sectionGamesTableGamesItemButton').data('winner-factor',game.winner_factor);
							  	
							  	block.find('.sectionGamesTableGamesItemWinnerAmount').removeClass('games-bet-t-i-amount--green');
							  	block.find('.sectionGamesTableGamesItemMultiplier').html('0.00x');
							  	if (BigNumber(game.winner_amount).isGreaterThan('0')) {
								  	block.find('.sectionGamesTableGamesItemWinnerAmount').addClass('games-bet-t-i-amount--green');
								  	block.find('.sectionGamesTableGamesItemMultiplier').html(BigNumber(game.winner_factor).toFixed(2)+'x');
							  	}
							  	
							  	block.find('.sectionGamesTableGamesItemButton').data('info',game.info);
							  	block.find('.sectionGamesTableGamesItemButton').data('result',game.result);
						  	})
					  	}
					  	if ($('.sectionGamesTableNotFound').length && section.find('input[name=type]').val() == 'my') {
						  	if (ans.games) {
							  	$('.sectionGamesTableNotFound').addClass('d-none');
							  	section.removeClass('d-none');
						  	} else {
							  	$('.sectionGamesTableNotFound').removeClass('d-none');
							  	section.addClass('d-none');
						  	}
					  	}
					} else {
						//setTimeout(function() {form.submit()}, 1000);
					}
					return false;
			  }
			});
	    }
	    
	    function initSectionsGames(bigs = false) {
		    if (bigs) {
			    $('.sectionGamesOnlyTop .sectionGamesTable:visible').each(function() {
					sectionGames($(this));
				});
		    } else {
			    $('.sectionGamesNotTop .sectionGamesTable:visible').each(function() {
					sectionGames($(this));
				});
		    }
		}
		
		
		

</body></html>