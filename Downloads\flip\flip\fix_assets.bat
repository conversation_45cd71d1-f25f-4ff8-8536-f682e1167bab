@echo off
echo Copying CSS files...

REM Copy main CSS files
copy "assets\cy\css\main.css_vs=100" "assets\cy\css\main.css"
copy "assets\cy\css\media.css_vs=100" "assets\cy\css\media.css"

REM Copy Bootstrap CSS
copy "assets\cy\libs\bootstrap\css\bootstrap.min.css_vs=100" "assets\cy\libs\bootstrap\css\bootstrap.min.css"

REM Copy other CSS files
copy "assets\cy\libs\priority-navigation-master\dist\priority-nav-core.css_vs=100" "assets\cy\libs\priority-navigation-master\dist\priority-nav-core.css"
copy "assets\cy\libs\swiper\swiper.min.css_vs=100" "assets\cy\libs\swiper\swiper.min.css"
copy "assets\cy\libs\bootstrap-select-1.14.0\dist\css\bootstrap-select.min.css_vs=100" "assets\cy\libs\bootstrap-select-1.14.0\dist\css\bootstrap-select.min.css"
copy "assets\cy\libs\plyr\dist\plyr.css_vs=100" "assets\cy\libs\plyr\dist\plyr.css"
copy "assets\cy\libs\ion.rangeSlider-master\css\ion.rangeSlider.min.css_vs=100" "assets\cy\libs\ion.rangeSlider-master\css\ion.rangeSlider.min.css"
copy "assets\cy\libs\toastr-master\build\toastr.min.css_vs=100" "assets\cy\libs\toastr-master\build\toastr.min.css"

echo Copying JS files...

REM Copy JavaScript files
copy "assets\cy\libs\priority-navigation-master\dist\priority-nav.min.js_vs=100" "assets\cy\libs\priority-navigation-master\dist\priority-nav.min.js"
copy "assets\cy\libs\jquery\jquery-3.6.0.min.js_vs=100" "assets\cy\libs\jquery\jquery-3.6.0.min.js"
copy "assets\cy\libs\autosize\autosize.min.js_vs=100" "assets\cy\libs\autosize\autosize.min.js"
copy "assets\cy\libs\swiper\swiper-bundle.min.js_vs=100" "assets\cy\libs\swiper\swiper-bundle.min.js"
copy "assets\cy\libs\bootstrap\js\bootstrap.bundle.min.js_vs=100" "assets\cy\libs\bootstrap\js\bootstrap.bundle.min.js"
copy "assets\cy\libs\bootstrap-select-1.14.0\dist\js\bootstrap-select.min.js_vs=100" "assets\cy\libs\bootstrap-select-1.14.0\dist\js\bootstrap-select.min.js"
copy "assets\cy\libs\bootstrap-select-1.14.0\dist\js\i18n\defaults-en_US.min.js_vs=100" "assets\cy\libs\bootstrap-select-1.14.0\dist\js\i18n\defaults-en_US.min.js"
copy "assets\cy\libs\clipboard.js-master\dist\clipboard.min.js_vs=100" "assets\cy\libs\clipboard.js-master\dist\clipboard.min.js"
copy "assets\cy\libs\ion.rangeSlider-master\js\ion.rangeSlider.min.js_vs=100" "assets\cy\libs\ion.rangeSlider-master\js\ion.rangeSlider.min.js"
copy "assets\cy\libs\plyr\dist\plyr.polyfilled.min.js_vs=100" "assets\cy\libs\plyr\dist\plyr.polyfilled.min.js"
copy "assets\cy\libs\chart.js-3.7.1\dist\chart.min.js_vs=100" "assets\cy\libs\chart.js-3.7.1\dist\chart.min.js"
copy "assets\cy\libs\toastr-master\build\toastr.min.js_vs=100" "assets\cy\libs\toastr-master\build\toastr.min.js"
copy "assets\cy\js\common.js_vs=100" "assets\cy\js\common.js"

echo Done! All assets have been copied.
pause
