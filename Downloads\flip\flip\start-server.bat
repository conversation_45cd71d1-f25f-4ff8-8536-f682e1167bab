@echo off
echo Starting local web server...
echo.
echo Trying different server options...
echo.

REM Try PHP built-in server first
echo Trying PHP built-in server...
php -S localhost:8000 2>nul
if %errorlevel% equ 0 goto :success

REM Try Python 3 HTTP server
echo Trying Python 3 HTTP server...
python -m http.server 8000 2>nul
if %errorlevel% equ 0 goto :success

REM Try Python 2 HTTP server
echo Trying Python 2 HTTP server...
python -m SimpleHTTPServer 8000 2>nul
if %errorlevel% equ 0 goto :success

REM Try Node.js http-server if available
echo Trying Node.js http-server...
npx http-server -p 8000 2>nul
if %errorlevel% equ 0 goto :success

echo.
echo ERROR: No suitable web server found!
echo.
echo Please install one of the following:
echo 1. PHP (recommended for PHP files)
echo 2. Python 3
echo 3. Node.js
echo.
echo Then run this script again.
pause
goto :end

:success
echo.
echo SUCCESS! Server started on http://localhost:8000
echo.
echo Open your browser and go to: http://localhost:8000
echo.
echo Press Ctrl+C to stop the server
pause

:end
