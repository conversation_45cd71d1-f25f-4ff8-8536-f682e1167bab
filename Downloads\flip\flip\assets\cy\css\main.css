/* USER VARIABLES SECTION */

:root {
	--accent: orange;
	--text: #333;
	--regular-text: 16px;
	--lineheight: 1.65;
	--userfont: "Inter", sans-serif;
	--systemfont: -apple-system, BlinkMacSystemFont, Arial, sans-serif;
}

/* BOOTSTRAP SETTINGS SECTION */

.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
	--bs-gutter-x: 12px;
}

.row,
.row>* {
	--bs-gutter-x: 24px;
}

/* FONTS LOAD SECTION */

@font-face {
	src: url("../fonts/Inter-Light.woff2") format("woff2");
	font-family: "Inter";
	font-weight: 300;
	font-style: normal;
}

@font-face {
	src: url("../fonts/Inter-Regular.woff2") format("woff2");
	font-family: "Inter";
	font-weight: 400;
	font-style: normal;
}

@font-face {
	src: url("../fonts/Inter-Medium.woff2") format("woff2");
	font-family: "Inter";
	font-weight: 500;
	font-style: normal;
}

@font-face {
	src: url("../fonts/Inter-SemiBold.woff2") format("woff2");
	font-family: "Inter";
	font-weight: 600;
	font-style: normal;
}

@font-face {
	src: url("../fonts/Inter-Bold.woff2") format("woff2");
	font-family: "Inter";
	font-weight: 700;
	font-style: normal;
}

@font-face {
	src: url("../fonts/Inter-ExtraBold.woff2") format("woff2");
	font-family: "Inter";
	font-weight: 800;
	font-style: normal;
}


/* GENERAL CSS SETTINGS */

::placeholder {
	color: #406084;
}

::selection {
	background-color: var(--accent);
	color: #fff;
}

input,
textarea {
	outline: none;
}

body {
	font-family: var(--userfont);
	font-size: var(--regular-text);
	line-height: var(--lineheight);
	color: var(--text);
	min-width: 320px;
	position: relative;
	overflow-x: hidden;
	background-color: #0D1721;
}

/* USER STYLES */


h1,
h2,
h3,
h4,
h5,
h6 {
	cursor: default;
	line-height: 1.25;
	margin-bottom: 0;
	font-weight: 500;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
	font-weight: 500;
	cursor: default;
	line-height: 1.25;
	margin-bottom: 0;
}

h1,
.h1 {
	font-size: 32px;
}

h2,
.h2 {
	font-size: 24px;
}

h3,
.h3 {
	font-size: 20px;
}

h4,
.h4 {
	font-size: 18px;
}


a:hover {
	text-decoration: none;
}

a,
button,
input,
select {
	transition: 0.4s ease;
	outline: none
}

button {
	background-color: transparent;
}

a:hover,
button:hover,
input:hover,
select:hover {
	transition: 0.4s ease;
	outline: none;
}

a:focus,
button:focus,
input:focus,
select:focus {
	outline: none;
}

p:last-child {
	margin-bottom: 0;
}

ul:last-child {
	margin-bottom: 0;
}

.image {
	max-width: 100%;
	display: block;
}

.accent {
	color: var(--accent);
	font-weight: bold;
}



.page {}

.page--main {}

.page-inner {
	position: relative;
	min-height: 800px;
	overflow: hidden;
}


.grey-btn {
	display: inline-block;
	line-height: 1;
	color: #EBF4FF;
	font-size: 18px;
	border: 0;
	background-color: #172a3e;
	text-align: center;
	padding: 15px 16px;
	border-radius: 8px;
	text-decoration: none;
	cursor: pointer;
}

.grey-btn:hover {
	color: #EBF4FF;
	text-decoration: none;
	background-color: #172a3e;
}

.green-gr-btn {
	display: inline-block;
	line-height: 1;
	color: #EBF4FF;
	font-size: 18px;
	font-weight: 500;
	border: 0;
	background-color: #17c964;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	text-align: center;
	padding: 15px 16px;
	border-radius: 8px;
	text-decoration: none;
}

.green-gr-btn:hover {
	color: #EBF4FF;
	text-decoration: none;
	background-color: #17c964;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}



.purple-btn {
	display: inline-block;
	line-height: 1;
	color: #EBF4FF;
	font-size: 18px;
	border: 0;
	box-shadow: 0 3px 10px -3px rgba(4, 121, 226, 0.27);
	background: #384BF2;
	border: 1px solid #384BF2;
	text-align: center;
	padding: 14px 15px;
	border-radius: 8px;
	text-decoration: none;
	transition: 0.4s ease;
}

.purple-btn:hover {
	color: #EBF4FF;
	text-decoration: none;
	background-color: #2331b6;
	border-color: #2331b6;
}


.purple-bd-btn {
	display: inline-block;
	line-height: 1;
	color: #EBF4FF;
	font-size: 18px;
	border: 0;
	background: transparent;
	border: 1px solid #384BF2;
	text-align: center;
	padding: 14px 15px;
	border-radius: 8px;
	text-decoration: none;
}

.purple-bd-btn:hover {
	color: #EBF4FF;
	text-decoration: none;
	background-color: #384BF2;
	border-color: #384BF2;
}

.white-bd-btn {
	display: inline-block;
	line-height: 1;
	color: #EBF4FF;
	font-size: 18px;
	border: 0;
	background: transparent;
	border: 1px solid #e4e4e4;
	text-align: center;
	padding: 14px 15px;
	border-radius: 8px;
	text-decoration: none;
}

.white-bd-btn:hover {
	color: #14202D;
	text-decoration: none;
	background-color: #EBF4FF;
	border-color: #e4e4e4;
}


.white-bd-btn:hover .send-btn__icon::before {
	background-color: #14202D;
}

header {
	position: relative;
	z-index: 1000;
}

.topline-block-wrapper {}

.topline-block {}

.topline {
	height: 66px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding-top: 8px;
	padding-bottom: 8px;
}

.topline-left {
	display: flex;
	align-items: center;
}

.topline-left .logo-wrapper {
	margin-right: 100px;
}


.topmenu-block {
	width: 625px;
}

.topmenu {
	width: 625px;
	font-size: 16px;
	line-height: 1;
	font-weight: 500;
	vertical-align: middle;
	position: relative;
	overflow: hidden;
	height: 40px;
}

.topmenu.priority-nav-has-dropdown {
	overflow: visible;
	height: auto;
}

.topmenu>ul {
	padding: 0;
	margin: 0;
	list-style-type: none;
	vertical-align: middle;
}

.topmenu-item {
	margin-right: 16px;
	display: inline-block;
	vertical-align: middle;
}

.topmenu-item:last-child {
	margin-right: 0;
}

.topmenu-item.active .topmenu-link {
	background-color: #172A3E;
}

.topmenu-link {
	color: #EBF4FF;
	display: flex;
	align-items: center;
	text-decoration: none;
	padding: 12px;
	position: relative;
	border-radius: 8px;
}

.topmenu-link:hover {
	color: #17c964;
}

.topmenu-link__icon {
	width: 16px;
	height: 16px;
}

.topmenu-link__icon+.topmenu-link__text {
	padding-left: 4px;
}

.topmenu-link__text {}

.topmenu-link--soon {
	color: #406084;
	pointer-events: none;
}


.topmenu-link__soon {
	font-weight: 500;
	font-size: 11px;
	line-height: 1.09;
	letter-spacing: -0.04em;
	color: #0170ef;
	padding-left: 2px;
	padding-right: 2px;
	padding-bottom: 1px;
	border-radius: 4px;
	background-color: rgba(1, 112, 239, 0.2);
	position: absolute;
	top: 0;
	right: 0;
}

.topmenu .priority-nav__wrapper {
	vertical-align: middle;
	display: inline-block;
	margin-left: 7px;
}


.topmenu.priority-nav-has-dropdown .priority-nav__dropdown-toggle {
	width: 24px;
	height: 24px;
	border: 0;
	background-color: transparent;
	display: block;
}

.topmenu.priority-nav-has-dropdown .priority-nav__dropdown-toggle::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/dots-three-сircle.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/dots-three-сircle.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}


.topmenu.priority-nav-has-dropdown .priority-nav__dropdown-toggle.is-open::before {
	mask-image: url('../images/svg/x-circle.svg');
	-webkit-mask-image: url('../images/svg/x-circle.svg');
	background-color: #406084;
}

.topmenu .priority-nav__dropdown {
	padding-left: 0;
	background: #14202d;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 8px 12px;
	margin-top: 12px;
	transition: 0.4s ease;
	visibility: hidden;
	opacity: 0;
	pointer-events: none;
}

.topmenu .priority-nav__dropdown.show {
	visibility: visible;
	opacity: 1;
	pointer-events: auto;
}

.topmenu .priority-nav__dropdown .topmenu-item {
	display: block;
	margin-right: 0;
	margin-top: 8px;
}

.topmenu .priority-nav__dropdown .topmenu-item:first-child {
	margin-top: 0;
}

.topmenu .priority-nav__dropdown .topmenu-item:nth-child(even) .topmenu-link {
	background: #0e1b28;
}


.topline-right {}

.topline-panel-block {}

.topline-panel {
	display: flex;
	align-items: center;
}

.topline-lang-panel-block {
	padding-left: 8px;
}

.topline-lang-panel {
	position: relative;
}

.current-lang {
	background: #172a3e;
	color: #ebf4ff;
	font-size: 14px;
	line-height: 1.35;
	font-weight: 500;
	position: relative;
	display: flex;
	align-items: center;
	padding: 10px;
	text-decoration: none;
	border-radius: 8px;
}

.current-lang__flag {
	width: 26px;
	height: 20px;
}

.current-lang__text {
	padding-left: 6px;
}

.current-lang:hover,
.current-lang:focus {
	color: #17c964;
}

.topline-lang-dropdown {
	position: absolute;
	z-index: 5;
	left: 0;
	border-radius: 8px;
	/* display: none; */
	top: 100%;
	margin-top: 0;
	padding: 0;
	padding-top: 4px;
	border: 0;
	background-color: transparent;
	min-width: 0;
}

.lang-link {
	background: #172a3e;
	color: #ebf4ff;
	font-size: 14px;
	line-height: 1.35;
	font-weight: 500;
	position: relative;
	display: flex;
	align-items: center;
	padding: 9px;
	text-decoration: none;
	border-radius: 8px;
	border: 1px solid #1a314b;
	margin-top: 4px;
}

.lang-link__flag {
	width: 26px;
	height: 20px;
}

.lang-link__text {
	padding-left: 6px;
}

.lang-link:first-child {
	margin-top: 0;
}

.lang-link:hover {
	color: #17c964;
}

.topline-login-btn-block {
	padding-left: 8px;
	display: flex;
}

.topline-login-btn {
	display: flex;
	align-items: center;
	background: #172a3e;
	color: #ebf4ff;
	font-size: 16px;
	line-height: 1;
	font-weight: 500;
	position: relative;
	display: flex;
	align-items: center;
	padding: 8px 16px;
	border: 0;
	text-decoration: none;
	border-radius: 8px;
}

.topline-login-btn__text {
	padding-right: 8px;
}

.topline-login-btn__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.topline-login-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/sign-in.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/sign-in.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #FFF;
	transition: 0.4s ease;
}

.topline-login-btn:hover {
	color: #17c964;
}

.topline-login-btn:hover .topline-login-btn__icon::before {
	background-color: #17c964;
}


.topline-registration-btn-block {
	padding-left: 8px;
	display: flex;
}

.topline-registration-btn {
	display: flex;
	align-items: center;
	line-height: 1;
	color: #EBF4FF;
	font-size: 16px;
	border: 0;
	background-color: #17c964;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	text-align: center;
	padding: 8px 12px;
	text-decoration: none;
	border-radius: 8px;
}

.topline-registration-btn__text {
	padding-right: 8px;
}

.topline-registration-btn__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.topline-registration-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/user-circle-plus.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/user-circle-plus.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.topline-registration-btn:hover {
	color: #EBF4FF;
}


.mobile-panel-btn-block {
	display: none;
	/* display: block; */
	padding-left: 8px;
}

.mobile-panel-btn {
	display: block;
	position: relative;
	z-index: 2;
	width: 40px;
	height: 40px;
	background: #172a3e;
	cursor: pointer;
	border-radius: 8px;
	border: 0;
}

.mobile-panel-btn::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/list.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/list.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.section-registration {
	padding-top: 100px;
	padding-bottom: 180px;
}

.registration-block {
	background: #14202d;
	max-width: 856px;
	margin-right: auto;
	margin-left: auto;
	border-radius: 24px;
}

.registration {
	display: flex;
}

.registration-left {
	position: relative;
	padding-top: 40px;
	padding-left: 45px;
	padding-right: 45px;
	padding-bottom: 310px;
	width: 330px;
	box-shadow: inset -8px 0 15px 0 rgba(0, 0, 0, 0.25);
	background: linear-gradient(120deg, #17c964 0%, #006fee 100%);
	border-radius: 24px 0 0 24px;
}

.registration-right {
	width: calc(100% - 330px);
	padding: 48px 72px;
	border: 1px solid #1a314b;
	border-radius: 0 24px 24px 0;
}

.registration-upline-block {
	margin-right: -45px;
	position: relative;
	z-index: 2;
}

.registration-upline {
	display: flex;
	background-image: url('../images/theme/registration-upline-bg.png');
	background-size: 100% 100%;
	background-position: center left;
	background-repeat: no-repeat;
	padding: 7px 8px;

}

.registration-upline__flag {
	width: 26px;
	height: 20px;
}

.registration-upline__content {
	width: calc(100% - 26px);
	padding-left: 14px;
	padding-right: 14px;
}

.registration-upline__name {
	font-weight: 600;
	font-size: 14px;
	line-height: 135%;
	color: #ebf4ff;
}

.registration-upline__descr {
	font-weight: 400;
	font-size: 14px;
	line-height: 135%;
	color: #ebf4ff;
}

.registration-feature-items-block {
	margin-top: 38px;
	max-width: 230px;
	position: relative;
	z-index: 2;
}

.registration-feature-items {}

.registration-feature-item-wrapper {
	margin-top: 32px;
}

.registration-feature-item-wrapper:first-child {
	margin-top: 0;
}

.registration-feature-item {
	display: flex;
	align-items: center;
}

.registration-feature-item__icon {
	width: 48px;
	height: 48px;
}

.registration-feature-item__descr {
	width: calc(100% - 48px);
	padding-left: 8px;
	font-weight: 600;
	font-size: 14px;
	line-height: 135%;
	color: #ebf4ff;
}

.registration-left-bg-image {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 243px;
}

.registration-left-bg-image .image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}



button {
	cursor: pointer;
}

.field--input {
	position: relative;
}

.field--input input {
	width: 100%;
}

.field--textarea {
	position: relative;
}

.field--textarea textarea {
	width: 100%;
	display: block;
}

.field--select {
	position: relative;
}

.field--select select {
	width: 100%;
	appearance: none;
	outline: none;
}

.field--select select {
	border: 0;
	opacity: 0;
	height: 56px;
	visibility: hidden;
}

.field--checkbox {
	position: relative;
}

.field--radio {
	position: relative;
}

.btn-wrapper {
	position: relative;
}

.field-block {
	margin-top: 16px;
	position: relative;
}

.field-block:first-child {
	margin-top: 0;
}

.field-title-block {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.field-title-block__left {}

.field-title-block__right {}

.field-title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.field-title .field-required-star {
	color: #0681d5;
}

.field {
	margin-top: 16px;
}

.field:first-child {
	margin-top: 0;
}

.field-title-block+.field {
	margin-top: 6px;
}

.form ::placeholder {
	color: #406084;
}

.form-button-block {
	margin-top: 16px;
}

.form-button-block .orange-btn {
	width: 100%;
}

.field input[type='text'],
.field input[type='email'],
.field input[type='password'] {
	background-color: #172a3e;
	height: 48px;
	padding-left: 14px;
	padding-right: 14px;
	color: #ebf4ff;
	font-size: 16px;
	border: 1px solid #1a314b;
	border-radius: 8px;
	text-overflow: ellipsis;
}

.field textarea {
	background-color: #172a3e;
	height: 120px;
	line-height: 1.25;
	padding-left: 14px;
	padding-right: 14px;
	color: #ebf4ff;
	font-size: 16px;
	border: 1px solid #1a314b;
	border-radius: 8px;
	resize: none;
	display: block;
	padding-top: 10px;
	padding-bottom: 10px;
	/* transition: 0.4s ease; */
}



.field textarea:focus,
.field input[type='text']:focus,
.field input[type='email']:focus,
.field input[type='password']:focus {
	border-color: #17c964;
}

.registration-form-block {}

.registration-form-block h2 {
	color: #ebf4ff;
}

.registration-form {
	margin-top: 24px;
}


.field--have-icon {}

.field--have-icon input[type='text'],
.field--have-icon input[type='email'],
.field--have-icon input[type='password'],
.field--have-icon textarea {
	padding-left: 44px;
}

.field--input-email {}

.field-icon {
	width: 20px;
	height: 20px;
	position: absolute;
	left: 12px;
	top: 50%;
	transform: translateY(-50%);
	pointer-events: none;
}


.field-icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #406084;
}

.field--email .field-icon::before {
	mask-image: url('../images/svg/envelope.svg');
	-webkit-mask-image: url('../images/svg/envelope.svg');
}

.field--password .field-icon::before {
	mask-image: url('../images/svg/lock.svg');
	-webkit-mask-image: url('../images/svg/lock.svg');
}

.field--username .field-icon::before {
	mask-image: url('../images/svg/user-circle-plus.svg');
	-webkit-mask-image: url('../images/svg/user-circle-plus.svg');
}

.field--pin .field-icon::before {
	mask-image: url('../images/svg/fingerprint.svg');
	-webkit-mask-image: url('../images/svg/fingerprint.svg');
}

.field--2fa .field-icon::before {
	mask-image: url('../images/svg/password.svg');
	-webkit-mask-image: url('../images/svg/password.svg');
}

.field--price .field-icon::before {
	mask-image: url('../images/svg/ticket.svg');
	-webkit-mask-image: url('../images/svg/ticket.svg');
}

.field--search .field-icon::before {
	mask-image: url('../images/svg/magnifying-glass.svg');
	-webkit-mask-image: url('../images/svg/magnifying-glass.svg');
}


.field--amount .field-icon::before {
	mask-image: url('../images/svg/tip-jar.svg');
	-webkit-mask-image: url('../images/svg/tip-jar.svg');
}




.field-right-panel-block {
	position: absolute;
	right: 14px;
	top: 50%;
	transform: translateY(-50%);
	z-index: 10;
}

.field-right-panel {
	display: flex;
	align-items: center;
}

.field-right-panel select {
	opacity: 0;
	visibility: hidden;
}

.field-right-panel .select-currency.bootstrap-select>.dropdown-toggle {
	padding: 0;
	border: 0;
	padding-right: 0;
	height: auto;
	display: block;
}

.field-right-panel .select-currency.bootstrap-select>.dropdown-toggle .select-currency-list-item__right {
	display: none;
}

.change-pswd-type-link-block {}

.change-pswd-type-link-block+.field-status-block {
	padding-left: 8px;
}

.change-pswd-type-link {
	display: block;
	width: 20px;
	height: 20px;
	position: relative;
}


.change-pswd-type-link::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.change-pswd-type-link.active::before {
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-image: url('../images/svg/eye.svg');
}


.change-pswd-type-link:hover::before {
	background-color: #17C964;
}

.field-status-block {
	position: relative;
}

.field-status-block+.field-status-block {
	padding-left: 8px;
}

.field-status {
	width: 20px;
	height: 20px;
	position: relative;
}

.field-status::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #406084;
	transition: 0.4s ease;
}

.field-status--success {}

.field-status--success::before {
	mask-image: url('../images/svg/check-circle.svg');
	-webkit-mask-image: url('../images/svg/check-circle.svg');
	background-color: #17C964;
}


.field-message-block {
	margin-top: 4px;
}

.field-message {
	display: flex;
}

.field-message__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.field-message__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/info.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/info.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.field-message__text {
	font-weight: 400;
	font-size: 12px;
	line-height: 1.33;
	color: #406084;
	padding-left: 4px;
	max-width: calc(100% - 16px);
}

.field-message__text a {
	text-decoration: underline;
	text-decoration-skip-ink: none;
	color: #0681d5;
}

.field-message__text a:hover {
	text-decoration: none;
}


.field-message--error {}

.field-message--error .field-message__icon::before {
	mask-image: url('../images/svg/warning.svg');
	-webkit-mask-image: url('../images/svg/warning.svg');
	background-color: #E53535;
}

.field-message--error .field-message__text {
	color: #E53535;
}

.field-error input[type='text'],
.field-error input[type='email'],
.field-error input[type='password'] {
	border-color: #e53535;
	box-shadow: 0 3px 12px 0 rgba(224, 36, 36, 0.1);
}


.field-message--success {}

.field-message--success .field-message__icon::before {
	mask-image: url('../images/svg/check-circle.svg');
	-webkit-mask-image: url('../images/svg/check-circle.svg');
	background-color: #17c964;
}

.field-message--success .field-message__text {
	color: #17c964;
}


.field--checkbox {
	display: flex;
}

.custom-checkbox-label {
	line-height: 1;
	margin-bottom: 0;
	text-align: left;
	display: flex;
	cursor: pointer;
}

.custom-checkbox-label-text {
	font-size: 12px;
	line-height: 1.33;
	color: #7998ba;
	max-width: calc(100% - 16px);
	padding-left: 12px;
}

.custom-checkbox-label-text a {
	font-weight: 500;
	color: #0681d5;
	text-decoration: none;
}

.custom-checkbox-label-text a:hover {
	color: #0681d5;
	text-decoration: underline;
}

.custom-checkbox {
	border: 1px solid #1a314b;
	border-radius: 3px;
	width: 16px;
	height: 16px;
	background-color: #172a3e;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.custom-checkbox::before {
	content: '';
	position: absolute;
	width: 12px;
	height: 12px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 12px 12px;
	mask-image: url('../images/svg/checkbox-check.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 12px 12px;
	-webkit-mask-image: url('../images/svg/checkbox-check.svg');
	background-color: transparent;
}

.checkbox {
	display: none;
}

.checkbox:checked~.custom-checkbox {
	background: #17C964;
	border-color: #17C964;
}

.checkbox:checked~.custom-checkbox::before {
	background: #fff;
}

.send-btn {
	display: flex;
	align-items: center;
	width: 100%;
	justify-content: center;
	padding-top: 12px;
	padding-bottom: 12px;
}

.send-btn__text {
	padding-right: 12px;
}

.send-btn__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.send-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/arrow-circle-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/arrow-circle-right.svg');
	background-color: #EBF4FF;
	transition: 0.4s ease;
}

.form-bottom-note-block {
	margin-top: 16px;
}

.form-bottom-note {
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
}

.form-bottom-note a {
	color: #0681D5;
	font-weight: 500;
	text-decoration: none;
}

.form-bottom-note a:hover {
	text-decoration: underline;
}

.form-bottom-note button {
	color: #0681D5;
	font-weight: 500;
	text-decoration: none;
	border: 0;
	padding: 0;
}

.form-bottom-note button:hover {
	text-decoration: underline;
}



.modals {}

.modal.fade .modal-dialog {
	transform: none;
}

.custom-modal {
	z-index: 3055;
}

.modal-dialog {
	max-width: 530px;
	margin-right: auto;
	margin-left: auto;
}

.custom-modal .modal-content {
	border: 1px solid #1a314b;
	border-radius: 24px;
	padding: 48px 72px;
	background-color: #14202d;
}

.custom-modal .modal-header {
	position: relative;
	align-items: flex-start;
	border: 0;
	padding: 0;
	padding-right: 40px;
}

.modal-close {
	position: absolute;
	width: 32px;
	height: 32px;
	border: 0;
	padding: 0;
	right: 0;
	z-index: 10;
	background-color: transparent;
}

.modal-close::before {
	content: '';
	position: absolute;
	width: 32px;
	height: 32px;
	mask-image: url('../images/svg/close.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 32px 32px;
	-webkit-mask-image: url('../images/svg/close.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 32px 32px;
	background-color: #406084;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 10;
}

.custom-modal .modal-body {
	position: relative;
	padding: 0;
}


.modal-body-content {
	position: relative;
	padding-top: 24px;
}

.modal-title-block {
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-title {
	position: relative;
	color: #EBF4FF;
}


.login-form-block {}


.login-form {}

.field-bottom-link-block {
	margin-top: 4px;
}

.reset-password-link {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #0681d5;
	position: relative;
	border: 0;
	padding: 0;
	padding-left: 22px;
	text-decoration: none;
}

.reset-password-link::before {
	content: '';
	position: absolute;
	width: 18px;
	height: 18px;
	mask-image: url('../images/svg/arrows-clockwise.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 18px 18px;
	-webkit-mask-image: url('../images/svg/arrows-clockwise.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 18px 18px;
	background-color: #0681d5;
	top: 50%;
	left: 0;
	transform: translateY(-50%);
	transition: 0.4s ease;
}

.reset-password-link:hover {
	color: #17C964;
}

.reset-password-link:hover::before {
	background-color: #17C964;
}


.recovery-form-block {}


.recovery-form {}

.form-info-block {}

.form-info {
	background-color: #172a3e;
	border-left: 1px solid #047bdf;
	border-radius: 0 8px 8px 0;
	padding: 12px 14px;
	display: flex;
	position: relative;
}

.form-info__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.form-info__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	mask-image: url('../images/svg/info.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/info.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #047BDF;
	top: 0;
	left: 0;
	transition: 0.4s ease;
}

.form-info__text {
	width: calc(100% - 20px);
	padding-left: 10px;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.form-info__text a {
	color: #0681d5;
	text-decoration: none;
}

.form-info__text a:hover {
	color: #0681d5;
	text-decoration: underline;
}

.form-info--warning {
	background: rgba(239, 141, 50, 0.1);
	border-left: 1px solid #ef8d32;
}

.form-info--warning .form-info__icon::before {
	background-color: #ef8d32;
}

.form-info--warning .form-info__text {
	color: #ef8d32;
}

.mobile-panel-block {
	position: fixed;
	top: 0;
	right: 0;
	height: 100%;
	z-index: 2000;
	transform: translateX(100%);
	transition: 0.4s transform ease;
	padding: 12px;
	width: 320px;
	overflow: auto;
	background-color: #0D1721;
	display: none;
}

.mobile-panel-block.active {
	transform: translateX(0);
	box-shadow: 0 0 5px 0px rgba(0, 0, 0, 0.15);
}

.mobile-panel-top-block {
	display: flex;
	justify-content: space-between;
	padding-top: 4px;
	padding-bottom: 4px;
}

.mobile-panel-top-left .logo-wrapper {
	display: flex;
	max-width: 114px;
}

.mobile-panel-top-left .logo {}

.mobile-panel-top-right {
	display: flex;
	align-items: center;
}


.mobile-panel-close-btn-block {
	padding-left: 4px;
}

.mobile-panel-close-btn {
	display: block;
	position: relative;
	z-index: 2;
	width: 40px;
	height: 40px;
	background: #172a3e;
	cursor: pointer;
	border-radius: 8px;
	border: 0;
}

.mobile-panel-close-btn::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/close.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/close.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.mobile-panel-menu-block {
	padding-top: 4px;
}

.mobile-panel-menu {
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 8px 12px;
}

.mobile-auth-panel-block {}

.mobile-auth-panel {
	display: flex;
	margin-left: -4px;
	margin-right: -4px;
}

.mobile-auth-panel-btn-block {
	width: 50%;
	padding-left: 4px;
	padding-right: 4px;
}

.mobile-auth-panel .topline-login-btn {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.mobile-auth-panel .topline-registration-btn {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}


.mobile-menu {
	font-size: 16px;
	line-height: 1;
	font-weight: 500;
	vertical-align: middle;
	margin-top: 8px;
}

.mobile-menu:first-child {
	margin-top: 0;
}

.mobile-menu>ul {
	padding: 0;
	margin: 0;
	list-style-type: none;
	vertical-align: middle;
}

.mobile-menu-item {
	display: flex;
	margin-top: 8px;
}

.mobile-menu-item:first-child {
	margin-top: 0;
}

.mobile-menu-link {
	color: #EBF4FF;
	display: flex;
	align-items: center;
	text-decoration: none;
	padding-top: 12px;
	padding-bottom: 12px;
	padding-right: 12px;
	position: relative;
	border-radius: 8px;
}

.mobile-menu-link:hover {
	color: #17c964;
}

.mobile-menu-link__icon {
	width: 16px;
	height: 16px;
}

.mobile-menu-link__icon+.mobile-menu-link__text {
	padding-left: 4px;
}

.mobile-menu-link__text {}

.mobile-menu-link--soon {
	color: #406084;
	pointer-events: none;
}


.mobile-menu-link__soon {
	font-weight: 500;
	font-size: 11px;
	line-height: 1.09;
	letter-spacing: -0.04em;
	color: #0170ef;
	padding-left: 2px;
	padding-right: 2px;
	padding-bottom: 1px;
	border-radius: 4px;
	background-color: rgba(1, 112, 239, 0.2);
	position: absolute;
	top: 0;
	right: 0;
}




.registration-mobile-feature-items-block {
	margin-top: 8px;
	display: none;
}

.registration-mobile-feature-items {
	display: flex;
	flex-wrap: wrap;
	margin-left: -12px;
	margin-right: -12px;
}

.registration-mobile-feature-item-wrapper {
	margin-top: 32px;
	width: 50%;
	padding-left: 12px;
	padding-right: 12px;
}


.registration-mobile-feature-item {
	display: block;
}

.registration-mobile-feature-item__icon {
	width: 32px;
	height: 32px;
	margin-right: auto;
	margin-left: auto;
}

.registration-mobile-feature-item__descr {
	width: 100%;
	padding-top: 8px;
	font-weight: 600;
	font-size: 14px;
	line-height: 1.35;
	color: #406084;
	text-align: center;
	max-width: 196px;
	margin-right: auto;
	margin-left: auto;
}

.registration-left-bg-image {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 243px;
}

.registration-left-bg-image .image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}



.topline-logout-btn-block {
	padding-left: 8px;
	display: flex;
}

.topline-logout-btn {
	background: #172a3e;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8px 8px;
	text-decoration: none;
	border-radius: 8px;
}


.topline-logout-btn__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.topline-logout-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/sign-out.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/sign-out.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #FFF;
	transition: 0.4s ease;
}

.topline-logout-btn:hover {
	color: #17c964;
}

.topline-logout-btn:hover .topline-logout-btn__icon::before {
	background-color: #17c964;
}


.topline-dashboard-btn-block {
	padding-left: 8px;
	display: flex;
}

.topline-dashboard-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	line-height: 1;
	color: #EBF4FF;
	font-size: 16px;
	border: 0;
	background-color: #17c964;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	text-align: center;
	padding: 8px 12px;
	text-decoration: none;
	border-radius: 8px;
}

.topline-dashboard-btn__text {
	padding-right: 8px;
}

.topline-dashboard-btn__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.topline-dashboard-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/house-simple.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/house-simple.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.topline-dashboard-btn:hover {
	color: #EBF4FF;
}


.topline-balance-block {}

.topline-balance {
	position: relative;
}

.topline-balance-btn {
	display: flex;
	align-items: center;
	position: relative;
	padding: 4px;
	border-radius: 8px;
	text-decoration: none;
	background-color: #14202D;
	border: 0;
	width: 100%;
}

.topline-balance-btn__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.topline-balance-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/user-circle.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/user-circle.svg');
	background-color: #EBF4FF;
	transition: 0.4s ease;
}

.topline-balance-btn__content {
	padding-left: 8px;
	padding-right: 24px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #ebf4ff;
	width: calc(100% - 24px);
	text-align: left;
}

.topline-balance-btn__amount {}

.topline-balance-btn__currency {}

.topline-balance-toggle-btn {
	border: 0;
	width: 20px;
	height: 20px;
	padding: 0;
	position: absolute;
	background-color: transparent;
	right: 4px;
	top: 50%;
	transform: translateY(-50%);
}

.topline-balance-toggle-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/eye-slash.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/eye-slash.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.topline-balance-toggle-btn.active::before {
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-image: url('../images/svg/eye.svg');
}

.topline-balance-toggle-btn:hover::before {
	background-color: #2EE57A;
}

.topline-balance-dropdown {
	background: #14202d;
	border-radius: 8px;
	padding: 16px;
	right: 0;
	margin-top: 14px !important;
}

.topline-balance-currency-list-block {}

.topline-balance-currency-list {
	max-height: 255px;
	overflow: auto;
	padding-right: 12px;
}


.topline-balance-currency-list::-webkit-scrollbar {
	width: 4px;
	height: 4px;
}

.topline-balance-currency-list::-webkit-scrollbar-track {
	background: #0E1B28;
	border-radius: 12px;
}

.topline-balance-currency-list::-webkit-scrollbar-thumb {
	background-color: #0170EF;
	border-radius: 12px;
}


.topline-balance-currency-list-item {
	display: flex;
	width: 215px;
}

.topline-balance-currency-list-item__left {
	display: flex;
	align-items: center;
	padding: 4px 6px;
	width: 50%;
}

.topline-balance-currency-list-item__currency {
	display: flex;
	align-items: center;
}

.topline-balance-currency-list-item__currency__icon {
	width: 16px;
	height: 16px;
}

.topline-balance-currency-list-item__currency__abbr {
	width: calc(100% - 16px);
	padding-left: 8px;
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
}

.topline-balance-currency-list-item__right {
	position: relative;
	overflow: hidden;
	display: flex;
	align-items: center;
	padding: 4px 6px;
	width: 50%;
}

.topline-balance-currency-list-item__amount {
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.topline-balance-currency-list-item--not-empty .topline-balance-currency-list-item__amount {
	color: #2ee57a;
}

.topline-balance-currency-list-item:nth-child(odd) {
	background-color: #0e1b28;
}

.topline-balance-manage-buttons-block {
	margin-top: 4px;
}

.topline-balance-manage-buttons {
	display: flex;
	align-items: center;
	margin-left: -4px;
	margin-right: -4px;
}

.topline-balance-manage-btn-wrapper {
	width: 50%;
	padding-left: 4px;
	padding-right: 4px;
	padding-top: 4px;
}

.topline-balance-manage-deposit-btn {
	font-size: 16px;
	padding: 12px;
	width: 100%;
}

.topline-balance-manage-withdraw-btn {
	font-size: 16px;
	padding: 12px;
	width: 100%;
}

.section-lottery-list {
	padding-top: 70px;
	padding-bottom: 40px;
}

.lottery-list-top-block {}

.lottery-list-top {
	display: flex;
	align-items: flex-end;
}

.lottery-list-top h2 {
	font-weight: 500;
	font-size: 32px;
	line-height: 1.25;
	color: #fff;
}

.lottery-list-top-left {
	width: calc(100% - 190px);
	padding-right: 24px;
}

.lottery-list-top-descr {
	margin-top: 16px;
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.lottery-list-top-right {
	width: 190px;
}


.iconed-btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.iconed-btn__text {
	padding-right: 12px;
}

.iconed-btn__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.iconed-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.iconed-btn__icon+.iconed-btn__text {
	padding-left: 12px;
	padding-right: 0;
}

.lottery-list-top-btn-block {
	display: flex;
}

.lottery-list-top-btn {
	font-size: 16px;
	padding-top: 11px;
	padding-bottom: 11px;
	width: 100%;
}

.lottery-list-top-btn .iconed-btn__icon::before {
	mask-image: url('../images/svg/ticket.svg');
	-webkit-mask-image: url('../images/svg/ticket.svg');
}

.lottery-list-filter-block {
	margin-top: 16px;
	padding: 4px 12px;
	background: #14202d;
	border-radius: 8px;
	position: relative;
}

.lottery-list-filter-form {}

.lottery-list-filter-form-row {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: space-between;
	margin-left: -8px;
	margin-right: -8px;
}

.lottery-list-filter-form-col {
	padding-left: 8px;
	padding-right: 8px;
	padding-top: 8px;
	padding-bottom: 8px;
}

.lottery-list-filter-form-col--type {}

.lottery-list-filter-form-col--currency {}

.lottery-list-filter-form-col--price {}

.lottery-list-filter-form-col--fund {}

.lottery-list-filter-form-col--checkboxes {}

.lottery-list-filter-form-col--clear {}

.lottery-list-filter-form-type-items-block {
	background: #172a3e;
	border-radius: 8px;
}

.lottery-list-filter-form-type-items {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
}

.lottery-list-filter-form-type-item-wrapper {
	padding-left: 8px;
	padding-right: 8px;
}

.lottery-list-filter-form-type-item-label {
	display: block;
	margin: 0;
}

.radio {
	display: none;
}

.lottery-list-filter-form-type-item {
	display: block;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	color: #406084;
	background-color: #172a3e;
	border: 1px solid #172a3e;
	padding: 15px 15px;
	text-decoration: none;
	border-radius: 8px;
	transition: 0.4s ease;
	cursor: pointer;
}

.lottery-list-filter-form-type-item:hover {
	color: #fff;
	background-color: #14202d;
	border: 1px solid #1a314b;
}


.radio:checked~.lottery-list-filter-form-type-item {
	color: #fff;
	background-color: #14202d;
	border: 1px solid #1a314b;
	pointer-events: none;
}

.lottery-list-filter-form-type-item.active {}





.select-wrapper {
	position: relative;
}

.select-wrapper select {
	opacity: 0;
	visibility: hidden;
}

.select-wrapper .bootstrap-select {
	width: 100%;
}

.select-wrapper .bootstrap-select.fit-width {
	width: 100% !important;
}

.bootstrap-select {
	width: 100% !important;
}

.bootstrap-select>.dropdown-toggle {
	border-radius: 8px;
	height: 48px;
	font-weight: 400;
	font-size: 16px;
	line-height: 1;
	/* color: #406084; */
	color: #fff;
	background-color: #172a3e;
	border: 1px solid #1a314b;
	padding: 13px;
	padding-right: 44px;
}

.bootstrap-select>.dropdown-toggle.bs-placeholder,
.bootstrap-select>.dropdown-toggle.bs-placeholder:active,
.bootstrap-select>.dropdown-toggle.bs-placeholder:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder:hover {
	color: #406084;
	/* color: #fff; */
}

.bootstrap-select>.dropdown-toggle:active {
	/* background-color: transparent; */
}

.bootstrap-select>.dropdown-toggle::after {
	display: none;
}

.bootstrap-select>.dropdown-toggle::after {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	right: 14px;
	top: 50%;
	padding: 0;
	margin: 0;
	border: 0;
	transform: translateY(-50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/caret-down.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/caret-down.svg');
	background-color: #406084;
	z-index: 5;
	transition: 0.4s ease;
	pointer-events: none;
	display: block;
}


.bootstrap-select .dropdown-toggle .filter-option {
	display: flex;
	align-items: center;
	width: 100%;
	height: auto;
}

.bootstrap-select .dropdown-toggle .filter-option-inner {
	width: 100%;
}

.bootstrap-select .dropdown-toggle:focus,
.bootstrap-select>select.mobile-device:focus+.dropdown-toggle {
	outline: none !important;
	box-shadow: none;
}

.bootstrap-select>select {
	opacity: 0 !important;
	visibility: hidden !important;
	height: 0 !important;
}


.bootstrap-select>.dropdown-toggle.show:after {
	transform: translateY(-50%) rotate(180deg);
}

.bootstrap-select>.dropdown-menu {
	width: 100%;
	min-width: 210px;
	margin-top: 0;
	border-top: 0;
	/* max-height: 160px; */
	border-radius: 8px;
	/* padding: 16px 14px; */
	padding: 4px 6px;
	border: 1px solid #1a314b;
	background-color: #14202d;
	margin-top: 6px !important;
	margin-bottom: 6px !important;
	/* display: block; */
	z-index: 120;
	inset: 0px 0px auto auto !important;
}


.bootstrap-select>.dropdown-menu .inner {
	padding: 0;
	background-color: transparent;
	background-image: none;
	max-height: 160px !important;
}

.bootstrap-select>.dropdown-menu .inner ul {
	padding: 0;
	margin-right: -8px;
	padding-right: 12px;
}

.bootstrap-select .dropdown-menu .inner::-webkit-scrollbar {
	width: 4px;
	height: 4px;
}

.bootstrap-select .dropdown-menu .inner::-webkit-scrollbar-track {
	background-color: #0e1b28;
	border-radius: 12px;
}

.bootstrap-select .dropdown-menu .inner::-webkit-scrollbar-thumb {
	background-color: #0170EF;
	border-radius: 20px;
}

.bootstrap-select .dropdown-menu li {
	display: flex;
}

.bootstrap-select .dropdown-menu li:first-child {
	padding-top: 0;
}

.bootstrap-select .dropdown-menu li:last-child {
	padding-bottom: 0;
	border-bottom: 0;
}

.bootstrap-select .dropdown-menu li a {
	display: flex;
	padding: 8px 6px;
	color: #ebf4ff;
	font-size: 14px;
	font-weight: 500;
	line-height: 1;
	max-width: 100%;
	background-color: transparent;
	border-radius: 8px;
}

.bootstrap-select .dropdown-menu li:nth-child(odd) a {
	/* background-color: #0e1b28; */
}

.bootstrap-select .dropdown-menu li a:hover,
.bootstrap-select .dropdown-menu li a:focus {
	color: #17c964;
}

.bootstrap-select .dropdown-menu li a:active {}

.bootstrap-select .dropdown-menu li a.active {}

.bootstrap-select .dropdown-menu li a.active {
	/* color: #17c964; */
	background-color: #0e1b28;
}

.bootstrap-select .dropdown-menu li a.active:hover {
	/* background-color: #DFE3FA; */
}




.bootstrap-select .dropdown-menu li a.active .phone-country-select-item__text {
	font-weight: 700;
}

.bootstrap-select .dropdown-menu li a span.text {
	margin-top: 0;
	width: 100%;
	max-width: 100%;
	position: relative;
}

.bootstrap-select .dropdown-menu li.disabled {
	display: none;
}

.select-currency-list-item {
	display: flex;
	align-items: center;
	max-width: 100%;
}

.select-currency-list-item__left {
	width: 16px;
	height: 16px;
	position: relative;
}


.select-currency-list-item__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.select-currency-list-item__icon .image {
	position: relative;
	z-index: 2;
	width: 100%;
}

.select-currency-list-item__arrow {
	width: 12px;
	height: 12px;
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	transition: 0.4s ease;
	display: none;
}

.select-currency-list-item__arrow::before {
	content: '';
	position: absolute;
	width: 12px;
	height: 12px;
	top: 50%;
	left: 50%;
	padding: 0;
	margin: 0;
	border: 0;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 12px 12px;
	mask-image: url('../images/svg/caret-down-fill.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 12px 12px;
	-webkit-mask-image: url('../images/svg/caret-down-fill.svg');
	background-color: #406084;
	z-index: 5;
	transition: 0.4s ease;
	pointer-events: none;
	display: block;
}

.select-currency-list-item__right {
	width: calc(100% - 16px);
	padding-left: 8px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}


.select-currency-list-item__title {
	font-weight: 500;
	font-size: 16px;
	color: #ebf4ff;
	line-height: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	transition: 0.4s ease;
}

.select-currency-list-item__amount {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	text-align: right;
	color: #406084;
}

.select-currency.bootstrap-select>.dropdown-toggle .select-currency-list-item__amount {
	display: none;
}

.select-currency.bootstrap-select .dropdown-toggle .filter-option {
	overflow: visible;
}

.select-currency.bootstrap-select .dropdown-toggle .filter-option-inner-inner {
	overflow: visible;
}

.select-currency.bootstrap-select>.dropdown-toggle {
	padding-right: 13px;
}

.select-currency.bootstrap-select>.dropdown-toggle::after {
	display: none;
}

.select-currency.bootstrap-select>.dropdown-toggle .select-currency-list-item__arrow {
	display: block;
}

.select-currency.bootstrap-select>.dropdown-toggle.show .select-currency-list-item__arrow {
	transform: translateY(-50%) rotate(180deg);
}

.select-currency.bootstrap-select>.dropdown-toggle .select-currency-list-item__left {
	width: 38px;
	height: 20px;
	position: relative;
}

.select-currency.bootstrap-select>.dropdown-toggle .select-currency-list-item__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.select-currency.bootstrap-select>.dropdown-toggle .select-currency-list-item__right {
	max-width: calc(100% - 38px);
	padding-left: 10px;
}

.bootstrap-select .dropdown-menu li a.active .select-currency-list-item__title {
	color: #17c964;
	background-color: transparent;
}

.bootstrap-select .dropdown-menu li a:hover .select-currency-list-item__title,
.bootstrap-select .dropdown-menu li a:focus .select-currency-list-item__title {
	color: #17c964;
	background-color: transparent;
}

.bootstrap-select>.dropdown-toggle.show .select-currency-list-item__icon--currency::before {
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.lottery-list-filter-form-price-block {}

.lottery-list-filter-form-price-row {
	display: flex;
	align-items: center;
	margin-left: -4px;
	margin-right: -4px;
}

.lottery-list-filter-form-price-col {
	padding-left: 4px;
	padding-right: 4px;
}

.lottery-list-filter-form-price-col--title {}

.lottery-list-filter-form-price-title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	text-align: right;
	color: #406084;
}

.lottery-list-filter-form-price-col--field {}

.lottery-list-filter-form-price-col .field {
	width: 102px;
}

.lottery-list-filter-form-fund-block {}

.lottery-list-filter-form-fund-row {
	display: flex;
	align-items: center;
	margin-left: -4px;
	margin-right: -4px;
}

.lottery-list-filter-form-fund-col {
	padding-left: 4px;
	padding-right: 4px;
}

.lottery-list-filter-form-fund-col--title {}

.lottery-list-filter-form-fund-title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	text-align: right;
	color: #406084;
}

.lottery-list-filter-form-fund-col--field {}

.lottery-list-filter-form-fund-col .field {
	width: 102px;
}

.checkbox-list-block {
	margin-top: 16px;
}

.checkbox-list {
	margin-top: -8px;
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
	flex-wrap: wrap;
}

.checkbox-list-item {
	margin-top: 8px;
	padding-left: 8px;
	padding-right: 8px;
}

.lottery-list-filter-form-col--checkboxes .checkbox-list-block {
	margin-top: 0;
}

.lottery-list-filter-form-clear-btn {
	width: 56px;
	height: 48px;
	display: flex;
	position: relative;
}

.lottery-list-filter-form-clear-btn::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/trash-simple.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/trash-simple.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.lottery-list-items-block {
	margin-top: 20px;
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.lottery-list-items {
	margin-left: -12px;
	margin-right: -12px;
	margin-top: -24px;
	display: flex;
	flex-wrap: wrap;
}

.lottery-list-item-wrapper {
	margin-top: 24px;
	padding-left: 12px;
	padding-right: 12px;
	width: 25%;
}

.lottery-list-item {
	padding: 24px;
	border-radius: 8px;
	background-image: url('../images/theme/ticket-bg.png');
	background-position: center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-decoration: none;
	height: 100%;
	max-width: 350px;
	margin-right: auto;
	margin-left: auto;
}

.lottery-list-item__inner {
	width: 100%;
	min-height: 335px;
}

.lottery-list-item__progress-block {}

.lottery-list-item__progress {
	height: 8px;
	border-radius: 8px;
	background-color: #0D1822;
	position: relative;
}

.lottery-list-item__progress__progress {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 0;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	border-radius: 8px;
}


.lottery-list-item__fund-block {
	margin-top: 20px;
}

.lottery-list-item__fund {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.lottery-list-item__fund-left {}

.lottery-list-item__fund-title {
	font-weight: 500;
	font-size: 12px;
	line-height: 1;
	text-transform: uppercase;
	color: #406084;
	max-width: 60px;
}

.lottery-list-item__fund-right {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.lottery-list-item__fund-amount {
	max-width: calc(100% - 20px);
	padding-right: 4px;

	font-weight: 500;
	font-size: 24px;
	line-height: 1.25;
	text-align: right;
	color: #ebf4ff;
}

.lottery-list-item__fund-currency {
	width: 20px;
	height: 20px;
}

.lottery-list-item__fund-currency .image {
	width: 100%;
}

.lottery-list-item__price-block {
	margin-top: 32px;
}

.lottery-list-item__price {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.lottery-list-item__price-left {}

.lottery-list-item__price-title {
	font-weight: 500;
	font-size: 12px;
	line-height: 1;
	text-transform: uppercase;
	color: #406084;
	max-width: 70px;
}

.lottery-list-item__price-right {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.lottery-list-item__price-amount {
	max-width: calc(100% - 20px);
	padding-right: 4px;

	font-weight: 500;
	font-size: 24px;
	line-height: 1.25;
	text-align: right;
	color: #ebf4ff;
}

.lottery-list-item__price-currency {
	width: 20px;
	height: 20px;
}

.lottery-list-item__price-currency .image {
	width: 100%;
}

.lottery-list-item__ticket-count-items-block {
	margin-top: 20px;
}

.lottery-list-item__ticket-count-items {
	margin-left: -6px;
	margin-right: -6px;
	margin-top: -12px;
	display: flex;
	flex-wrap: wrap;
}

.lottery-list-item__ticket-count-item-wrapper {
	padding-left: 6px;
	padding-right: 6px;
	width: 50%;
	margin-top: 12px;
}

.lottery-list-item__ticket-count-item {
	padding: 7px 12px;
	background-color: #0E1B28;
	border-radius: 8px;
	display: flex;
}

.lottery-list-item__ticket-count-item__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.lottery-list-item__ticket-count-item__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/ticket.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/ticket.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
}

.lottery-list-item__ticket-count-item__content {
	width: calc(100% - 24px);
	padding-left: 8px;
}


.lottery-list-item__ticket-count-item__count {
	font-size: 20px;
	line-height: 1.25;
	color: #ebf4ff;
}

.lottery-list-item__ticket-count-item__descr {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.lottery-list-item__ticket-count-item--saled {}

.lottery-list-item__ticket-count-item--saled .lottery-list-item__ticket-count-item__icon::before {
	background-color: #17C964;
	mask-image: url('../images/svg/ticket-fill.svg');
	-webkit-mask-image: url('../images/svg/ticket-fill.svg');
}

.lottery-list-item__ticket-count-item--not-saled {}

.lottery-list-item__ticket-count-item--not-saled .lottery-list-item__ticket-count-item__icon::before {
	background-color: #BD3D44;
	mask-image: url('../images/svg/ticket-fill.svg');
	-webkit-mask-image: url('../images/svg/ticket-fill.svg');
}

.lottery-list-item__participants-block {
	margin-top: 20px;
}

.lottery-list-item__participants {
	display: flex;
	align-items: center;
}

.lottery-list-item__participants__images-block {
	width: 54px;
}

.lottery-list-item__participants__images {
	display: flex;
	align-items: center;
}

.lottery-list-item__participants__image {
	width: 24px;
	height: 24px;
	border-radius: 24px;
	box-shadow: 0 0 0 1.5px #14202d;
}

.lottery-list-item__participants__image .image {
	width: 100%;
	height: 100%;
	border-radius: 24px;
	object-fit: contain;
}

.lottery-list-item__participants__image+.lottery-list-item__participants__image {
	margin-left: -9px;
}

.lottery-list-item__participants__title-block {
	width: calc(100% - 54px);
	padding-left: 8px;
}

.lottery-list-item__participants__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.lottery-list-item__buy-btn-block {
	margin-top: 8px;
}

.lottery-list-item__buy-btn {
	font-size: 16px;
	padding-top: 11px;
	padding-bottom: 11px;
	width: 100%;
}

.lottery-list-item__buy-btn .iconed-btn__icon::before {
	mask-image: url('../images/svg/ticket-fill.svg');
	-webkit-mask-image: url('../images/svg/ticket-fill.svg');
}

.lottery-list-item__bottom-block {
	margin-top: 20px;
}

.lottery-list-item__bottom {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.lottery-list-item__bottom-left {
	padding-right: 12px;
}

.lottery-list-item__creator-title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.lottery-list-item__bottom-right {}

.lottery-list-item__creator {
	display: flex;
	align-items: center;
}

.lottery-list-item__creator__name {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	text-align: right;
	color: #406084;
	padding-right: 8px;
}

.lottery-list-item__creator__image {
	width: 16px;
	height: 16px;
	border-radius: 50%;
}

.lottery-list-item__creator__image .image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 50%;
}


.lottery-list-item--pending {
	background-image: url('../images/theme/ticket-bg--pending.png');
}

.lottery-list-item--pending .lottery-list-item__fund-block {
	margin-top: 16px;
}

.lottery-list-item--pending .lottery-list-item__ticket-count-items-block {
	margin-top: 16px;
}

.lottery-list-item--pending .lottery-list-item__participants-block {
	margin-top: 16px;
}

.lottery-list-item--pending .lottery-list-item__bottom-block {
	margin-top: 16px;
}


.lottery-list-item__status-block {}

.lottery-list-item__status {
	display: flex;
	align-items: center;
	justify-content: center;
}

.lottery-list-item__status__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.lottery-list-item__status__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/hourglass.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/hourglass.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #E5AA35;
}

.lottery-list-item__status__text {
	font-weight: 500;
	font-size: 14px;
	line-height: 125%;
	color: #e5aa35;
}

.lottery-list-show-more-btn-block {
	margin-top: 24px;
	text-align: center;
}

.lottery-list-show-more-btn {
	width: 100%;
	max-width: 300px;
	font-size: 16px;
}

.lottery-list-item__countdown-block {
	margin-top: 12px;
	position: relative;
}

.lottery-list-item__countdown__time {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	color: #ebf4ff;
	z-index: 2;
}

.lottery-list-item__countdown__progress-block {
	background: #0d1822;
	position: relative;
	height: 48px;
	border-radius: 8px;
	overflow: hidden;
}

.lottery-list-item__countdown__progress {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 0;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.lottery-list-item--complete {
	background-image: url('../images/theme/ticket-bg--complete.png');
}

.lottery-list-item__winner {
	margin-top: 14px;
}

.lottery-list-item__winner__title {
	font-weight: 500;
	font-size: 12px;
	line-height: 1;
	text-transform: uppercase;
	text-align: center;
	color: #406084;
}

.lottery-list-item__winner__image-block {
	margin-top: 14px;
	width: 64px;
	height: 64px;
	margin-right: auto;
	margin-left: auto;
	position: relative;
}


.lottery-list-item__winner__image-block::before {
	content: '';
	position: absolute;
	top: -2px;
	left: -2px;
	width: 68px;
	height: 68px;
	border-radius: 68px;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.lottery-list-item__winner__image {
	width: 64px;
	height: 64px;
	position: relative;
	z-index: 2;
}

.lottery-list-item__winner__image .image {
	width: 64px;
	height: 64px;
	border-radius: 64px;
	object-fit: contain;
}

.lottery-list-item__winner__name {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	text-align: center;
	color: #ebf4ff;
	margin-top: 8px;
}

.lottery-list-item__winner__tickets-block {
	margin-top: 8px;
}

.lottery-list-item__winner__tickets {
	display: flex;
	justify-content: center;
	margin-left: -4px;
	margin-top: -4px;
	margin-right: -4px;
}

.lottery-list-item__winner__ticket-wrapper {
	margin-top: 4px;
	padding-left: 4px;
	padding-right: 4px;
}

.lottery-list-item__winner__ticket {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 4px 6px;
	background: #0e1b28;
	border-radius: 24px;
}

.lottery-list-item__winner__ticket__count {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
	padding-right: 4px;
}

.lottery-list-item__winner__ticket__icon {
	position: relative;
	width: 16px;
	height: 16px;
}

.lottery-list-item__winner__ticket__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/ticket.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/ticket.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #406084;
}

.lottery-list-item__winner__ticket__descr {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
	padding-left: 4px;
}

.lottery-list-item__winner__ticket--sale {}

.lottery-list-item__winner__ticket--sale .lottery-list-item__winner__ticket__icon::before {
	mask-image: url('../images/svg/ticket-fill.svg');
	-webkit-mask-image: url('../images/svg/ticket-fill.svg');
	background-color: #20B16C;
}

.lottery-list-item__winner__ticket--sale .lottery-list-item__winner__ticket__descr {
	display: none;
}

.lottery-list-item__winner__participants-block {
	margin-top: 14px;
}

.lottery-list-item__winner__participants {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.lottery-list-item__winner__participants__left {
	padding-right: 12px;
}

.lottery-list-item__winner__participants__right {
	display: flex;
	align-items: center;
}

.lottery-list-item__winner__participants__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
	padding-right: 4px;
}

.lottery-list-item__winner__participants__count {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	text-align: right;
	color: #ebf4ff;
}

.lottery-list-item__winner__prize-block {
	margin-top: 14px;
	background: #0e1b28;
	border-radius: 8px;
	padding: 12px 32px;
}

.lottery-list-item__winner__prize-title {
	font-weight: 500;
	font-size: 12px;
	line-height: 1;
	text-transform: uppercase;
	text-align: center;
	color: #406084;
}

.lottery-list-item__winner__prize {
	margin-top: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.lottery-list-item__winner__prize__amount {
	padding-right: 8px;
	font-weight: 500;
	font-size: 24px;
	line-height: 1.25;
}

.lottery-list-item__winner__prize__amount span {
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.lottery-list-item__winner__prize__currency {
	width: 24px;
	height: 24px;
}

.lottery-list-item__winner__prize__currency .image {
	width: 100%;
}

.lottery-list-item__finished-block {
	margin-top: 14px;
}

.lottery-list-item__finished {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.lottery-list-item__finished__left {
	padding-right: 12px;
}

.lottery-list-item__finished__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;

}

.lottery-list-item__finished__right {}

.lottery-list-item__finished__date {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.bs-placeholder {}

.bootstrap-select .dropdown-menu li a.bs-placeholder {
	display: none;
}

.select-currency-list-item__icon--currency {}

.select-currency-list-item__icon--currency::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/currency-circle-dollar.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/currency-circle-dollar.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #406084;
}

.select-currency-list-item--placeholder .select-currency-list-item__title {
	color: #406084;
	font-weight: 400;
}

.lottery-list-filter-form .custom-checkbox-label {
	align-items: center;
}

.lottery-list-filter-form .custom-checkbox-label-text {
	font-size: 16px;
}

.topline--autorized {}



.mobile-account-panel-block {}

.mobile-account-panel {
	display: flex;
	flex-wrap: wrap;
	margin-left: -4px;
	margin-right: -4px;
}

.mobile-account-panel-balance {
	width: 100%;
	padding-left: 4px;
	padding-right: 4px;
}

.mobile-account-panel-balance .topline-balance-btn {
	height: 40px;
}

.mobile-account-panel-balance .topline-balance-btn__content {
	font-size: 14px;
}

.mobile-account-panel-dashboard {
	width: calc(100% - 48px);
	padding-left: 4px;
	padding-right: 4px;
	padding-top: 8px;
}

.mobile-account-panel-dashboard .topline-dashboard-btn-block {
	padding-left: 0;
}

.mobile-account-panel-dashboard .topline-dashboard-btn {
	width: 100%;
	font-size: 14px;
}

.mobile-account-panel-logout {
	padding-left: 4px;
	padding-right: 4px;
	width: 48px;
	padding-top: 8px;
}

.lottery-list-filter-panel-btn-block {
	padding-top: 4px;
	padding-bottom: 4px;
	display: none;
}

.lottery-list-filter-panel-btn {
	display: flex;
	align-items: center;
	text-decoration: none;
	border: 0;
	padding: 0;
}

.lottery-list-filter-panel-btn__icon {
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	position: relative;
}

.lottery-list-filter-panel-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/caret-down.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/caret-down.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
	transition: 0.4s ease;
}


.lottery-list-filter-panel-btn__text {
	width: calc(100% - 44px);
	padding-left: 12px;
	font-weight: 500;
	font-size: 20px;
	line-height: 1.25;
	color: #406084;
}


.lottery-list-filter-panel-btn.active .lottery-list-filter-panel-btn__icon::before {
	transform: translate(-50%, -50%) rotate(-180deg);
}


.lottery-list-items-empty {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	height: 780px;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 48px;
}

.lottery-list-items-empty__image {}

.lottery-list-items-empty__descr {
	margin-top: 16px;
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #fff;
}

.lottery-list-items-empty__btn-block {
	margin-top: 16px;
	display: flex;
}

.lottery-list-items-empty__btn {
	font-size: 16px;
	padding-top: 11px;
	padding-bottom: 11px;
	padding-left: 11px;
	padding-right: 11px;
	width: 100%;
}


.lottery-list-items-empty__btn .iconed-btn__icon::before {
	mask-image: url('../images/svg/trash-simple.svg');
	-webkit-mask-image: url('../images/svg/trash-simple.svg');
}



.section-lottery-detail {
	padding-top: 70px;
}

.section-related-lotteries {
	padding-top: 48px;
	padding-bottom: 80px;
}

.s-related-lotteries-top-block {}

.s-related-lotteries-top {
	padding-top: 7px;
	padding-bottom: 7px;
	padding-right: 100px;
}

.s-related-lotteries-title {
	color: #ebf4ff;
}

.swiper-button-disabled {
	opacity: 0.4;
	cursor: default;
	pointer-events: none;
}

.related-lottery-slider-block {
	position: relative;
	margin-top: 32px;
}

.related-lottery-slider {
	position: relative;
	margin-left: -16px;
	margin-right: -16px;
}

.related-lottery-slider .swiper-button-next,
.related-lottery-slider .swiper-button-prev {
	position: absolute;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	top: -76px;
	margin-top: 0;
	bottom: auto;
	background: #172a3e;
}

.related-lottery-slider .swiper-button-next::after,
.related-lottery-slider .swiper-button-prev::after {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.related-lottery-slider .swiper-button-next {
	right: 16px;
}

.related-lottery-slider .swiper-button-prev {
	right: 68px;
}


.related-lottery-slider .swiper-button-next::after {
	mask-image: url('../images/svg/caret-right.svg');
	-webkit-mask-image: url('../images/svg/caret-right.svg');
}

.related-lottery-slider .swiper-button-prev::after {
	mask-image: url('../images/svg/caret-left.svg');
	-webkit-mask-image: url('../images/svg/caret-left.svg');
}

.related-lottery-slide {
	padding-left: 16px;
	padding-right: 16px;
	height: auto;
}


.lottery-detail-block {}

.lottery-detail {
	display: flex;
}

.lottery-detail-left {
	width: calc(100% - 415px);
	padding-right: 12px;
}

.lottery-detail-right {
	width: 415px;
}

.lottery-detail-info {
	border-radius: 8px;
	padding: 48px;
	background: #14202d;
}

.back-link-block {}

.back-link {
	display: inline-flex;
	text-decoration: none;
}

.back-link__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.back-link__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/arrow-circle-left.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/arrow-circle-left.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	transition: 0.4s ease;
}

.back-link__text {
	padding-top: 4px;
	padding-bottom: 4px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	color: #406084;
	padding-left: 12px;
	max-width: calc(100% - 24px);
}

.lottery-detail-info-top-block {
	margin-top: 8px;
}

.lottery-detail-info-top {
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
}

.lottery-detail-info-top__left {
	padding-right: 12px;
}

.lottery-detail-info-top__right {}

.lottery-detail-info-top__title {
	color: #ebf4ff;
}

.lottery-detail-info-creator {}

.lottery-detail-info-creator__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
	text-align: right;
}

.lottery-detail-info-creator__content {
	padding-top: 4px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.lottery-detail-info-creator__name {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	text-align: right;
	color: #ebf4ff;
	padding-right: 8px;
	max-width: calc(100% - 16px);
}

.lottery-detail-info-creator__image {
	width: 16px;
	height: 16px;
	border-radius: 16px;
	position: relative;
}

.lottery-detail-info-creator__image .image {
	width: 100%;
	height: 100%;
	object-fit: contain;
	border-radius: 16px;
}

.lottery-detail-info-progress-block {
	margin-top: 24px;
}

.lottery-detail-info-progress {
	background-color: #0D1822;
	height: 8px;
	width: 100%;
	border-radius: 8px;
	position: relative;
}

.lottery-detail-info-progress__progress {
	left: 0;
	top: 0;
	width: 0;
	height: 100%;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	position: absolute;
	border-radius: 8px;
}

.lottery-detail-info-items-block {
	margin-top: 24px;
}

.lottery-detail-info-items {
	display: flex;
	margin-top: -16px;
	margin-left: -8px;
	margin-right: -8px;
}

.lottery-detail-info-item-wrapper {
	flex-grow: 1;
	margin-top: 16px;
	padding-left: 8px;
	padding-right: 8px;
}

.lottery-detail-info-item {
	padding: 24px;
	background: #0e1b28;
	border-radius: 8px;
	height: 100%;
}

.lottery-detail-info-item__icon-block {
	width: 40px;
	height: 40px;
	margin-right: auto;
	margin-left: auto;
	background: #172a3e;
	border-radius: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.lottery-detail-info-item__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.lottery-detail-info-item__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	transition: 0.4s ease;
}

.lottery-detail-info-item__title {
	margin-top: 24px;
	font-weight: 500;
	font-size: 12px;
	line-height: 1;
	text-transform: uppercase;
	text-align: center;
	color: #406084;
}

.lottery-detail-info-item__content {
	padding-top: 8px;
}

.lottery-detail-info-item__value {
	display: flex;
	align-items: center;
	justify-content: center;
}

.lottery-detail-info-item__value__text {
	font-weight: 500;
	font-size: 24px;
	line-height: 1.25;
	color: #ebf4ff;
}

.lottery-detail-info-item__value__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.lottery-detail-info-item__value__icon .image {
	width: 100%;
}

.lottery-detail-info-item--prize {}

.lottery-detail-info-item--prize .lottery-detail-info-item__icon::before {
	mask-image: url('../images/svg/trophy-fill.svg');
	-webkit-mask-image: url('../images/svg/trophy-fill.svg');
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.lottery-detail-info-item--prize .lottery-detail-info-item__value__text {
	padding-right: 8px;
	max-width: calc(100% - 24px);
}

.lottery-detail-info-item--prize .lottery-detail-info-item__value__text span {
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}


.lottery-detail-info-item--price {}

.lottery-detail-info-item--price .lottery-detail-info-item__icon::before {
	mask-image: url('../images/svg/shopping-bag-open.svg');
	-webkit-mask-image: url('../images/svg/shopping-bag-open.svg');
	background-color: #0170EF;
}

.lottery-detail-info-item--price .lottery-detail-info-item__value__text {
	padding-right: 8px;
	color: #fff;
	max-width: calc(100% - 24px);
}

.lottery-detail-info-item--total {}

.lottery-detail-info-item--total .lottery-detail-info-item__icon::before {
	mask-image: url('../images/svg/ticket.svg');
	-webkit-mask-image: url('../images/svg/ticket.svg');
	background-color: #406084;
}

.lottery-detail-info-item--sale {}

.lottery-detail-info-item--sale .lottery-detail-info-item__icon::before {
	mask-image: url('../images/svg/ticket-fill.svg');
	-webkit-mask-image: url('../images/svg/ticket-fill.svg');
	background-color: #406084;
}

.lottery-detail-info-item--sale .lottery-detail-info-item__value__text {
	color: #406084;
}

.lottery-detail-info-item--sale-green .lottery-detail-info-item__icon::before {
	background-color: #20B16C;
}

.lottery-detail-info-item--sale-green .lottery-detail-info-item__value__text {
	color: #17c964;
}


.lottery-detail-info-purchase-form-block {
	margin-top: 24px;
}

.lottery-detail-info-purchase-form-title {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.lottery-detail-info-purchase-form {
	max-width: 530px;
	margin-top: 6px;
}

.lottery-detail-info-purchase-form-row {
	display: flex;
}

.lottery-detail-info-purchase-form__field {
	width: calc(100% - 300px);
	padding-right: 16px;
}

.lottery-detail-info-purchase-form__btn-block {
	width: 300px;
}



.lottery-detail-info-purchase-form__btn {
	font-size: 16px;
	padding-top: 11px;
	padding-bottom: 11px;
	width: 100%;
}

.lottery-detail-info-purchase-form__btn .iconed-btn__icon::before {
	mask-image: url('../images/svg/ticket-fill.svg');
	-webkit-mask-image: url('../images/svg/ticket-fill.svg');
}



.field--count {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 5px;
	position: relative;
}


.field--count input[type='text'],
.field--count input[type='email'],
.field--count input[type='password'] {
	padding-left: 48px;
	padding-right: 48px;
	text-align: center;
}

.field-count-btn {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	border-radius: 8px;
	padding: 6px;
	width: 32px;
	height: 32px;
	background: #1a314b;
	border: 0;
}

.field-count-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #EBF4FF;
	transition: 0.4s ease;
}

.field-count-btn--minus {
	left: 8px;
}

.field-count-btn--minus::before {
	mask-image: url('../images/svg/minus.svg');
	-webkit-mask-image: url('../images/svg/minus.svg');
}

.field-count-btn--plus {
	right: 8px;
}

.field-count-btn--plus::before {
	mask-image: url('../images/svg/plus.svg');
	-webkit-mask-image: url('../images/svg/plus.svg');
}



.lottery-detail-participants-block {
	margin-top: 32px;
}

.lottery-detail-participants {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.lottery-detail-participants__left {
	padding-right: 12px;
}

.lottery-detail-participants__content {
	display: flex;
	align-items: center;
}

.lottery-detail-participants__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
	padding-right: 4px;
}


.lottery-detail-participants__count {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #ebf4ff;
}

.lottery-detail-participants__right {}

.lottery-detail-participants__images-block {
	width: 54px;
}

.lottery-detail-participants__images {
	display: flex;
	align-items: center;
}

.lottery-detail-participants__image {
	width: 24px;
	height: 24px;
	border-radius: 24px;
	box-shadow: 0 0 0 1.5px #14202d;
}

.lottery-detail-participants__image .image {
	width: 100%;
	height: 100%;
	object-fit: contain;
	border-radius: 24px;
}

.lottery-detail-participants__image+.lottery-detail-participants__image {
	margin-left: -9px;
}


.lottery-detail-download-block {}

.lottery-detail-download {
	background: #14202d;
	display: flex;
	border-radius: 8px;
	padding: 24px;
	text-decoration: none;
}

.lottery-detail-download__icon {
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	background: #0e1b28;
	position: relative;
	display: block;
}

.lottery-detail-download__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/cloud-arrow-down.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/cloud-arrow-down.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	transition: 0.4s ease;
}

.lottery-detail-download__content {
	padding-left: 16px;
	width: calc(100% - 44px);
}

.lottery-detail-download__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #ebf4ff;
}

.lottery-detail-download__title a {
	color: #ebf4ff;
	text-decoration: none;
}

.lottery-detail-download__title a {
	color: #ebf4ff;
	text-decoration: none;
}

.lottery-detail-download__descr {
	font-weight: 400;
	font-size: 14px;
	line-height: 1.45;
	color: #7998ba;
}



.lottery-detail-guarantee-btn-block {
	margin-top: 12px;
}

.lottery-detail-guarantee-btn {
	background: #14202d;
	display: flex;
	border-radius: 8px;
	padding: 24px;
	text-decoration: none;
}

.lottery-detail-guarantee-btn__icon {
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	background: #17C964;
	position: relative;
}

.lottery-detail-guarantee-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/shield-check.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/shield-check.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
	transition: 0.4s ease;
}

.lottery-detail-guarantee-btn__content {
	padding-left: 16px;
	width: calc(100% - 44px);
	position: relative;
	padding-right: 20px;
}

.lottery-detail-guarantee-btn__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #ebf4ff;
	padding-right: 8px;
}

.lottery-detail-guarantee-btn__descr {
	font-weight: 400;
	font-size: 14px;
	line-height: 1.45;
	color: #7998ba;
}

.lottery-detail-guarantee-btn__arrow {
	position: absolute;
	width: 20px;
	height: 20px;
	top: 0;
	right: 0;
}

.lottery-detail-guarantee-btn__arrow::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/arrow-circle-up-right.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/arrow-circle-up-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #406084;
	transition: 0.4s ease;
}


.lottery-detail-ticket-table-block {
	margin-top: 12px;
	padding: 12px;
	padding-top: 0;
	background: #14202d;
	border-radius: 8px;
}

.lottery-detail-ticket-table {}

.lottery-detail-ticket-t-heading {
	display: flex;
}

.lottery-detail-ticket-t-h-col {
	width: 50%;
	padding: 14px 12px;
}

.lottery-detail-ticket-t-h-col__value {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.lottery-detail-ticket-t-h-col--participant {}

.lottery-detail-ticket-t-h-col--tickets {}

.lottery-detail-ticket-t-items {}

.lottery-detail-ticket-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
}

.lottery-detail-ticket-t-item:nth-child(odd) {
	display: flex;
	background: #0e1b28;
}


.lottery-detail-ticket-t-i-col {
	width: 50%;
	padding: 14px 12px;
	display: flex;
	align-items: center;
}

.lottery-detail-ticket-t-i-participant {
	display: flex;
	align-items: center;
}

.lottery-detail-ticket-t-i-participant__image {
	width: 16px;
	height: 16px;
	border-radius: 16px;
	position: relative;
}

.lottery-detail-ticket-t-i-participant__image .image {
	width: 100%;
	height: 100%;
	border-radius: 16px;
	object-fit: cover;
}

.lottery-detail-ticket-t-i-participant__content {
	padding-left: 8px;
	width: calc(100% - 16px);
}

.lottery-detail-ticket-t-i-participant__name {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
}

.lottery-detail-ticket-t-i-tickets {}

.lottery-detail-ticket-t-i-tickets-block {
	display: flex;
	position: relative;
}

.lottery-detail-ticket-t-i-tickets {
	display: flex;
	align-items: center;
	position: relative;
	cursor: default;
}

.lottery-detail-ticket-t-i-tickets__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.lottery-detail-ticket-t-i-tickets__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/ticket-fill.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/ticket-fill.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #406084;
	transition: 0.4s ease;
}

.lottery-detail-ticket-t-i-tickets__count {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
}

.lottery-detail-ticket-t-i-tickets-tooltip {
	position: absolute;
	left: calc(100% + 12px);
	top: 50%;
	transform: translateY(-50%);
	background: #0e1b28;
	font-weight: 400;
	font-size: 14px;
	line-height: 1.45;
	color: #7998ba;
	border: 1px solid #1a314b;
	border-radius: 4px;
	padding: 6px 8px;
	min-width: 120px;
	max-width: 120px;
	opacity: 0;
	visibility: hidden;
	pointer-events: none;
	transition: 0.4s ease;
	word-wrap: break-word;
}

.lottery-detail-ticket-t-i-tickets-tooltip::before {
	content: '';
	position: absolute;
	right: 100%;
	top: 50%;
	transform: translateY(-50%);
	width: 5px;
	height: 12px;
	mask-image: url('../images/svg/tooltip-arrow.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 5px 12px;
	-webkit-mask-image: url('../images/svg/tooltip-arrow.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 5px 12px;
	background-color: #1A314B;
}

.lottery-detail-ticket-t-i-tickets:hover .lottery-detail-ticket-t-i-tickets__icon::before {
	background-color: #17C964;
}

.lottery-detail-ticket-t-i-tickets:hover+.lottery-detail-ticket-t-i-tickets-tooltip {
	opacity: 1;
	visibility: visible;
}


.lottery-detail-ticket-t-i-col--participant {}

.lottery-detail-ticket-t-i-col--tickets {}


.pagination-block {
	margin-top: 8px;
}

.pagination {
	margin-left: -4px;
	margin-right: -4px;
}

.pagination ul {
	display: flex;
	align-items: center;
	list-style-type: none;
	padding: 0;
	margin: 0;
}

.pagination ul li {
	display: flex;
	align-items: center;
	justify-content: center;
	padding-left: 4px;
	padding-right: 4px;
}

.pagination ul li span {
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	line-height: 1;
	color: #406084;
	cursor: default;
	text-transform: uppercase;
}

.pagination ul li a {
	border: 1px solid #1a314b;
	border-radius: 8px;
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	line-height: 1;
	color: #406084;
	text-decoration: none;
}

.pagination ul li a:hover {
	color: #e6e6e6;
}

.pagination ul li.first a {
	width: 32px;
	height: 32px;
	position: relative;
}

.pagination ul li.first a::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/caret-left.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/caret-left.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.pagination ul li.first a:hover::before {
	background-color: #EBF4FF;
}

.pagination ul li.last a {
	width: 32px;
	height: 32px;
	position: relative;
}

.pagination ul li.last a::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/caret-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/caret-right.svg');
	background-color: #406084;
}

.pagination ul li.last a:hover::before {
	background-color: #EBF4FF;
}

.pagination ul li.active a {
	color: #e6e6e6;
}


.lottery-detail-info-status-block {
	margin-top: 24px;
}

.lottery-detail-info-status {
	display: flex;
	align-items: center;
}

.lottery-detail-info-status__icon {
	position: relative;
	width: 16px;
	height: 16px;
}

.lottery-detail-info-status__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #406084;
}

.lottery-detail-info-status__text {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #e5aa35;
	width: calc(100% - 16px);
	padding-left: 10px;
}

.lottery-detail-info-status--pending {}

.lottery-detail-info-status--pending .lottery-detail-info-status__icon::before {
	mask-image: url('../images/svg/hourglass.svg');
	-webkit-mask-image: url('../images/svg/hourglass.svg');
	background-color: #E5AA35;
}

.lottery-detail-info-status--pending .lottery-detail-info-status__text {
	color: #e5aa35;
}


.lottery-detail-info-status--finished {}

.lottery-detail-info-status--finished .lottery-detail-info-status__icon::before {
	mask-image: url('../images/svg/check-circle.svg');
	-webkit-mask-image: url('../images/svg/check-circle.svg');
	background-color: #17C964;
}

.lottery-detail-info-status--finished .lottery-detail-info-status__text {
	color: #17C964;
}

.lottery-detail-info-countdown-block {
	margin-top: 32px;
}

.lottery-detail-info-countdown {
	position: relative;
	border-radius: 8px;
	height: 48px;
	background-color: #0d1822;
	overflow: hidden;
}

.lottery-detail-info-countdown__icon {
	position: absolute;
	z-index: 2;
	border-radius: 8px;
	width: 40px;
	height: 40px;
	background: #172a3e;
	left: 4px;
	top: 50%;
	transform: translateY(-50%);
}

.lottery-detail-info-countdown__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/timer.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/timer.svg');
	background-color: #EBF4FF;
}

.lottery-detail-info-countdown__progress {
	width: 0;
	height: 100%;
	left: 0;
	top: 0;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.lottery-detail-info-countdown__time {
	position: absolute;
	font-weight: 500;
	font-size: 16px;
	line-height: 1.35;
	color: #ebf4ff;
	right: 14px;
	top: 50%;
	transform: translateY(-50%);
}

.lottery-detail-info-winner-block {
	margin-top: 16px;
}

.lottery-detail-info-winner {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16px;
	background-color: #0e1b28;
	border-radius: 8px;
}

.lottery-detail-info-winner__image-block {
	width: 72px;
	height: 72px;
	border-radius: 72px;
	position: relative;
}


.lottery-detail-info-winner__image-block::before {
	content: '';
	position: absolute;
	top: -2px;
	left: -2px;
	width: 76px;
	height: 76px;
	border-radius: 76px;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.lottery-detail-info-winner__image {
	width: 72px;
	height: 72px;
	object-fit: contain;
	border-radius: 72px;
	position: relative;
	z-index: 2;
}

.lottery-detail-info-winner__image .image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 72px;
}

.lottery-detail-info-winner__content {
	padding-left: 16px;
	max-width: calc(100% - 72px);
}


.lottery-detail-info-winner__title {
	font-weight: 500;
	font-size: 12px;
	line-height: 1;
	text-transform: uppercase;
	color: #406084;
}

.lottery-detail-info-winner__name {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #ebf4ff;
	margin-top: 6px;
}

.lottery-detail-info-winner__tickets-block {
	margin-top: 6px;
}

.lottery-detail-info-winner__tickets {
	display: flex;
	justify-content: flex-start;
	margin-left: -4px;
	margin-top: -4px;
	margin-right: -4px;
}

.lottery-detail-info-winner__ticket-wrapper {
	margin-top: 4px;
	padding-left: 4px;
	padding-right: 4px;
}

.lottery-detail-info-winner__ticket {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 4px 6px;
	background: #172a3e;
	border-radius: 24px;
}

.lottery-detail-info-winner__ticket__count {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #ebf4ff;
	padding-right: 4px;
}

.lottery-detail-info-winner__ticket__icon {
	position: relative;
	width: 16px;
	height: 16px;
}

.lottery-detail-info-winner__ticket__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/ticket.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/ticket.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #ebf4ff;
}

.lottery-detail-info-winner__ticket__descr {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 4px;
}

.lottery-detail-info-winner__ticket--sale {}

.lottery-detail-info-winner__ticket--sale .lottery-detail-info-winner__ticket__icon::before {
	mask-image: url('../images/svg/ticket-fill.svg');
	-webkit-mask-image: url('../images/svg/ticket-fill.svg');
	background-color: #20B16C;
}

.lottery-detail-info-winner__ticket--sale .lottery-detail-info-winner__ticket__descr {
	display: none;
}

.copy-link {
	position: relative;
	display: inline-flex;
	align-items: center;
	text-decoration: none;
	color: #20b16c;
	padding: 0;
	border: 0;
}

.copy-link:hover {
	position: relative;
	display: inline-flex;
	text-decoration: none;
	color: #20b16c;
}

.copy-link::after {
	pointer-events: none;
	content: attr(aria-label);
	position: absolute;
	text-transform: uppercase;
	z-index: 1;
	font-weight: 700;
	font-size: 11px;
	line-height: 1;
	left: 50%;
	transform: translateX(-50%);
	white-space: nowrap;
	text-transform: uppercase;
	/* background-color: #14202d;
	color: #20B16C; */
	background-color: #17C964;
	color: #ebf4ff;
	padding: 4px;
	top: -20px;
	border-radius: 4px;
	opacity: 0;
	visibility: hidden;
	transition: 0.4s ease;
}

.copy-link__text {
	padding-right: 4px;
}

.copy-link__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.copy-link__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/copy.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/copy.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #384BF2;
	transition: 0.4s ease;
}


.copy-link.active {}

.copy-link.active .copy-link__text {
	color: #1DA025;
}

.copy-link.active .copy-link__icon::before {
	background-color: #1DA025;
}

.copy-link.active::after {
	opacity: 1;
	visibility: visible;
}


.section-lottery-create {
	padding-top: 70px;
	padding-bottom: 40px;
}



.s-lottery-create-block {}

.s-lottery-create {
	display: flex;
}

.s-lottery-create-left {
	width: calc(100% - 415px);
	padding-right: 12px;
}

.s-lottery-create-right {
	width: 415px;
}

.lottery-create-block {
	border-radius: 8px;
	padding: 48px;
	background: #14202d;
}

.lottery-create-title-block {
	margin-top: 24px;
}

.lottery-create-title {
	color: #ebf4ff;
}

.lottery-create-form-block {
	margin-top: 24px;
}

.lottery-create-form {}

.lottery-create-form-row {}


.lottery-create-form-row .row,
.lottery-create-form-row .row>* {
	--bs-gutter-x: 16px;
}

.lottery-create-form-row .field-block:first-child {
	margin-top: 16px;
}

.lottery-create-form-payment-block {
	margin-top: 24px;
	max-width: 380px;
}

.lottery-create-form-payment-top-block {}

.lottery-create-form-payment-top {
	display: flex;
	align-items: center;
}

.lottery-create-form-payment-top__icon {
	position: relative;
	border-radius: 8px;
	width: 44px;
	height: 44px;
	background-color: #0E1B28;
}

.lottery-create-form-payment-top__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/shopping-bag-open.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/shopping-bag-open.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	transition: 0.4s ease;
}

.lottery-create-form-payment-top__title-block {
	max-width: calc(100% - 44px);
	padding-left: 16px;
}

.lottery-create-form-payment-top__title {
	color: #ebf4ff;
}

.lottery-create-form-payment-prize-block {
	margin-top: 16px;
}

.lottery-create-form-payment-prize {
	background: #0e1b28;
	border-radius: 8px;
	padding: 16px 24px;
	display: flex;
}

.lottery-create-form-payment-prize__icon {
	width: 40px;
	height: 40px;
	background: #172a3e;
	border-radius: 40px;
	position: relative;
}

.lottery-create-form-payment-prize__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/trophy-fill.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/trophy-fill.svg');
	background-color: #406084;
	transition: 0.4s ease;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.lottery-create-form-payment-prize__content {
	padding-left: 16px;
	width: calc(100% - 40px);
}

.lottery-create-form-payment-prize__title {
	font-weight: 500;
	font-size: 12px;
	line-height: 1;
	text-transform: uppercase;
	color: #406084;
}

.lottery-create-form-payment-prize__descr {
	font-weight: 400;
	font-size: 12px;
	line-height: 1.45;
	color: #7998ba;
	margin-top: 8px;
}

.lottery-create-form .form-button-block button {
	width: 100%;
}

.lottery-create-banner-block {
	height: 100%;
}

.lottery-create-banner {
	border-radius: 12px;
	height: 100%;
	background: linear-gradient(120deg, #17c964 0%, #006fee 100%);
	position: relative;
	padding-left: 48px;
	padding-right: 48px;
	padding-top: 80px;
	padding-bottom: 360px;
	overflow: hidden;
}

.lottery-create-banner-bg-image {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 340px;
}

.lottery-create-banner-bg-image .image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}


.lottery-create-banner-info-slider-block {
	position: relative;
	max-width: 262px;
	margin-right: auto;
	margin-left: auto;
}

.lottery-create-banner-info-slider {
	position: relative;
}

.lottery-create-banner-info-slider .swiper-pagination {
	margin-top: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.lottery-create-banner-info-slider .swiper-pagination .swiper-pagination-bullet {
	width: 8px;
	height: 8px;
	border-radius: 8px;
	margin-left: 2px;
	margin-right: 2px;
	background-color: rgba(255, 255, 255, 0.5);
	border: 1px solid transparent;
	position: relative;
	opacity: 1;
}

.lottery-create-banner-info-slider .swiper-pagination .swiper-pagination-bullet::before {
	display: none;
}

.lottery-create-banner-info-slider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
	background-color: #FFFFFF;
}


.lottery-create-banner-info-slide {
	display: flex;
	align-items: flex-end;
	height: auto;
}

.lottery-create-banner-info-slide-title {
	width: 100%;
	font-weight: 500;
	font-size: 24px;
	line-height: 1.25;
	text-align: center;
	color: #ebf4ff;
}

.lottery-create-form-payment-prize__amount {
	display: flex;
	align-items: center;
}

.lottery-create-form-payment-prize__amount__amount {
	padding-right: 8px;
	font-weight: 500;
	font-size: 24px;
	line-height: 1.25;
}

.lottery-create-form-payment-prize__amount__amount span {
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.lottery-create-form-payment-prize__amount__count {
	width: 24px;
	height: 24px;
	position: relative;
}

.lottery-create-form-payment-prize__amount__count .image {
	width: 100%;
}



.section-games-list {
	padding-top: 70px;
	padding-bottom: 40px;
}

.games-list-top-block {}

.games-list-top {}

.games-list-top-title-block {}

.games-list-top-title {
	color: #fff;
}

.games-list-top-descr-block {
	margin-top: 16px;
}

.games-list-top-descr {
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.games-list-filter-block {
	margin-top: 20px;
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.games-list-filter {}

.games-list-filter-form {}

.games-list-filter-form-row {
	display: flex;
	margin-left: -6px;
	margin-right: -6px;
}

.games-list-filter-form-col {
	padding-left: 6px;
	padding-right: 6px;
}

.games-list-filter-form-col--type {
	width: 282px;
}

.games-list-filter-form-col--search {
	width: calc(100% - 614px);
}

.games-list-filter-form-col--category {
	width: 332px;
}



.games-list-filter-form-type-items-block {
	background: #172a3e;
	border-radius: 8px;
}

.games-list-filter-form-type-items {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
}

.games-list-filter-form-type-item-wrapper {
	padding-left: 8px;
	padding-right: 8px;
}

.games-list-filter-form-type-item-label {
	display: block;
	margin: 0;
}

.games-list-filter-form-type-item {
	display: block;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	color: #406084;
	background-color: #172a3e;
	border: 1px solid #172a3e;
	padding: 15px 15px;
	text-decoration: none;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.games-list-filter-form-type-item:hover {
	color: #fff;
	background-color: #14202d;
	border: 1px solid #1a314b;
}

.radio:checked~.games-list-filter-form-type-item {
	color: #fff;
	background-color: #14202d;
	border: 1px solid #1a314b;
	pointer-events: none;
}


.select-item {}

.select-item-text {}


.games-list-items-block {
	background: #14202d;
	border-radius: 8px;
	padding: 24px;
	margin-top: 20px;
}

.games-list-items {
	display: flex;
	flex-wrap: wrap;
	margin-left: -12px;
	margin-right: -12px;
	margin-top: -24px;
}

.games-list-item-wrapper {
	width: 220px;
	padding-left: 12px;
	padding-right: 12px;
	margin-top: 24px;
}

.games-list-item {
	text-decoration: none;
	color: #7998ba;
}

.games-list-item__image {
	border-radius: 8px;
}

.games-list-item__image .image {
	border-radius: 8px;
}

.games-list-item__online {
	margin-top: 16px;
	display: flex;
	flex-wrap: wrap;
	position: relative;
	padding-left: 16px;
}

.games-list-item__online::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	border-radius: 8px;
	width: 8px;
	height: 8px;
	box-shadow: 0 1px 4px 0 rgba(46, 229, 122, 0.25);
	background: #2ee57a;
}

.games-list-item__online__count {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
}

.games-list-item__online__title {
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
	padding-left: 4px;
}

.games-bet-block {
	margin-top: 12px;
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.games-bet-top-block {}

.games-bet-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.games-bet-top__left {}

.games-bet-top__right {}

.games-bet-tabs-block {
	border-radius: 8px;
	background: #172a3e;
}

.games-bet-tabs {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
}

.games-bet-tab-wrapper {
	padding-left: 8px;
	padding-right: 8px;
}

.games-bet-tab {
	padding: 13px 15px;
	display: flex;
	align-items: center;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.games-bet-tab__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
	transition: 0.4s ease;
	position: relative;
}

.games-bet-tab__icon {
	width: 24px;
	height: 24px;
	position: relative;
	margin-top: -2px;
	margin-bottom: -2px;
}

.games-bet-tab__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	transition: 0.4s ease;
}


.games-bet-tab__icon+.games-bet-tab__title {
	padding-left: 8px;
}


.games-bet-tab:hover,
.games-bet-tab.active {
	border-color: #1a314b;
	background: #14202d;
}

.games-bet-tab:hover .games-bet-tab__title,
.games-bet-tab.active .games-bet-tab__title {
	color: #ebf4ff;
}

.games-bet-tab:hover .games-bet-tab__icon::before,
.games-bet-tab.active .games-bet-tab__icon::before {
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}


.games-bet-tab--my {}

.games-bet-tab--all {}

.games-bet-tab--high {}

.games-bet-tab--race {}

.games-bet-tab--race .games-bet-tab__icon::before {
	mask-image: url('../images/svg/trophy.svg');
	-webkit-mask-image: url('../images/svg/trophy.svg');
}

.games-bet-tab--race.active .games-bet-tab__icon::before {
	mask-image: url('../images/svg/trophy-fill.svg');
	-webkit-mask-image: url('../images/svg/trophy-fill.svg');
}

.games-bet-tab--race .games-bet-tab__title {
	padding-right: 16px;
}

.games-bet-tab--race .games-bet-tab__title::before {
	content: '';
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	border-radius: 8px;
	width: 8px;
	height: 8px;
	box-shadow: 0 1px 4px 0 rgba(46, 229, 122, 0.25);
	background: #2ee57a;
}

.sort-count-form-block {}

.sort-count-form {}


.sort-count-form .bootstrap-select>.dropdown-toggle {
	font-size: 14px;
	height: 32px;
	padding: 6px;
	padding-left: 12px;
	padding-right: 40px;
	color: #e6e6e6;
}

.sort-count-form .bootstrap-select>.dropdown-toggle::after {
	right: 12px;
}

.games-bet-tabs-content-block {
	padding-top: 16px;
}

.games-bet-tabs-content {}

.games-bet-tab-content {
	display: none;
}

.games-bet-tab-content:first-child {
	display: block;
}

.games-bet-tab-content:nth-child(4) {
	/* display: block; */
}

.games-bet-table-block {}

.games-bet-table {}

.games-bet-t-heading {
	display: flex;
}

.games-bet-t-h-col {
	width: 16.66%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.games-bet-t-h-col--game {}

.games-bet-t-h-col--user {}

.games-bet-t-h-col--time {}

.games-bet-t-h-col--bet {
	text-align: right;
}

.games-bet-t-h-col--multiplier {
	text-align: center;
}

.games-bet-t-h-col--payout {
	text-align: right;
}

.games-bet-t-h-col-title-block {
	position: relative;
}

.games-bet-t-h-col-title {
	position: relative;
}


.games-bet-t-items {}

.games-bet-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.games-bet-t-item:nth-child(odd) {
	background: #0e1b28;
}


.games-bet-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 16.66%;
}

.games-bet-t-i-col__title-block {
	display: none;
	position: relative;
}

.games-bet-t-i-col__title {
	position: relative;
}

.games-bet-t-i-col__value-block {
	width: 100%;
}

.games-bet-t-i-col--game {}

.games-bet-t-i-col--game .games-bet-t-i-col__value-block {
	display: flex;
}

.games-bet-t-i-col--user {}

.games-bet-t-i-col--time {}

.games-bet-t-i-col--bet {}

.games-bet-t-i-col--multiplier {}

.games-bet-t-i-col--payout {}


.games-bet-t-i-game-block {}

.games-bet-t-i-game {
	display: flex;
	align-items: center;
	text-decoration: none;
	border: 0;
	padding: 0;
}

a.games-bet-t-i-game:hover .games-bet-t-i-game__title {
	color: #2ee57a;
}

.games-bet-t-i-game__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.games-bet-t-i-game__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #406084;
	transition: 0.4s ease;
}

.games-bet-t-i-game__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1;
	color: #ebf4ff;
	max-width: calc(100% - 16px);
	padding-left: 8px;
	transition: 0.4s ease;
}

.games-bet-t-i-game--dice {}

.games-bet-t-i-game--dice .games-bet-t-i-game__icon::before {
	mask-image: url('../images/svg/games/dice-five.svg');
	-webkit-mask-image: url('../images/svg/games/dice-five.svg');
}

.games-bet-t-i-game--roulette {}

.games-bet-t-i-game--roulette .games-bet-t-i-game__icon::before {
	mask-image: url('../images/svg/games/checkerboard.svg');
	-webkit-mask-image: url('../images/svg/games/checkerboard.svg');
}

.games-bet-t-i-game--limbo {}

.games-bet-t-i-game--limbo .games-bet-t-i-game__icon::before {
	mask-image: url('../images/svg/games/boules.svg');
	-webkit-mask-image: url('../images/svg/games/boules.svg');
}



.games-bet-t-i-user {
	display: flex;
	align-items: center;
}

.games-bet-t-i-user__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.games-bet-t-i-user__icon .image {
	border-radius: 16px;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.games-bet-t-i-user__username {
	font-weight: 500;
	color: #ebf4ff;
}

.games-bet-t-i-user__icon+.games-bet-t-i-user__username {
	padding-left: 8px;
	max-width: calc(100% - 16px);
}

.games-bet-t-i-user__username-hidden {
	position: relative;
	padding-left: 24px;
	display: none;
}

.games-bet-t-i-user__username-hidden::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.games-bet-t-i-user--hidden {}

.games-bet-t-i-user--hidden .games-bet-t-i-user__username-hidden {
	display: block;
}

.games-bet-t-i-user--hidden .games-bet-t-i-user__icon {
	display: none;
}

.games-bet-t-i-user--hidden .games-bet-t-i-user__username {
	display: none;
}

.games-bet-t-i-amount {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.games-bet-t-i-amount__amount {
	color: #7998ba;
	padding-right: 8px;
	width: calc(100% - 16px);
	text-align: right;
}

.games-bet-t-i-amount__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.games-bet-t-i-amount--green {
	color: #2ee57a;
}

.games-bet-t-i-amount--green .games-bet-t-i-amount__amount {
	color: #2ee57a;
}

.games-bet-t-i-multiplier {
	text-align: center;
}



.games-leader-table-block {}

.games-leader-table {}



.games-bet-table-block {}

.games-bet-table {}

.games-leader-t-heading {
	display: flex;
}

.games-leader-t-h-col {
	width: 25%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.games-leader-t-h-col--rank {}

.games-leader-t-h-col--user {}

.games-leader-t-h-col--wagered {
	text-align: right;
}

.games-leader-t-h-col--prize {
	text-align: right;
}


.games-leader-t-h-col-title-block {
	position: relative;
}

.games-leader-t-h-col-title {
	position: relative;
}


.games-leader-t-items {}

.games-leader-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.games-leader-t-item:nth-child(odd) {
	background: #0e1b28;
}


.games-leader-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 25%;
}

.games-leader-t-i-col__title-block {
	display: none;
	position: relative;
}

.games-leader-t-i-col__title {
	position: relative;
}

.games-leader-t-i-col__value-block {
	width: 100%;
}

.games-leader-t-i-col--game {}

.games-leader-t-i-col--user {}

.games-leader-t-i-col--time {}

.games-leader-t-i-col--bet {}

.games-leader-t-i-col--multiplier {}

.games-leader-t-i-col--payout {}


.games-leader-t-i-rank {
	display: flex;
	align-items: center;
}

.games-leader-t-i-rank__icon {
	width: 24px;
	height: 24px;
	position: relative;
	margin-top: -4px;
	margin-bottom: -4px;
}

.games-leader-t-i-rank__title {
	font-weight: 400;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.games-leader-t-i-user-block {}

.games-leader-t-i-user {
	display: flex;
	align-items: center;
}

.games-leader-t-i-user__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.games-leader-t-i-user__icon .image {
	border-radius: 16px;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.games-leader-t-i-user__username {
	font-weight: 500;
	color: #ebf4ff;
}

.games-leader-t-i-user__icon+.games-leader-t-i-user__username {
	padding-left: 8px;
	max-width: calc(100% - 16px);
}

.games-leader-t-i-user__username-hidden {
	position: relative;
	padding-left: 24px;
	display: none;
}

.games-leader-t-i-user__username-hidden::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.games-leader-t-i-user--hidden {}

.games-leader-t-i-user--hidden .games-leader-t-i-user__username-hidden {
	display: block;
}

.games-leader-t-i-user--hidden .games-leader-t-i-user__icon {
	display: none;
}

.games-leader-t-i-user--hidden .games-leader-t-i-user__username {
	display: none;
}

.games-leader-t-i-amount {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.games-leader-t-i-amount__amount {
	color: #7998ba;
	padding-right: 8px;
	width: calc(100% - 16px);
	text-align: right;
}

.games-leader-t-i-amount__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.games-leader-t-i-amount--green {
	color: #2ee57a;
}

.games-leader-t-i-amount--green .games-leader-t-i-amount__amount {
	color: #2ee57a;
}

.games-leader-t-i-multiplier {
	text-align: center;
}

.section-game {
	padding-bottom: 40px;
}

.game-section-block {}

.game-section {
	display: flex;
}

.game-section__left {
	width: 306px;
}

.game-section__right {
	width: calc(100% - 306px);
	padding-left: 12px;
}

.game-manage-panel-block {}

.game-manage-panel {
	border-radius: 8px;
	padding: 12px;
	background: #14202d;
	display: flex;
	flex-direction: column;
}

.game-manage-tabs-block {
	width: 100%;
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 4px;
}

.game-manage-tabs {
	display: flex;
	margin-left: -4px;
	margin-right: -4px;
	margin-top: -8px;
}

.game-manage-tab-wrapper {
	margin-top: 8px;
	width: 50%;
	padding-left: 4px;
	padding-right: 4px;
}

.game-manage-tab {
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	color: #406084;
	padding: 11px;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.game-manage-tab:hover,
.game-manage-tab.active {
	border-color: #1a314b;
	background: #1a314b;
	color: #ebf4ff;
}

.game-manage-tab.active {
	pointer-events: none;
}


.game-manage-tabs-content-block {
	width: 100%;
}

.game-manage-tabs-content {}

.game-manage-tab-content {
	display: none;
	padding-top: 12px;
}

.game-manage-tab-content:first-child {
	display: block;
}

/* .game-manage-tab-content:nth-child(2) {
	display: block;
} */

.game-manage-form {}


.game-manage-form .field-block {
	margin-top: 12px;
}

.game-manage-form .field-block:first-child {
	margin-top: 0;
}





.chip-value-slider-block {
	position: relative;
	margin-top: 6px;
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 3px 10px;
}

.chip-value-slider {
	position: relative;
	width: 240px;
	margin-right: auto;
	margin-left: auto;
}

.chip-value-slider .swiper-button-next,
.chip-value-slider .swiper-button-prev {
	position: absolute;
	width: 16px;
	height: 16px;
	top: 50%;
	transform: translateY(-50%);
}

.chip-value-slider .swiper-button-next::after,
.chip-value-slider .swiper-button-prev::after {
	content: '';
	position: absolute;
	width: 12px;
	height: 12px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 12px 12px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 12px 12px;
	background-color: #406084;
}

.chip-value-slider .swiper-button-next {
	right: -18px;
}

.chip-value-slider .swiper-button-prev {
	left: -18px;
}


.chip-value-slider .swiper-button-next::after {
	mask-image: url('../images/svg/caret-right-fill.svg');
	-webkit-mask-image: url('../images/svg/caret-right-fill.svg');
}

.chip-value-slider .swiper-button-prev::after {
	mask-image: url('../images/svg/caret-left-fill.svg');
	-webkit-mask-image: url('../images/svg/caret-left-fill.svg');
}

.chip-value-slide {
	width: 40px;
	height: 40px;
}

.chip-value-item {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40px;
	height: 40px;
	position: relative;
	text-decoration: none;
	margin-right: auto;
	margin-left: auto;
}

.chip-value-item::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	border-radius: 8px;
	box-shadow: 0 3px 10px -3px rgba(4, 121, 226, 0.27);
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	opacity: 0;
	visibility: hidden;
	transition: 0.4s ease;
}

.chip-value-item__icon {
	position: relative;
	z-index: 2;
}

.chip-value-item__text {
	position: absolute;
	z-index: 3;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	font-weight: 700;
	font-size: 6px;
	line-height: 1;
	text-align: center;
	color: #fff;
}

.chip-value-item.active::before {
	opacity: 1;
	visibility: visible;
}

.field-title-amount {
	font-weight: 500;
	font-size: 14px;
	line-height: 1;
	text-align: right;
	color: #406084;
}

.game-manage-form .field--amount .field-icon::before {
	display: none;
}

.field-icon .image {
	width: 100%;
}

.field-buttons-block {}

.field-buttons {
	display: flex;
	margin-left: -2px;
	margin-right: -2px;
}

.field-btn-wrapper {
	padding-left: 2px;
	padding-right: 2px;
}

.field-btn {
	font-weight: 500;
	font-size: 14px;
	line-height: 1;
	color: #406084;
	background-color: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 6px 9px;
	text-decoration: none;
}

.field-btn:hover {
	color: #ebf4ff;
	background-color: #1a314b;
	border-color: #1a314b;
}

.field-btn.active {
	color: #ebf4ff;
	background-color: #1a314b;
	border-color: #1a314b;
}



.field--game-manage--total-bet {}

.field--game-manage--total-bet input[type='text'],
.field--game-manage--total-bet input[type='email'],
.field--game-manage--total-bet input[type='password'] {
	padding-right: 100px;
}

.field-block--have-select {
	z-index: 20;
}

.field-block--have-select input[type='text'],
.field-block--have-select input[type='email'],
.field-block--have-select input[type='password'] {
	padding-right: 64px;
}


.field-title-right-icon {
	width: 20px;
	height: 20px;
}

.field--game-manage--number-bets {}


.field-left-panel-block {
	position: absolute;
	left: 14px;
	top: 50%;
	transform: translateY(-50%);
	z-index: 10;
}

.field-left-panel {
	display: flex;
	align-items: center;
}

.field--game-manage--on-win {}

.field--game-manage--on-win input[type='text'],
.field--game-manage--on-win input[type='email'],
.field--game-manage--on-win input[type='password'] {
	padding-left: 196px;
	padding-right: 36px;
	text-align: center;
}

.field--game-manage--on-loss {}

.field--game-manage--on-loss input[type='text'],
.field--game-manage--on-loss input[type='email'],
.field--game-manage--on-loss input[type='password'] {
	padding-left: 196px;
	padding-right: 36px;
	text-align: center;
}


.field-percent-icon-block {
	padding-left: 8px;
}

.field-percent-icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.field-percent-icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/percent.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/percent.svg');
	background-color: #406084;
	transition: 0.4s ease;
}



.field-roll-over-icon-block {
	padding-left: 8px;
}

.field-roll-over-icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.field-roll-over-icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/arrows-clockwise.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/arrows-clockwise.svg');
	background-color: #406084;
	transition: 0.4s ease;
}



.field-multiplier-icon-block {
	padding-left: 8px;
}

.field-multiplier-icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.field-multiplier-icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/arrow-fat-line-up.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/arrow-fat-line-up.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.field--game-manage--stop-profit {}

.field--have-icon--right {}

.field--have-icon--right input[type='text'],
.field--have-icon--right input[type='email'],
.field--have-icon--right input[type='password'] {
	padding-right: 44px;
	padding-left: 14px;
}

.field--have-icon--right .field-icon {
	right: 14px;
	left: auto;
}


.game-panel-block {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.game-panel {}

.game-panel__top {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
}

.game-panel__top-left {
	padding-top: 6px;
	padding-bottom: 6px;
}

.game-panel__top-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.game-title {
	color: #406084;
}

.game-panel-fairness-btn-block {
	display: flex;
}

.game-panel-fairness-btn {
	display: inline-block;
	text-decoration: none;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	color: #406084;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 12px 10px;
}

.game-panel-fairness-btn:hover {
	background-color: #1a314b;
	color: #ebf4ff;
}

.game-panel__middle {
	padding-top: 36px;
	padding-bottom: 36px;
}

.game-panel__bottom {}

.game-roulette-manage-buttons-block {}

.game-roulette-manage-buttons {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.game-panel-undo-btn-block {
	display: flex;
}

.game-panel-undo-btn {
	display: inline-block;
	text-decoration: none;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	color: #ebf4ff;
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 12px 10px;
	padding-left: 42px;
	position: relative;
}

.game-panel-undo-btn::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 10px;
	top: 50%;
	transform: translateY(-50%);
	mask-image: url('../images/svg/arrow-u-up-left.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/arrow-u-up-left.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.game-panel-undo-btn:hover {
	color: #ebf4ff;
	background: #1a314b;
}

.game-panel-clear-btn-block {
	display: flex;
}

.game-panel-clear-btn {
	display: inline-block;
	text-decoration: none;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	color: #ebf4ff;
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 12px 10px;
	padding-right: 42px;
	position: relative;
}

.game-panel-clear-btn::after {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	right: 10px;
	top: 50%;
	transform: translateY(-50%);
	mask-image: url('../images/svg/arrows-clockwise.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/arrows-clockwise.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.game-panel-clear-btn:hover {
	color: #ebf4ff;
	background: #1a314b;
}


.game-roulette-block {}

.game-roulette {}

.game-roulette__cylinder {
	width: 300px;
	height: 300px;
	margin-right: auto;
	margin-left: auto;
	position: relative;
}

.game-roulette__cylinder__stator {
	width: 362px;
	height: 362px;
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 1;
	transform: translate(-50%, -50%);
}

.game-roulette__cylinder__rotor {
	position: absolute;
	width: 100px;
	height: 100px;
	top: 50%;
	left: 50%;
	z-index: 2;
	transform: translate(-50%, -50%);
	animation: game-roulette__cylinder__rotor-anim 8s ease-in-out infinite;
}

@keyframes game-roulette__cylinder__rotor-anim {
	0% {
		transform: translate(-50%, -50%) rotate(0deg);
	}

	100% {
		transform: translate(-50%, -50%) rotate(720deg);
	}
}

.game-roulette-bet-block {
	margin-top: 40px;
}

.game-roulette-bet {
	margin-left: -2px;
	margin-right: -2px;
	display: flex;
	justify-content: center;
}

.game-roulette-bet-left {
	padding-left: 2px;
	padding-right: 2px;
	max-width: 48px;
}

.game-roulette-bet-center-left {
	padding-left: 2px;
	padding-right: 2px;
	max-width: 208px;
}

.game-roulette-bet-center {
	padding-left: 2px;
	padding-right: 2px;
	max-width: 208px;
}

.game-roulette-bet-center-right {
	padding-left: 2px;
	padding-right: 2px;
	max-width: 208px;
}

.game-roulette-bet-right {
	padding-left: 2px;
	padding-right: 2px;
	max-width: 48px;
}


.game-roulette-bet-buttons-block {}

.game-roulette-bet-buttons {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	margin-left: -2px;
	margin-right: -2px;
	margin-top: -4px;
}

.game-roulette-bet-btn-wrapper {
	padding-left: 2px;
	padding-right: 2px;
	margin-top: 4px;
}

.game-roulette-bet-btn {
	width: 48px;
	height: 48px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #172a3e;
	border: 2px solid #172a3e;
	border-radius: 8px;
	padding: 2px;

	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	text-align: center;
	color: #ebf4ff;
	text-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
	text-decoration: none;
}

.game-roulette-bet-btn:hover {
	color: #ebf4ff;
}

.game-roulette-bet-btn--red {
	background: #fc5757;
	border: 2px solid #fc5757;
}

.game-roulette-bet-btn--bd {
	background: transparent;
	border: 2px solid #1a314b;
}

.game-roulette-bet-btn--bd:hover {
	background: #1a314b;
	color: #ebf4ff;
}

.game-roulette-bet-btn--long {
	width: 204px;
}

.game-roulette-bet-btn--middle {
	width: 100px;
}

.game-roulette-bet-btn--zero {
	height: 152px;
	background: #2ee57a;
	border: 2px solid #2ee57a;
}

.game-description-dropdown-panel-block {
	margin-top: 12px;
}

.game-description-dropdown-panel {
	background: #14202d;
	border-radius: 8px
}

.game-description-dropdown-panel__heading {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 12px;
	cursor: pointer;
}

.game-description-dropdown-panel__heading-left {
	display: flex;
	align-items: center;
}

.game-description-dropdown-panel__title-block {
	padding-right: 10px;
}

.game-description-dropdown-panel__title {
	color: #ebf4ff;
}

.game-description-dropdown-panel__logo-block {}

.game-description-dropdown-panel__logo {}


.game-description-dropdown-panel__heading-right {}

.game-description-dropdown-panel__heading-arrow {
	width: 44px;
	height: 44px;
	position: relative;
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
}

.game-description-dropdown-panel__heading-arrow::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/caret-down.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/caret-down.svg');
	background-color: #406084;
	z-index: 5;
	transition: 0.4s ease;
	pointer-events: none;
	display: block;
}

.game-description-dropdown-panel.active .game-description-dropdown-panel__heading-arrow::before {
	transform: translate(-50%, -50%) rotate(180deg);
}

.game-description-dropdown-panel__body {
	padding: 12px;
	padding-top: 0;
	display: none;
}


.game-descr-tabs-block {
	display: flex;
}

.game-descr-tabs-wrapper {
	background: #172a3e;
	border-radius: 8px;
}

.game-descr-tabs {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
}

.game-descr-tab-wrapper {
	padding-left: 8px;
	padding-right: 8px;
}

.game-descr-tab {
	padding: 13px 15px;
	display: flex;
	align-items: center;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.game-descr-tab__icon {
	width: 24px;
	height: 24px;
	position: relative;
	margin-top: -2px;
	margin-bottom: -2px;
}

.game-descr-tab__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	transition: 0.4s ease;
}

.game-descr-tab__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
	transition: 0.4s ease;
	position: relative;
	padding-left: 8px;
}

.game-descr-tab:hover,
.game-descr-tab.active {
	border-color: #1a314b;
	background: #14202d;
}

.game-descr-tab:hover .game-descr-tab__title,
.game-descr-tab.active .game-descr-tab__title {
	color: #ebf4ff;
}

.game-descr-tab--description {}

.game-descr-tab--description .game-descr-tab__icon::before {
	mask-image: url('../images/svg/info.svg');
	-webkit-mask-image: url('../images/svg/info.svg');
}

.game-descr-tab--description.active .game-descr-tab__icon::before {
	mask-image: url('../images/svg/info-fill.svg');
	-webkit-mask-image: url('../images/svg/info-fill.svg');
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.game-descr-tab--big-wins {}

.game-descr-tab--big-wins .game-descr-tab__icon::before {
	mask-image: url('../images/svg/crown-simple.svg');
	-webkit-mask-image: url('../images/svg/crown-simple.svg');
}

.game-descr-tab--big-wins.active .game-descr-tab__icon::before {
	mask-image: url('../images/svg/crown-simple-fill.svg');
	-webkit-mask-image: url('../images/svg/crown-simple-fill.svg');
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.game-descr-tabs-content-block {
	padding-top: 16px;
}

.game-descr-tabs-content {}

.game-descr-tab-content {
	display: none;
}

.game-descr-tab-content:first-child {
	display: block;
}

.game-descr-tab-content:nth-child(2) {
	/* display: block; */
}



.games-wins-table-block {}

.games-wins-table {}

.games-wins-t-heading {
	display: flex;
}

.games-wins-t-h-col {
	width: 16.66%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.games-wins-t-h-col--rank {}

.games-wins-t-h-col--user {}

.games-wins-t-h-col--date {}

.games-wins-t-h-col--bet {
	text-align: right;
}

.games-wins-t-h-col--multiplier {
	text-align: center;
}

.games-wins-t-h-col--payout {
	text-align: right;
}

.games-wins-t-h-col-title-block {
	position: relative;
}

.games-wins-t-h-col-title {
	position: relative;
}


.games-wins-t-items {}

.games-wins-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.games-wins-t-item:nth-child(odd) {
	background: #0e1b28;
}


.games-wins-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 16.66%;
}

.games-wins-t-i-col__title-block {
	display: none;
	position: relative;
}

.games-wins-t-i-col__title {
	position: relative;
}

.games-wins-t-i-col__value-block {
	width: 100%;
}

.games-wins-t-i-col--rank {}

.games-wins-t-i-col--user {}

.games-wins-t-i-col--date {}

.games-wins-t-i-col--bet {}

.games-wins-t-i-col--multiplier {}

.games-wins-t-i-col--payout {}


.games-wins-t-i-rank {
	display: flex;
	align-items: center;
}

.games-wins-t-i-rank__icon {
	width: 24px;
	height: 24px;
	position: relative;
	margin-top: -2px;
	margin-bottom: -2px;
}

.games-wins-t-i-rank__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1;
	color: #ebf4ff;
}


.games-wins-t-i-user {
	display: flex;
	align-items: center;
}

.games-wins-t-i-user__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.games-wins-t-i-user__icon .image {
	border-radius: 16px;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.games-wins-t-i-user__username {
	font-weight: 500;
	color: #ebf4ff;
}

.games-wins-t-i-user__icon+.games-wins-t-i-user__username {
	padding-left: 8px;
	max-width: calc(100% - 16px);
}

.games-wins-t-i-user__username-hidden {
	position: relative;
	padding-left: 24px;
	display: none;
}

.games-wins-t-i-user__username-hidden::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.games-wins-t-i-user--hidden {}

.games-wins-t-i-user--hidden .games-wins-t-i-user__username-hidden {
	display: block;
}

.games-wins-t-i-user--hidden .games-wins-t-i-user__icon {
	display: none;
}

.games-wins-t-i-user--hidden .games-wins-t-i-user__username {
	display: none;
}

.games-wins-t-i-amount {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.games-wins-t-i-amount__amount {
	color: #7998ba;
	padding-right: 8px;
	width: calc(100% - 16px);
	text-align: right;
}

.games-wins-t-i-amount__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.games-wins-t-i-amount--green {
	color: #2ee57a;
}

.games-wins-t-i-amount--green .games-wins-t-i-amount__amount {
	color: #2ee57a;
}

.games-wins-t-i-multiplier {
	text-align: center;
}


.game-description-block {}

.game-description {
	display: flex;
}

.game-description__left {
	width: 220px;
	padding-right: 24px;
}

.game-description__image {
	border-radius: 8px;
}

.game-description__image .image {
	border-radius: 8px;
}

.game-description__right {
	width: calc(100% - 220px);
	padding-right: 24px;
}

.game-description-tags-block {}

.game-description-tags {
	margin-top: -8px;
	margin-left: -4px;
	margin-right: -4px;
	display: flex;
	flex-wrap: wrap;
}

.game-description-tags-wrapper {
	margin-top: 8px;
	padding-left: 4px;
	padding-right: 4px;
}

.game-description-tag {
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 24px;
	padding: 9px 10px;
	font-weight: 500;
	font-size: 14px;
	line-height: 1;
	color: #406084;
}

.game-description-tag--edge {
	color: #ebf4ff;
}

.text--green {
	color: #2ee57a;
}

.game-description-tag--edge .text--green {
	font-weight: 600;
}


.game-description__content {
	padding-top: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.game-description__content b {
	font-weight: 600;
}

.game-description__content a {
	font-weight: 600;
	color: #0681d5;
	text-decoration: none;
}

.game-description__content a:hover {
	text-decoration: underline;
	color: #0681d5;
}


.game-description__content h2 {
	font-weight: 500;
	font-size: 22px;
	line-height: 1.45;
	color: #ebf4ff;
	margin-bottom: 1rem;
}

.game-description__content h3 {
	font-weight: 500;
	font-size: 18px;
	line-height: 1.45;
	color: #ebf4ff;
	margin-bottom: 1rem;
}

.game-description__content blockquote {
	background: #172a3e;
	border-left: 2px solid #047bdf;
	padding: 8px 16px;
	padding-left: 14px;
}

.game-description__content ul,
.game-description__content ol {
	padding-left: 20px;
	margin-bottom: 1rem;
}

.game-description__content ul:last-child,
.game-description__content ol:last-child {
	margin-bottom: 0;
}

.tdu {
	text-decoration: underline;
}

.game-roulette-info-table-block {
	margin-top: 12px;
}

.game-roulette-info-table {}

.game-roulette-info-t-item {
	padding: 12px;
	color: #ebf4ff;
	font-weight: 500;
	font-size: 14px;
	line-height: 1.45;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.game-roulette-info-t-item:nth-child(odd) {
	background: #172a3e;
}

.game-roulette-info-t-item__title {
	padding-right: 12px;
}

.game-roulette-info-t-item__value {}




.modal-title-icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.modal-title-icon .image {
	width: 100%;
	height: 100%;
	border-radius: 24px;
	object-fit: contain;
}


.modal-title-icon+.modal-title {
	max-width: calc(100% - 24px);
	padding-left: 8px;
}

.m-fairness-tabs-block {
	margin-top: 24px;
	border-radius: 8px;
	background: #172a3e;
}

.m-fairness-tabs {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
}

.m-fairness-tab-wrapper {
	padding-left: 8px;
	padding-right: 8px;
	width: 50%;
}

.m-fairness-tab {
	padding: 13px 15px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.m-fairness-tab__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
	transition: 0.4s ease;
	position: relative;
}

.m-fairness-tab:hover,
.m-fairness-tab.active {
	border-color: #1a314b;
	background: #14202d;
}

.m-fairness-tab:hover .m-fairness-tab__title,
.m-fairness-tab.active .m-fairness-tab__title {
	color: #ebf4ff;
}

.modal--fairness .modal-content {
	padding-left: 48px;
	padding-right: 48px;
}


.modal--fairness .modal-title-icon {
	width: 32px;
	height: 32px;
}

.modal--fairness .modal-title-icon+.modal-title {
	max-width: calc(100% - 32px);
}

.m-fairness-tabs-content-block {}

.m-fairness-tabs-content {
	padding-top: 16px;
}

.m-fairness-tab-content {
	display: none;
}

.m-fairness-tab-content:first-child {
	display: block;
}

/*.m-fairness-tab-content:nth-child(2) {
 display: block; 
}*/

.m-fairness-seeds-form-block {
	margin-top: 8px;
}

.m-fairness-seeds-form {}

.m-fairness-seeds-form .field-block {
	margin-top: 8px;
}

.m-fairness-seeds-form .field-block:first-child {
	margin-top: 0;
}

.copy-field-btn-block {
	position: relative;
	padding-left: 8px;
}

.copy-field-btn {
	position: relative;
	display: block;
	width: 20px;
	height: 20px;
}



.copy-field-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/copy.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/copy.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #406084;
	transition: 0.4s ease;
}


.copy-field-btn::after {
	pointer-events: none;
	content: attr(aria-label);
	position: absolute;
	text-transform: uppercase;
	z-index: 1;
	font-weight: 700;
	font-size: 11px;
	line-height: 1;
	right: 0;
	white-space: nowrap;
	text-transform: uppercase;
	/* background-color: #14202d;
	color: #20B16C; */
	background-color: #17C964;
	color: #ebf4ff;
	padding: 4px;
	top: -30px;
	border-radius: 4px;
	opacity: 0;
	visibility: hidden;
	transition: 0.4s ease;
}

.copy-field-btn.active::after {
	opacity: 1;
	visibility: visible;
}

.m-fairness-seeds-form-fieldset {
	margin-top: 8px;
}

.m-fairness-seeds-form-fieldset-title {
	color: #ebf4ff;
}

.field-change-btn-block {
	margin-right: -14px;
}

.field-change-btn {
	box-shadow: 0 3px 10px -3px rgba(4, 121, 226, 0.27);
	font-size: 16px;
	padding: 15px 24px;
	border-radius: 0 8px 8px 0;
}



.m-fairness-verify-form-progress-block {
	margin-top: 8px;
}

.m-fairness-verify-form-progress {
	border: 1px dashed #1a314b;
	border-radius: 8px;
	padding: 40px 40px;
}

.m-fairness-verify-form-progress__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	text-align: center;
	color: #406084;
}

.m-fairness-verify-form-progress__progress {
	margin-top: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.m-fairness-verify-form-progress__progress__dot {
	width: 8px;
	height: 8px;
	position: relative;
	margin-left: 3px;
	margin-right: 3px;
}

.m-fairness-verify-form-progress__progress__dot::before {
	content: '';
	width: 8px;
	height: 8px;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	border-radius: 20px;
	background-color: #384BF2;
	animation: m-fairness-verify-form-progress__progress__dot-before-anim 1s linear infinite;
}

.m-fairness-verify-form-progress__progress__dot:nth-child(2)::before {
	animation: m-fairness-verify-form-progress__progress__dot-before-anim 1s linear infinite 0.5s;
}

@keyframes m-fairness-verify-form-progress__progress__dot-before-anim {
	0% {
		width: 8px;
		height: 8px;
	}

	50% {
		width: 12px;
		height: 12px;
	}

	100% {
		width: 8px;
		height: 8px;
	}
}

.m-fairness-verify-form-block {
	margin-top: 24px;
}

.m-fairness-verify-form {}

.m-fairness-verify-form .field-block {
	margin-top: 8px;
}

.m-fairness-verify-form .field-block:first-child {
	margin-top: 0;
}

.field-right-count-buttons-block {
	margin-right: -14px;
}

.field-right-count-buttons {
	display: flex;
}

.field-right-count-btn-wrapper {}

.field-right-count-btn-wrapper:last-child {
	padding-left: 1px;
}

.field-right-count-btn {
	display: block;
	padding: 12px 24px;
	width: 48px;
	height: 48px;
	box-shadow: 0 3px 10px -3px rgba(4, 121, 226, 0.27);
	background: #384bf2;
	border: 0;
	position: relative;
}

.field-right-count-btn--count-down {
	border-radius: 8px 0 0 8px;
}

.field-right-count-btn--count-down::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/caret-down.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/caret-down.svg');
	background-color: #EBF4FF;
}

.field-right-count-btn--count-up {
	border-radius: 0 8px 8px 0;
}

.field-right-count-btn--count-up::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/caret-up.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/caret-up.svg');
	background-color: #EBF4FF;
}

.m-fairness-verify-form .form-button-block {
	margin-top: 24px;
	display: flex;
	justify-content: center;
}

.m-fairness-verify-form-send-btn {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	text-align: center;
	color: #ebf4ff;
	background-color: transparent;
	border: 0;
	padding: 0;
}


.modal--user {}

.modal--user .modal-content {
	padding: 32px;
}

.modal--user .modal-title-icon {}

.m-user-info-block {}

.m-user-info {
	border: 1px solid #1a314b;
	border-radius: 8px;
}

.m-user-info-items {}

.m-user-info-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 14px 12px;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.m-user-info-item:nth-child(odd) {
	background: #0e1b28;
}


.m-user-info-item:first-child {
	border-radius: 8px 8px 0 0;
}

.m-user-info-item:last-child {
	border-radius: 0 0 8px 8px;
}

.m-user-info-item__left {}

.m-user-info-item__right {}

.m-user-info-item__title {
	font-weight: 600;
}

.m-user-info-item__date {}


.m-user-info-item-game-block {}

.m-user-info-item-game {
	display: flex;
	align-items: center;
}

.m-user-info-item-game__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.m-user-info-item-game__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #406084;
	transition: 0.4s ease;
}

.m-user-info-item-game__content {
	width: calc(100% - 16px);
	padding-left: 8px;
	display: flex;
	align-items: center;
	font-weight: 500;
}

.m-user-info-item-game__title {
	padding-right: 4px;
	color: #ebf4ff;
}

.m-user-info-item-game__table {
	color: #7998ba;
}



.m-user-info-item-game--dice {}

.m-user-info-item-game--dice .m-user-info-item-game__icon::before {
	mask-image: url('../images/svg/games/dice-five.svg');
	-webkit-mask-image: url('../images/svg/games/dice-five.svg');
}

.m-user-info-item-game--roulette {}

.m-user-info-item-game--roulette .m-user-info-item-game__icon::before {
	mask-image: url('../images/svg/games/checkerboard.svg');
	-webkit-mask-image: url('../images/svg/games/checkerboard.svg');
}

.m-user-info-item-game--limbo {}

.m-user-info-item-game--limbo .m-user-info-item-game__icon::before {
	mask-image: url('../images/svg/games/boules.svg');
	-webkit-mask-image: url('../images/svg/games/boules.svg');
}



.m-user-info-item-amount {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.m-user-info-item-amount__amount {
	color: #7998ba;
	padding-right: 8px;
	width: calc(100% - 16px);
	text-align: right;
}

.m-user-info-item-amount__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.modal--user--hidden .modal-title-icon {}

.modal--user--hidden .modal-title-icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}



.modal--bet .modal-dialog {
	max-width: 710px;
}

.modal--bet .modal-content {
	padding: 48px;
}

.field-block--bet-id {}

.field-block--bet-id .field-title {
	font-weight: 500;
	font-size: 18px;
	line-height: 1.25;
	color: #ebf4ff;
}

.field-title-user-block {}

.field-title-user {
	display: flex;
	align-items: center;
}

.field-title-user__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.field-title-user__icon .image {
	border-radius: 16px;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.field-title-user__username {
	font-weight: 500;
	color: #ebf4ff;
}

.field-title-user__icon+.field-title-user__username {
	padding-left: 8px;
	max-width: calc(100% - 16px);
}

.field-title-user__username-hidden {
	position: relative;
	padding-left: 24px;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
	display: none;
}

.field-title-user__username-hidden::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.field-title-user--hidden {}

.field-title-user--hidden .field-title-user__username-hidden {
	display: block;
}

.field-title-user--hidden .field-title-user__icon {
	display: none;
}

.field-title-user--hidden .field-title-user__username {
	display: none;
}



.copy-field-alt-btn-block {
	position: relative;
	padding-left: 8px;
}

.copy-field-alt-btn {
	position: relative;
	display: block;
	width: 20px;
	height: 20px;
}



.copy-field-alt-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/link.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/link.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #406084;
	transition: 0.4s ease;
}


.copy-field-alt-btn::after {
	pointer-events: none;
	content: attr(aria-label);
	position: absolute;
	text-transform: uppercase;
	z-index: 1;
	font-weight: 700;
	font-size: 11px;
	line-height: 1;
	right: 0;
	white-space: nowrap;
	text-transform: uppercase;
	/* background-color: #14202d;
	color: #20B16C; */
	background-color: #17C964;
	color: #ebf4ff;
	padding: 4px;
	top: -30px;
	border-radius: 4px;
	opacity: 0;
	visibility: hidden;
	transition: 0.4s ease;
}

.copy-field-alt-btn.active::after {
	opacity: 1;
	visibility: visible;
}



.m-bet-info-block {
	margin-top: 24px;
}

.m-bet-info {
	border: 1px solid #1a314b;
	border-radius: 8px;
}

.m-bet-info-items {}

.m-bet-info-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 14px 12px;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.m-bet-info-item:nth-child(odd) {
	background: #0e1b28;
}


.m-bet-info-item:first-child {
	border-radius: 8px 8px 0 0;
}

.m-bet-info-item:last-child {
	border-radius: 0 0 8px 8px;
}

.m-bet-info-item__left {}

.m-bet-info-item__right {}

.m-bet-info-item__title {
	font-weight: 500;
	color: #ebf4ff;
}

.m-bet-info-item__ratio {}

.m-bet-info-item__date {}




.m-bet-info-item-amount {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.m-bet-info-item-amount__amount {
	color: #7998ba;
	padding-right: 8px;
	width: calc(100% - 16px);
	text-align: right;
}

.m-bet-info-item-amount__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.m-bet-info-item-amount--green {}

.m-bet-info-item-amount--green .m-bet-info-item-amount__amount {
	color: #2ee57a;
}

.m-bet-play-btn-block {
	margin-top: 28px;
}

.m-bet-play-btn {
	width: 100%;
}

.m-bet-honesty-panel-block {
	margin-top: 24px;
}

.m-bet-honesty-panel {}

.m-bet-honesty-panel-heading {
	padding-top: 8px;
	padding-bottom: 8px;
	position: relative;
	padding-right: 48px;
	cursor: pointer;
}

.m-bet-honesty-panel-heading__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
}

.m-bet-honesty-panel-heading__arrow {
	width: 32px;
	height: 32px;
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;

}

.m-bet-honesty-panel-heading__arrow::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/caret-down.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/caret-down.svg');
	background-color: #406084;
	z-index: 5;
	transition: 0.4s ease;
	pointer-events: none;
	display: block;
}

.m-bet-honesty-panel.active .m-bet-honesty-panel-heading__arrow::before {
	transform: translate(-50%, -50%) rotate(180deg);
}


.m-bet-honesty-panel-body {
	padding-top: 8px;
	display: none;
}

.m-bet-honesty-panel-form-row {}

.m-bet-honesty-panel-form-row {}

.m-bet-honesty-panel-form-row .row,
.m-bet-honesty-panel-form-row .row>* {
	--bs-gutter-x: 16px;
}

.m-bet-honesty-panel-form-col {
	padding-top: 8px;
}

.m-bet-honesty-bottom-link-block {
	margin-top: 24px;
}

.m-bet-honesty-bottom-link {
	position: relative;
	padding-right: 24px;
	font-weight: 600;
	font-size: 14px;
	line-height: 1.45;
	color: #0170ef;
	text-decoration: none;
}

.m-bet-honesty-bottom-link::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-image: url('../images/svg/arrow-circle-up-right.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/arrow-circle-up-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #0170EF;
	transition: 0.4s ease;
}


.m-bet-honesty-bottom-link:hover {
	color: #0170ef;
	text-decoration: underline;
}


.field--have-icon--right-two input[type='text'],
.field--have-icon--right-two input[type='email'],
.field--have-icon--right-two input[type='password'] {
	padding-right: 72px;
}


.field--new-client-seed input[type='text'],
.field--new-client-seed input[type='email'],
.field--new-client-seed input[type='password'] {
	padding-right: 120px;
}

.field--nonce input[type='text'],
.field--nonce input[type='email'],
.field--nonce input[type='password'] {
	padding-right: 108px;
}

.game-panel--dice {}

.game-panel--dice .game-panel__middle {
	padding-top: 220px;
	padding-bottom: 220px;
}

.game-dice-block {
	margin-top: 44px;
}

.game-dice {}

.game-dice-slider-block {
	margin-right: auto;
	margin-left: auto;
	max-width: 612px;
	padding: 15px;
	background: #172a3e;
	border-radius: 100px;
	border: 1px solid #1a314b;
	position: relative;
}

.game-dice-slider-wrapper {
	border-radius: 100px;
	padding: 12px;
	background: #14202d;
	position: relative;
}

.game-dice-slider-wrapper .irs--flat {
	height: 12px;
}

.game-dice-slider-wrapper .irs--flat .irs-line {
	background: #2ee57a;
	border-radius: 12px;
	height: 12px;
	top: 0;
}

.game-dice-slider-wrapper .irs--flat .irs-bar {
	height: 12px;
	top: 0;
	background-color: #fc5757;
}

.game-dice-slider-wrapper .irs--flat .irs-bar--single {
	border-radius: 8px 0 0 8px;
}

.game-dice-slider-wrapper .irs--flat .irs-handle {
	width: 48px;
	height: 48px;
	box-shadow: 0 3px 10px -3px rgba(4, 121, 226, 0.27);
	background: #384bf2;
	border-radius: 8px;
	top: -18px;
	cursor: pointer;
}

.game-dice-slider-wrapper .irs--flat .irs-handle::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%) rotate(90deg);
	mask-image: url('../images/svg/list.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/list.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #1A2DD4;
}

.game-dice-slider-wrapper .irs--flat .irs-grid {
	height: 12px;
}

.game-dice-slider-wrapper .irs--flat .irs-grid-text {
	top: -68px;
	bottom: auto;
	font-weight: 600;
	font-size: 16px;
	line-height: 1.25;
	text-align: center;
	color: #ebf4ff;
	text-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}

.game-dice-slider-wrapper .irs--flat .irs-grid-text::before {
	content: '';
	position: absolute;
	width: 12px;
	height: 12px;
	left: 50%;
	top: 30px;
	transform: translateX(-50%);
	background-image: url('../images/svg/triange-circled.svg');
	background-position: center;
	background-repeat: no-repeat;
	background-size: 12px 12px;
}

.game-dice-slider-wrapper .irs--flat .irs-min,
.game-dice-slider-wrapper .irs--flat .irs-max {
	display: none;
}

.game-dice-slider-wrapper .irs--flat .irs-from,
.game-dice-slider-wrapper .irs--flat .irs-to,
.game-dice-slider-wrapper .irs--flat .irs-single {
	display: none;
}


.game-dice-slider-wrapper .irs--flat .irs-from::before,
.game-dice-slider-wrapper .irs--flat .irs-to::before,
.game-dice-slider-wrapper .irs--flat .irs-single::before {
	display: none;
}

.game-dice-slider-wrapper .irs--flat .irs-grid-pol {
	display: none;
}

.game-dice-slider-wrapper .irs--flat .irs-handle i {
	display: none;
}



.game-dice-slider {}


.game-dice-fields-block {
	background: #0e1a27;
	border-radius: 8px;
	padding: 16px;
}

.game-dice-fields {}

.game-dice-fields-row {
	margin-left: -6px;
	margin-right: -6px;
	display: flex;
	margin-top: -12px;
}

.game-dice-fields-col {
	margin-top: 12px;
	width: 33.33%;
	padding-left: 6px;
	padding-right: 6px;
}

.ttu {
	text-transform: uppercase;
}


.game-dice-info-table-block {
	margin-top: 1rem;
	margin-bottom: 1rem;
}

.game-dice-info-table {}

.game-dice-info-t-heading {
	display: flex;
}

.game-dice-info-t-h-col {
	width: 33.33%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.game-dice-info-t-h-col {
	text-align: center;
	padding: 14px 12px;
}

.game-dice-info-t-h-col:first-child {
	text-align: left;
}

.game-dice-info-t-h-col:last-child {
	text-align: right;
}


.game-dice-info-t-item {
	color: #7998ba;
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	border-radius: 8px;
	display: flex;
	align-items: center;
}

.game-dice-info-t-item:nth-child(odd) {
	background: #172a3e;
}

.game-dice-info-t-item-col {
	width: 33.33%;
	padding: 14px 12px;
}

.game-dice-info-t-item-col--rank {
	text-align: left;
}

.game-dice-info-t-item-col--roll-over {
	text-align: center;
}

.game-dice-info-t-item-col--win-chance {
	text-align: right;
}

.game-dice-info-t-item__title {
	padding-right: 12px;
}

.game-dice-info-t-item__value {}


.m-dice-bet-game-block {
	padding-top: 24px;
}


.m-dice-bet-game-block .game-dice-slider-block {
	padding: 8px;
}

.m-dice-bet-game-block .game-dice-slider-wrapper {
	padding: 6px;
}

.m-dice-bet-game-block .game-dice-slider-wrapper .irs--flat .irs-grid-text {
	font-size: 8px;
	top: -36px;
}

.m-dice-bet-game-block .game-dice-slider-wrapper .irs--flat {
	height: 6px;
}

.m-dice-bet-game-block .game-dice-slider-wrapper .irs--flat .irs-line {
	height: 6px;
}

.m-dice-bet-game-block .game-dice-slider-wrapper .irs--flat .irs-bar--single {
	height: 6px;
}

.m-dice-bet-game-block .game-dice-slider-wrapper .irs--flat .irs-grid {
	height: 6px;
}

.m-dice-bet-game-block .game-dice-slider-wrapper .irs--flat .irs-handle {
	height: 24px;
	width: 24px;
	padding: 6px;
	border-radius: 4px;
	box-shadow: 0 2px 5px -2px rgba(4, 121, 226, 0.27);
	top: -9px;
}

.m-dice-bet-game-block .game-dice-slider-wrapper .irs--flat .irs-handle::before {
	mask-size: 14px 14px;
	-webkit-mask-size: 14px 14px;
	width: 14px;
	height: 14px;
}

.m-dice-bet-game-block .game-dice-block {
	margin-top: 24px;
}

.m-dice-bet-game-block .game-dice-slider-wrapper .irs--flat .irs-grid-text::before {
	top: 14px;
}

.m-dice-bet-game-block .game-dice-fields-block {
	margin-top: 24px;
}

.m-dice-bet-game-block .game-dice-bet {
	width: 30px;
	height: 33px;
	background-size: 30px 33px;
	bottom: 0;
}

.m-dice-bet-game-block .game-dice-bet__count {
	font-size: 10px;
}


.game-panel--limbo {}

.game-panel--limbo .game-panel__middle {
	padding-top: 200px;
	padding-bottom: 200px;
}


.game-limbo-block {}

.game-limbo {
	padding: 10px;
}

.game-limbo-ratio {
	font-weight: 600;
	font-size: 100px;
	line-height: 1.25;
	text-align: center;
	color: #ebf4ff;
}

.game-limbo-fields-block {
	background: #0e1a27;
	border-radius: 8px;
	padding: 16px;
}

.game-limbo-fields {}

.game-limbo-fields-row {
	margin-left: -6px;
	margin-right: -6px;
	display: flex;
	flex-wrap: wrap;
	margin-top: -12px;
}

.game-limbo-fields-col {
	margin-top: 12px;
	width: 50%;
	padding-left: 6px;
	padding-right: 6px;
}




.m-limbo-bet-game-block {
	margin-top: 24px;
}

.m-limbo-bet-game-params-block {}

.m-limbo-bet-game-params {
	display: flex;
	margin-left: -6px;
	margin-right: -6px;
}

.m-limbo-bet-game-param-wrapper {
	width: 50%;
	padding-left: 6px;
	padding-right: 6px;
}

.m-limbo-bet-game-param {
	border-radius: 8px;
	padding: 8px 12px;
	background: #0e1b28;
}

.m-limbo-bet-game-param__value {
	font-weight: 400;
	font-size: 20px;
	line-height: 1.25;
	color: #ebf4ff;
	text-align: center;
}

.m-limbo-bet-game-param__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
	text-align: center;
}

.m-limbo-bet-game-param--red .m-limbo-bet-game-param__value {
	color: #FF060A;
}

.m-limbo-bet-game-param--green .m-limbo-bet-game-param__value {
	color: #2ee57a;
}

.db-page-block {
	padding-top: 12px;
	padding-bottom: 12px;
}

.db-page {
	display: flex;
}

.db-page-left {
	width: 86px;
	transition: 0.4s width ease;
}

.db-side-block {
	height: 100%;
	background: #14202d;
	border-radius: 8px;
	padding: 16px 4px;
	transition: 0.4s padding ease;
}

.db-side-logo-block {
	display: flex;
	align-items: center;
	justify-content: center;
	padding-left: 12px;
	padding-right: 12px;
}

.db-side-logo {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
}

.db-side-logo__image {
	width: 44px;
}

.db-side-logo__text-block {
	width: calc(100% - 44px);
	padding-left: 0;
	position: relative;
	overflow: hidden;
	width: 0;
	transition: 0.4s width ease;
}

.db-side-logo__text {
	padding-left: 12px;
}

.db-side-logo__text .image {
	width: 61px;
	min-width: 61px;
	max-width: none;
}

.db-side-toggle-panel-btn-block {
	margin-top: 24px;
}

.db-side-toggle-panel-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8px 4px;
	border: 0;
	width: 100%;
}

.db-side-toggle-panel-btn__text-block {
	position: relative;
	overflow: hidden;
	width: 0;
	transition: 0.4s width ease;
}

.db-side-toggle-panel-btn__text {
	padding-left: 8px;
	padding-right: 8px;
	font-weight: 500;
	font-size: 12px;
	line-height: 1.33;
	color: #406084;
	text-align: left;
	white-space: nowrap;
}

.db-side-toggle-panel-btn__icon {
	width: 32px;
	height: 32px;
	position: relative;
}

.db-side-toggle-panel-btn__icon::before {
	content: '';
	width: 32px;
	height: 32px;
	position: absolute;
	content: '';
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 32px 32px;
	mask-image: url('../images/svg/list.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 32px 32px;
	-webkit-mask-image: url('../images/svg/list.svg');
	background-color: #406084;
}

.db-side-toggle-panel-btn.active .db-side-toggle-panel-btn__icon::before {
	mask-image: url('../images/svg/close.svg');
	-webkit-mask-image: url('../images/svg/close.svg');
}

.db-sidemenu-block {
	margin-top: 24px;
}

.db-sidemenu {}

.db-sidemenu>ul {
	list-style-type: none;
	padding: 0;
	margin: 0;
}

.db-sidemenu-item {
	margin-top: 24px;
}

.db-sidemenu-item:first-child {
	margin-top: 0;
}



.db-sidemenu-item--dashboard {}

.db-sidemenu-item--transactions {}

.db-sidemenu-item--deposits {}

.db-sidemenu-item--refill {}

.db-sidemenu-item--withdrawal {}

.db-sidemenu-item--affiliate {}

.db-sidemenu-item--settings {}



.db-sidemenu-link {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 10px 12px;
	text-decoration: none;
	position: relative;
}

.db-sidemenu-link::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	border-radius: 8px;
	box-shadow: 0 3px 10px -3px rgba(4, 121, 226, 0.27);
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	opacity: 0;
	visibility: hidden;
	pointer-events: none;
	transition: 0.4s ease;
}

.db-sidemenu-link__icon {
	width: 28px;
	height: 28px;
	position: relative;
	z-index: 2;
}

.db-sidemenu-link__icon::before {
	content: '';
	position: absolute;
	width: 28px;
	height: 28px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 28px 28px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 28px 28px;
	background-color: #EBF4FF;
}

.db-sidemenu-link__text-block {
	width: 0;
	position: relative;
	overflow: hidden;
	z-index: 2;
	transition: 0.4s width ease;
}

.db-sidemenu-link__text {
	padding-left: 8px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #ebf4ff;
	z-index: 2;
	white-space: nowrap;
}




.db-sidemenu-link--dashboard {}

.db-sidemenu-link--dashboard .db-sidemenu-link__icon::before {
	mask-image: url('../images/svg/house-simple.svg');
	-webkit-mask-image: url('../images/svg/house-simple.svg');
}

.db-sidemenu-link--transactions {}

.db-sidemenu-link--transactions .db-sidemenu-link__icon::before {
	mask-image: url('../images/svg/calendar-dots.svg');
	-webkit-mask-image: url('../images/svg/calendar-dots.svg');
}

.db-sidemenu-link--deposits {}

.db-sidemenu-link--deposits .db-sidemenu-link__icon::before {
	mask-image: url('../images/svg/tip-jar.svg');
	-webkit-mask-image: url('../images/svg/tip-jar.svg');
}

.db-sidemenu-link--refill {}

.db-sidemenu-link--refill .db-sidemenu-link__icon::before {
	mask-image: url('../images/svg/plus-circle.svg');
	-webkit-mask-image: url('../images/svg/plus-circle.svg');
}

.db-sidemenu-link--withdrawal {}

.db-sidemenu-link--withdrawal .db-sidemenu-link__icon::before {
	mask-image: url('../images/svg/rocket-launch.svg');
	-webkit-mask-image: url('../images/svg/rocket-launch.svg');
}

.db-sidemenu-link--affiliate {}

.db-sidemenu-link--affiliate .db-sidemenu-link__icon::before {
	mask-image: url('../images/svg/link.svg');
	-webkit-mask-image: url('../images/svg/link.svg');
}

.db-sidemenu-link--settings {}

.db-sidemenu-link--settings .db-sidemenu-link__icon::before {
	mask-image: url('../images/svg/gear-six.svg');
	-webkit-mask-image: url('../images/svg/gear-six.svg');
}

.db-sidemenu-link:hover {}



.db-sidemenu-link.active::before {
	opacity: 1;
	visibility: visible;
}

.db-sidemenu-link:hover::before {
	opacity: 1;
	visibility: visible;
}

.db-sidemenu-item.active .db-sidemenu-link::before {
	opacity: 1;
	visibility: visible;
}



.db-page-right {
	width: calc(100% - 86px);
	padding-left: 12px;
	transition: 0.4s width ease;
}

.db-page-topline-block {
	position: relative;
	z-index: 20;
}

.db-page-topline {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.db-page-topline__left {}

.db-page-topline__right {}


.db-page-topline-panel-block {}

.db-page-topline-panel {
	display: flex;
	align-items: center;
}

.db-page-topline-panel__left {
	display: flex;
	align-items: center;
}

.db-page-topline-panel__right {
	display: flex;
	align-items: center;
}

.db-page-topline-panel__right__logo {
	display: none;
}

.db-page-topline-panel__right__logo .logo-wrapper {
	max-width: 97px;
}

.db-page-topline-panel__right__content {
	display: flex;
	align-items: center;
}

.db-page--wide-menu {}

.db-page--wide-menu .db-page-left {
	width: 196px;
}

.db-page--wide-menu .db-page-right {
	width: calc(100% - 196px);
}

.db-page--wide-menu .db-side-block {
	padding-left: 8px;
	padding-right: 8px;
}


.db-page--wide-menu .db-side-logo__text-block {
	width: calc(100% - 44px);
}

.db-page--wide-menu .db-side-toggle-panel-btn__text-block {
	width: calc(100% - 32px);
	transition: 0.4s width ease;
}

.db-page--wide-menu .db-sidemenu-link__text-block {
	width: calc(100% - 28px);
}



.topline-refill-btn-block {
	padding-left: 8px;
	display: flex;
}

.topline-refill-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	line-height: 1;
	color: #EBF4FF;
	font-size: 16px;
	border: 0;
	background-color: #17c964;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	text-align: center;
	padding: 8px 12px;
	text-decoration: none;
	border-radius: 8px;
}

.topline-refill-btn__text {
	padding-left: 8px;
}

.topline-refill-btn__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.topline-refill-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/plus-circle.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/plus-circle.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.topline-refill-btn:hover {
	color: #EBF4FF;
}

.db-page-content-block {
	padding-top: 12px;
}

.db-page-content {}

.dashboard-block {}

.dashboard {
	display: flex;
}

.dashboard-left {
	width: 316px;
}

.dashboard-right {
	width: calc(100% - 316px);
	padding-left: 12px;
}

.dashboard-info-block {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
	height: 100%;
}

.dashboard-info {}

.dashboard-info h1 {
	color: #ebf4ff;
}

.dashboard-info-descr {
	margin-top: 16px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.dashboard-info-stats-block {
	margin-top: 16px;
}

.dashboard-info-stats-title {
	font-weight: 600;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.dashboard-info-stat-fields-block {
	margin-top: 12px;
}

.dashboard-info-stat-fields {
	display: flex;
	flex-wrap: wrap;
	margin-top: -12px;
	margin-left: -6px;
	margin-right: -6px;
}

.dashboard-info-stat-field-wrapper {
	margin-top: 12px;
	width: 100%;
	padding-left: 6px;
	padding-right: 6px;
}

.dashboard-info-stat-field {
	border-radius: 8px;
	padding: 8px 8px 8px 16px;
	background: #0e1b28;
	display: flex;
	flex-direction: row-reverse;
}

.dashboard-info-stat-field__icon {
	width: 40px;
	height: 40px;
	background: #172a3e;
	border-radius: 40px;
	position: relative;
}

.dashboard-info-stat-field__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.dashboard-info-stat-field__content {
	width: 100%;
}

.dashboard-info-stat-field__title {
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.dashboard-info-stat-field__value {
	font-weight: 500;
	font-size: 20px;
	line-height: 1.25;
	color: #ebf4ff;
}

.dashboard-info-stat-field__icon+.dashboard-info-stat-field__content {
	width: calc(100% - 40px);
	padding-right: 8px;
}


.dashboard-info-stat-field-wrapper--total {}

.dashboard-info-stat-field--total {}

.dashboard-info-stat-field--total .dashboard-info-stat-field__icon::before {
	mask-image: url('../images/svg/tip-jar-fill.svg');
	-webkit-mask-image: url('../images/svg/tip-jar-fill.svg');
}

.dashboard-info-stat-field--total .dashboard-info-stat-field__value {
	color: #17c964;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.dashboard-info-stat-field-wrapper--stacking {}

.dashboard-info-stat-field--stacking {}

.dashboard-info-stat-field--stacking .dashboard-info-stat-field__icon::before {
	mask-image: url('../images/svg/trophy-fill.svg');
	-webkit-mask-image: url('../images/svg/trophy-fill.svg');
}

.dashboard-info-stat-field-wrapper--lottery {
	width: 50%;
}

.dashboard-info-stat-field--lottery {}

.dashboard-info-stat-field-wrapper--casino {
	width: 50%;
}

.dashboard-info-stat-field--casino {}

.dashboard-info-affiliate-block {
	margin-top: 16px;
}

.dashboard-info-affiliate-title {
	font-weight: 600;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.dashboard-info-panel {}

.dashboard-info-panel__top {}

.dashboard-info-panel__bottom {}


.dashboard-info-affiliate-copy-link-btn-block {
	margin-top: 12px;
}

.dashboard-info-affiliate-copy-link-btn {
	width: 100%;
}

.dashboard-info-affiliate-copy-link-btn .iconed-btn__icon::before {
	mask-image: url('../images/svg/link.svg');
	-webkit-mask-image: url('../images/svg/link.svg');
}


.dashboard-info-affiliate-link-info-block {
	margin-top: 12px;
}

.dashboard-info-affiliate-link-info {
	border: 1px solid #1a314b;
	background-color: #172a3e;
	border-radius: 8px;
	padding: 11px;
	display: flex;
}

.dashboard-info-affiliate-link-info-share-btn-block {
	width: 20px;
	height: 20px;
	position: relative;
}

.dashboard-info-affiliate-link-info-share-btn {
	width: 20px;
	height: 20px;
	position: relative;
	border: 0;
	padding: 0;
}

.dashboard-info-affiliate-link-info-share-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/share-network.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/share-network.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #047BDF;
}

.dashboard-info-affiliate-link-info-content {
	width: calc(100% - 20px);
	padding-left: 10px;
}

.dashboard-info-affiliate-link-info-link-block {}

.dashboard-info-affiliate-link-info-link {
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
	position: relative;
	overflow-wrap: break-word;
}

.dashboard-info-affiliate-link-info-descr {
	margin-top: 10px;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}


.dashboard-tabs-block {
	border-radius: 8px;
	background: #172a3e;
}

.dashboard-tabs {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
}

.dashboard-tab-wrapper {
	padding-left: 8px;
	padding-right: 8px;
}

.dashboard-tab {
	padding: 13px 15px;
	display: flex;
	align-items: center;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.dashboard-tab__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
	transition: 0.4s ease;
	position: relative;
}

.dashboard-tab__icon {
	width: 24px;
	height: 24px;
	position: relative;
	margin-top: -2px;
	margin-bottom: -2px;
}

.dashboard-tab__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	transition: 0.4s ease;
}


.dashboard-tab__icon+.dashboard-tab__title {
	padding-left: 8px;
}


.dashboard-tab:hover,
.dashboard-tab.active {
	border-color: #1a314b;
	background: #14202d;
}

.dashboard-tab.active {
	pointer-events: none;
}

.dashboard-tab:hover .dashboard-tab__title,
.dashboard-tab.active .dashboard-tab__title {
	color: #ebf4ff;
}

.dashboard-tab:hover .dashboard-tab__icon::before,
.dashboard-tab.active .dashboard-tab__icon::before {
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}


.dashboard-tab--balance {}

.dashboard-tab--balance .dashboard-tab__icon::before {
	mask-image: url('../images/svg/wallet.svg');
	-webkit-mask-image: url('../images/svg/wallet.svg');
}

.dashboard-tab--balance.active .dashboard-tab__icon::before {
	mask-image: url('../images/svg/wallet-fill.svg');
	-webkit-mask-image: url('../images/svg/wallet-fill.svg');
}

.dashboard-tab--lotteries {}

.dashboard-tab--lotteries .dashboard-tab__icon::before {
	mask-image: url('../images/svg/ticket.svg');
	-webkit-mask-image: url('../images/svg/ticket.svg');
}

.dashboard-tab--lotteries.active .dashboard-tab__icon::before {
	mask-image: url('../images/svg/ticket-fill.svg');
	-webkit-mask-image: url('../images/svg/ticket-fill.svg');
}

.dashboard-tab--games {}

.dashboard-tab--games .dashboard-tab__icon::before {
	mask-image: url('../images/svg/dice-five.svg');
	-webkit-mask-image: url('../images/svg/dice-five.svg');
}

.dashboard-tab--games.active .dashboard-tab__icon::before {
	mask-image: url('../images/svg/dice-five-fill.svg');
	-webkit-mask-image: url('../images/svg/dice-five-fill.svg');
}

.dashboard-tab--deposits {}

.dashboard-tab--deposits .dashboard-tab__icon::before {
	mask-image: url('../images/svg/tip-jar.svg');
	-webkit-mask-image: url('../images/svg/tip-jar.svg');
}

.dashboard-tab--deposits.active .dashboard-tab__icon::before {
	mask-image: url('../images/svg/tip-jar-fill.svg');
	-webkit-mask-image: url('../images/svg/tip-jar-fill.svg');
}

.dashboard-tabs-content-block {
	padding-top: 12px;
}

.dashboard-tabs-content {}

.dashboard-tab-content {
	display: none;
}

.dashboard-tab-content:first-child {
	display: block;
}

/* .dashboard-tab-content:nth-child(3) {
	display: block;
} */

.db-balance-block {
	background-color: #14202d;
	border-radius: 8px;
}

.db-balance-top-block {}

.db-balance-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 12px;
}

.db-balance-top-left {
	padding-right: 8px;
}

.db-balance-top-content {}

.db-balance-top-title {
	color: #ebf4ff;
}

.db-balance-top-descr {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.db-balance-top-right {}


.db-balance-table-block {
	padding-left: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
}

.db-balance-table {}

.db-balance-t-heading {
	display: flex;
}

.db-balance-t-h-col {
	width: calc(33% - 38px);
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-balance-t-h-col--payments {}

.db-balance-t-h-col--currency {}

.db-balance-t-h-col--balance {}

.db-balance-t-h-col--actions {
	text-align: right;
	width: 114px;
}

.db-balance-t-items {}

.db-balance-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-balance-t-item:nth-child(odd) {
	background: #0e1b28;
}

.db-balance-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: calc(33% - 38px);
}

.db-balance-t-i-col--payments {}

.db-balance-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-balance-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-balance-t-i-col__value-block {
	width: 100%;
}

.db-balance-t-i-col--currency {}

.db-balance-t-i-col--balance {}

.db-balance-t-i-col--actions {
	width: 114px;
}


.db-balance-t-i-payment {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.db-balance-t-i-payment__title {

	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-balance-t-i-payment__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-balance-t-i-currency {}

.db-balance-t-i-balance {
	display: flex;
	align-items: center;
}

.db-balance-t-i-balance__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-balance-t-i-balance__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/wallet-fill.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/wallet-fill.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-balance-t-i-balance__balance {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-balance-t-i-balance--green {}

.db-balance-t-i-balance--green .db-balance-t-i-balance__icon::before {
	background-color: #17C964;
}

.db-balance-t-i-actions-block {}

.db-balance-t-i-actions {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-left: -4px;
	margin-right: -4px;
}

.db-balance-t-i-action-wrapper {
	padding-left: 4px;
	padding-right: 4px;
}

.db-balance-t-i-action-top-up-btn {
	width: 20px;
	height: 20px;
	position: relative;
	display: block;
	border: 0;
	padding: 0;
}

.db-balance-t-i-action-top-up-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/plus-circle.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/plus-circle.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-balance-t-i-action-top-up-btn:hover::before {
	background-color: #ebf4ff;
}

.db-balance-t-i-action-withdraw-btn {
	width: 20px;
	height: 20px;
	display: block;
	position: relative;
	border: 0;
	padding: 0;
}

.db-balance-t-i-action-withdraw-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/arrow-circle-up-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/arrow-circle-up-right.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-balance-t-i-action-withdraw-btn:hover::before {
	background-color: #ebf4ff;
}




.db-lottery-block {
	background-color: #14202d;
	border-radius: 8px;
}

.db-lottery-top-block {}

.db-lottery-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 12px;
}

.db-lottery-top-left {
	padding-right: 8px;
}

.db-lottery-top-content {}

.db-lottery-top-title {
	color: #ebf4ff;
}

.db-lottery-top-descr {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.db-lottery-top-right {}

.db-lottery-table-block {
	padding-left: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
}

.db-lottery-table {}

.db-lottery-t-heading {
	display: flex;
}

.db-lottery-t-h-col {
	width: 20%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-lottery-t-h-col--status {}

.db-lottery-t-h-col--time {}

.db-lottery-t-h-col--price-ticket {
	text-align: right;
}

.db-lottery-t-h-col--prize-fund {
	text-align: right;
}

.db-lottery-t-h-col--lottery {
	text-align: right;
}

.db-lottery-t-items {}

.db-lottery-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-lottery-t-item:nth-child(odd) {
	background: #0e1b28;
}

.db-lottery-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 20%;
}


.db-lottery-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-lottery-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-lottery-t-i-col__value-block {
	width: 100%;
}

.db-lottery-t-i-col--status {}

.db-lottery-t-i-col--time {}

.db-lottery-t-i-col--price-ticket {}

.db-lottery-t-i-col--prize-fund {}

.db-lottery-t-i-col--lottery {}

.db-lottery-t-i-amount-block {}

.db-lottery-t-i-amount {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.db-lottery-t-i-amount__amount {
	font-weight: 400;
	line-height: 1.25;
	color: #7998ba;
	padding-right: 8px;
	text-align: right;
	width: calc(100% - 16px);
}

.db-lottery-t-i-amount__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-lottery-t-i-time {}

.db-lottery-t-i-status-block {}

.db-lottery-t-i-status {
	display: flex;
	align-items: center;
}

.db-lottery-t-i-status__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-lottery-t-i-status__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-lottery-t-i-status__title {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-lottery-t-i-status--pending {}

.db-lottery-t-i-status--pending .db-lottery-t-i-status__icon::before {
	mask-image: url('../images/svg/hourglass.svg');
	-webkit-mask-image: url('../images/svg/hourglass.svg');
	background-color: #E5AA35;
}

.db-lottery-t-i-status--pending .db-lottery-t-i-status__title {
	color: #e5aa35;
}

.db-lottery-t-i-status--finished {}

.db-lottery-t-i-status--finished .db-lottery-t-i-status__icon::before {
	mask-image: url('../images/svg/check-circle.svg');
	-webkit-mask-image: url('../images/svg/check-circle.svg');
	background-color: #2EE57A;
}

.db-lottery-t-i-status--finished .db-lottery-t-i-status__title {
	color: #2EE57A;
}


.db-lottery-t-i-status--progress {}

.db-lottery-t-i-status--progress .db-lottery-t-i-status__icon::before {
	mask-image: url('../images/svg/circles-three.svg');
	-webkit-mask-image: url('../images/svg/circles-three.svg');
	background-color: #0170EF;
}

.db-lottery-t-i-status--progress .db-lottery-t-i-status__title {
	color: #0170EF;
}



.db-lottery-t-i-countdown-block {
	display: flex;
	justify-content: flex-end;
}

.db-lottery-t-i-countdown {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.db-lottery-t-i-countdown__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-lottery-t-i-countdown__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/timer.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/timer.svg');
	background-color: #F7931A;
	transition: 0.4s ease;
}

.db-lottery-t-i-countdown__countdown {
	width: calc(100% - 16px);
	padding-left: 8px;
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
}


.db-lottery-t-i-user-block {
	display: flex;
	justify-content: flex-end;
}

.db-lottery-t-i-user {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.db-lottery-t-i-user__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-lottery-t-i-user__name {
	width: calc(100% - 16px);
	padding-right: 8px;
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}


.db-lottery-t-i-tickets-block {}

.db-lottery-t-i-tickets {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-left: -4px;
	margin-right: -4px;
}

.db-lottery-t-i-ticket-wrapper {
	padding-left: 4px;
	padding-right: 4px;
}

.db-lottery-t-i-ticket {
	display: flex;
	align-items: center;
}

.db-lottery-t-i-ticket__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-lottery-t-i-ticket__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/ticket-fill.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/ticket-fill.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-lottery-t-i-ticket__count {
	padding-left: 8px;
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
	width: calc(100% - 16px);
}

.db-lottery-t-i-ticket--green {}

.db-lottery-t-i-ticket--green .db-lottery-t-i-ticket__icon::before {
	background-color: #17C964;
}


.db-games-block {
	background-color: #14202d;
	border-radius: 8px;
}

.db-games-top-block {}

.db-games-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 12px;
}

.db-games-top-left {
	padding-right: 8px;
}

.db-games-top-content {}

.db-games-top-title {
	color: #ebf4ff;
}

.db-games-top-descr {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.db-games-top-right {}

.db-games-table-block {
	padding-left: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
}



.db-games-table {}

.db-games-t-heading {
	display: flex;
}

.db-games-t-h-col {
	width: 20%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-games-t-h-col--game {}


.db-games-t-h-col--time {}

.db-games-t-h-col--bet {
	text-align: right;
}

.db-games-t-h-col--multiplier {
	text-align: center;
}

.db-games-t-h-col--payout {
	text-align: right;
}

.db-games-t-h-col-title-block {
	position: relative;
}

.db-games-t-h-col-title {
	position: relative;
}


.db-games-t-items {}

.db-games-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-games-t-item:nth-child(odd) {
	background: #0e1b28;
}


.db-games-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 20%;
}

.db-games-t-i-col__title-block {
	display: none;
	position: relative;
}

.db-games-t-i-col__title {
	position: relative;
}

.db-games-t-i-col__value-block {
	width: 100%;
}

.db-games-t-i-col--game {}

.db-games-t-i-col--game .db-games-t-i-col__value-block {
	display: flex;
}

.db-games-t-i-col--user {}

.db-games-t-i-col--time {}

.db-games-t-i-col--bet {}

.db-games-t-i-col--multiplier {}

.db-games-t-i-col--payout {}


.db-games-t-i-game-block {}

.db-games-t-i-game {
	display: flex;
	align-items: center;
	text-decoration: none;
	border: 0;
	padding: 0;
}

a.db-games-t-i-game:hover .db-games-t-i-game__title {
	color: #2ee57a;
}

.db-games-t-i-game__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-games-t-i-game__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #406084;
	transition: 0.4s ease;
}

.db-games-t-i-game__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1;
	color: #ebf4ff;
	max-width: calc(100% - 16px);
	padding-left: 8px;
	transition: 0.4s ease;
}

.db-games-t-i-game--dice {}

.db-games-t-i-game--dice .db-games-t-i-game__icon::before {
	mask-image: url('../images/svg/games/dice-five.svg');
	-webkit-mask-image: url('../images/svg/games/dice-five.svg');
}

.db-games-t-i-game--roulette {}

.db-games-t-i-game--roulette .db-games-t-i-game__icon::before {
	mask-image: url('../images/svg/games/checkerboard.svg');
	-webkit-mask-image: url('../images/svg/games/checkerboard.svg');
}

.db-games-t-i-game--limbo {}

.db-games-t-i-game--limbo .db-games-t-i-game__icon::before {
	mask-image: url('../images/svg/games/boules.svg');
	-webkit-mask-image: url('../images/svg/games/boules.svg');
}



.db-games-t-i-user {
	display: flex;
	align-items: center;
}

.db-games-t-i-user__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-games-t-i-user__icon .image {
	border-radius: 16px;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.db-games-t-i-user__username {
	font-weight: 500;
	color: #ebf4ff;
}

.db-games-t-i-user__icon+.db-games-t-i-user__username {
	padding-left: 8px;
	max-width: calc(100% - 16px);
}

.db-games-t-i-user__username-hidden {
	position: relative;
	padding-left: 24px;
	display: none;
}

.db-games-t-i-user__username-hidden::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-games-t-i-user--hidden {}

.db-games-t-i-user--hidden .db-games-t-i-user__username-hidden {
	display: block;
}

.db-games-t-i-user--hidden .db-games-t-i-user__icon {
	display: none;
}

.db-games-t-i-user--hidden .db-games-t-i-user__username {
	display: none;
}

.db-games-t-i-amount {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.db-games-t-i-amount__amount {
	color: #7998ba;
	padding-right: 8px;
	width: calc(100% - 16px);
	text-align: right;
}

.db-games-t-i-amount__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-games-t-i-amount--green {
	color: #2ee57a;
}

.db-games-t-i-amount--green .db-games-t-i-amount__amount {
	color: #2ee57a;
}

.db-games-t-i-multiplier {
	text-align: center;
}



.db-deposits-block {
	background-color: #14202d;
	border-radius: 8px;
}

.db-deposits-top-block {}

.db-deposits-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 12px;
}

.db-deposits-top-left {
	padding-right: 8px;
}

.db-deposits-top-content {}

.db-deposits-top-title {
	color: #ebf4ff;
}

.db-deposits-top-descr {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.db-deposits-top-right {}

.db-deposits-table-block {
	padding-left: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
}


.db-deposits-table {}

.db-deposits-t-heading {
	display: flex;
}

.db-deposits-t-h-col {
	width: 20%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-deposits-t-h-col--plan {
	width: 23.5%;
}

.db-deposits-t-h-col--status {
	width: 17%;
}

.db-deposits-t-h-col--total-profit {
	width: 23%;
}

.db-deposits-t-h-col--start-date {
	width: 25.25%;
}

.db-deposits-t-h-col--details {
	width: 11.25%;
	text-align: right;
}

.db-deposits-t-items {}

.db-deposits-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	background: #0e1b28;
	position: relative;
	padding-bottom: 4px;
	margin-top: 12px;
}

.db-deposits-t-item:first-child {
	/* margin-top: 0; */
}

.db-deposits-t-item:nth-child(odd) {}

.db-deposits-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 20%;
}


.db-deposits-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-deposits-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-deposits-t-i-col__value-block {
	width: 100%;
}


.db-deposits-t-i-col--plan {
	width: 23.5%;
}

.db-deposits-t-i-col--status {
	width: 17%;
}

.db-deposits-t-i-col--total-profit {
	width: 23%;
}

.db-deposits-t-i-col--start-date {
	width: 27.25%;
}

.db-deposits-t-i-col--details {
	width: 9.25%;
}


.db-deposits-t-i-date {}

.db-deposits-t-i-plan-block {}

.db-deposits-t-i-plan {
	display: flex;
	align-items: center;
}

.db-deposits-t-i-plan__icon {
	width: 22px;
	height: 24px;
}

.db-deposits-t-i-plan__title {
	width: calc(100% - 22px);
	padding-left: 8px;
	font-weight: 500;
	font-size: 20px;
	line-height: 1;
	color: #ebf4ff;
}

.db-deposits-t-i-status-block {}

.db-deposits-t-i-status {
	display: flex;
	align-items: center;
}

.db-deposits-t-i-status__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-deposits-t-i-status__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-deposits-t-i-status__title {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-deposits-t-i-status--active {}

.db-deposits-t-i-status--active .db-deposits-t-i-status__icon::before {
	mask-image: url('../images/svg/hourglass.svg');
	-webkit-mask-image: url('../images/svg/hourglass.svg');
	background-color: #E5AA35;
}

.db-deposits-t-i-status--active .db-deposits-t-i-status__title {
	color: #e5aa35;
}

.db-deposits-t-i-status--completed {}

.db-deposits-t-i-status--completed .db-deposits-t-i-status__icon::before {
	mask-image: url('../images/svg/check-circle.svg');
	-webkit-mask-image: url('../images/svg/check-circle.svg');
	background-color: #2EE57A;
}

.db-deposits-t-i-status--completed .db-deposits-t-i-status__title {
	color: #2EE57A;
}



.db-deposits-t-i-profit-block {}

.db-deposits-t-i-profit {
	display: flex;
	align-items: center;
}

.db-deposits-t-i-profit__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-deposits-t-i-profit__content {
	width: calc(100% - 16px);
	padding-left: 8px;
	font-size: 14px;
	line-height: 1.25;
	font-weight: 400;
	color: #7998ba;
}

.db-deposits-t-i-profit__amount {
	font-weight: 500;
	color: #2ee57a;
}

.db-deposits-t-i-details-btn-block {
	display: flex;
	justify-content: flex-end;
}

.db-deposits-t-i-details-btn {
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	position: relative;
	border: 0;
}

.db-deposits-t-i-details-btn::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/eye.svg');
	background-color: #6B88A8;
	transition: 0.4s ease;
}


.db-deposits-t-i-progress {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 4px;
	border-radius: 8px;
	background-color: #172a3e;
}

.db-deposits-t-i-progress__progress {
	position: absolute;
	left: 0;
	width: 0;
	height: 100%;
	border-radius: 8px;
	background-color: #0650a4;
}

.db-deposits-t-i-progress--complete {}

.db-deposits-t-i-progress--complete .db-deposits-t-i-progress__progress {
	background-color: #17c964;
}



.modal--info {}

.modal--info .modal-dialog {
	max-width: 440px;
}

.modal--info .modal-content {
	padding: 24px;
	border: 0;
}

.modal--info .modal-header {
	padding-right: 0;
	position: static;
}

.modal--info .modal-body-content {
	padding-top: 0;
	padding-right: 44px;
}

.modal--info .modal-close {
	top: 12px;
	right: 18px;
}

.modal--info .modal-close::before {
	background-color: #fff;
}


.modal--info--success .modal-content {
	background-color: #2F9259;
}

.modal--info--fail .modal-content {
	background-color: #d64150;
}

.modal--info--warning .modal-content {
	background-color: #ef8d32;
}


.modal-info-block {}

.modal-info {
	display: flex;
	align-items: center;
}

.modal-info__icon-block {
	width: 78px;
	height: 78px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-position: center;
	background-repeat: no-repeat;
	background-size: 78px 78px;
	padding-bottom: 10px;
}

.modal-info__icon {
	width: 32px;
	height: 32px;
}

.modal-info__content {
	width: calc(100% - 78px);
	padding-left: 24px;
}

.modal-info__title {
	font-weight: 500;
	font-size: 24px;
	line-height: 1.25;
	color: #ebf4ff;
}

.modal-info__descr {
	margin-top: 1px;
	font-size: 16px;
	line-height: 1.25;
	color: #ebf4ff;
}



.modal-info--success {}

.modal-info--success .modal-info__icon-block {
	background-image: url('../images/svg/dashboard/modal-info-icon-block-bg--success.svg');
}

.modal-info--fail {}

.modal-info--fail .modal-info__icon-block {
	background-image: url('../images/svg/dashboard/modal-info-icon-block-bg--fail.svg');
}

.modal-info--warning {}

.modal-info--warning .modal-info__icon-block {
	background-image: url('../images/svg/dashboard/modal-info-icon-block-bg--warning.svg');
}


.copy-btn {
	position: relative;
}

.copy-btn::after {
	pointer-events: none;
	content: attr(aria-label);
	position: absolute;
	text-transform: uppercase;
	z-index: 1;
	font-weight: 700;
	font-size: 11px;
	line-height: 1;
	right: 12px;
	white-space: nowrap;
	text-transform: uppercase;
	/* background-color: #14202d;
	color: #20B16C; */
	background-color: #17C964;
	color: #ebf4ff;
	padding: 4px;
	top: -20px;
	border-radius: 4px;
	opacity: 0;
	visibility: hidden;
	transition: 0.4s ease;
}



.copy-btn.active::after {
	opacity: 1;
	visibility: visible;
}




.mobile-db-menu-block {
	padding-top: 4px;
}

.mobile-db-menu {
	font-size: 16px;
	line-height: 1;
	font-weight: 500;
	vertical-align: middle;
	margin-top: 8px;
}

.mobile-db-menu:first-child {
	margin-top: 0;
}

.mobile-db-menu>ul {
	padding: 0;
	margin: 0;
	list-style-type: none;
	vertical-align: middle;
}

.mobile-db-menu-item {
	display: flex;
	margin-top: 8px;
}

.mobile-db-menu-item:first-child {
	margin-top: 0;
}

.mobile-db-menu-link {
	width: 100%;
	color: #EBF4FF;
	display: flex;
	align-items: center;
	text-decoration: none;
	padding: 10px 12px;
	position: relative;
	border-radius: 8px;
}

.mobile-db-menu-link::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	border-radius: 8px;
	box-shadow: 0 3px 10px -3px rgba(4, 121, 226, 0.27);
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	opacity: 0;
	visibility: hidden;
	pointer-events: none;
	transition: 0.4s ease;
}

.mobile-db-menu-link:hover {
	color: #fff;
}

.mobile-db-menu-link:hover::before {
	opacity: 1;
	visibility: visible;
	pointer-events: auto;
}

.mobile-db-menu-link__icon {
	width: 28px;
	height: 28px;
	position: relative;
	z-index: 2;
}

.mobile-db-menu-link__icon {
	width: 28px;
	height: 28px;
	position: relative;
	z-index: 2;
}

.mobile-db-menu-link__icon::before {
	content: '';
	position: absolute;
	width: 28px;
	height: 28px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 28px 28px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 28px 28px;
	background-color: #EBF4FF;
}



.mobile-db-menu-link__icon+.mobile-db-menu-link__text {
	padding-left: 8px;
	width: calc(100% - 28px);
}

.mobile-db-menu-link__text {
	position: relative;
	z-index: 2;
}

.mobile-db-menu-link--dashboard {}

.mobile-db-menu-link--dashboard .mobile-db-menu-link__icon::before {
	mask-image: url('../images/svg/house-simple.svg');
	-webkit-mask-image: url('../images/svg/house-simple.svg');
}

.mobile-db-menu-link--transactions {}

.mobile-db-menu-link--transactions .mobile-db-menu-link__icon::before {
	mask-image: url('../images/svg/calendar-dots.svg');
	-webkit-mask-image: url('../images/svg/calendar-dots.svg');
}

.mobile-db-menu-link--deposits {}

.mobile-db-menu-link--deposits .mobile-db-menu-link__icon::before {
	mask-image: url('../images/svg/tip-jar.svg');
	-webkit-mask-image: url('../images/svg/tip-jar.svg');
}

.mobile-db-menu-link--refill {}

.mobile-db-menu-link--refill .mobile-db-menu-link__icon::before {
	mask-image: url('../images/svg/plus-circle.svg');
	-webkit-mask-image: url('../images/svg/plus-circle.svg');
}

.mobile-db-menu-link--withdrawal {}

.mobile-db-menu-link--withdrawal .mobile-db-menu-link__icon::before {
	mask-image: url('../images/svg/rocket-launch.svg');
	-webkit-mask-image: url('../images/svg/rocket-launch.svg');
}

.mobile-db-menu-link--affiliate {}

.mobile-db-menu-link--affiliate .mobile-db-menu-link__icon::before {
	mask-image: url('../images/svg/link.svg');
	-webkit-mask-image: url('../images/svg/link.svg');
}

.mobile-db-menu-link--settings {}

.mobile-db-menu-link--settings .mobile-db-menu-link__icon::before {
	mask-image: url('../images/svg/gear-six.svg');
	-webkit-mask-image: url('../images/svg/gear-six.svg');
}

.mobile-db-menu-link--settings {}

.mobile-db-menu-link--logout .mobile-db-menu-link__icon::before {
	mask-image: url('../images/svg/sign-out.svg');
	-webkit-mask-image: url('../images/svg/sign-out.svg');
}

.mobile-db-menu-link:hover {}


.mobile-db-menu-item.active .mobile-db-menu-link::before {
	opacity: 1;
	visibility: visible;
}

.mobile-db-menu-link.active::before {
	opacity: 1;
	visibility: visible;
}


.new-deposit-block {}

.new-deposit {
	display: flex;
}

.new-deposit-left {
	width: 316px;
}

.new-deposit-right {
	width: calc(100% - 316px);
	padding-left: 12px;
}





.new-deposit-info-block {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
	height: 100%;
}

.new-deposit-info {}

.new-deposit-info h1 {
	color: #ebf4ff;
}

.new-deposit-info-descr {
	margin-top: 16px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.new-deposit-info-stats-block {
	margin-top: 16px;
}

.new-deposit-info-stats-title {
	font-weight: 600;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.new-deposit-info-stat-items-block {
	margin-top: 16px;
	max-width: 250px;
}

.new-deposit-info-stat-items {
	display: flex;
	flex-wrap: wrap;
	margin-top: -12px;
	margin-left: -6px;
	margin-right: -6px;
}

.new-deposit-info-stat-item-wrapper {
	margin-top: 12px;
	width: 100%;
	padding-left: 6px;
	padding-right: 6px;
}

.new-deposit-info-stat-item {
	display: flex;
}

.new-deposit-info-stat-item__icon-block {
	padding: 8px;
	width: 40px;
	height: 40px;
	border-radius: 40px;
	background: #172a3e;
	position: relative;
}

.new-deposit-info-stat-item__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.new-deposit-info-stat-item__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.new-deposit-info-stat-item__text {
	width: calc(100% - 40px);
	max-width: 220px;
	padding-left: 16px;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.new-deposit-info-stat-item__text b {
	font-weight: 600;
	color: #0170ef;
}



.new-deposit-info-stat-item--percent {}

.new-deposit-info-stat-item--percent .new-deposit-info-stat-item__icon::before {
	mask-image: url('../images/svg/percent.svg');
	-webkit-mask-image: url('../images/svg/percent.svg');
}

.new-deposit-info-stat-item--deposit {}

.new-deposit-info-stat-item--deposit .new-deposit-info-stat-item__icon::before {
	mask-image: url('../images/svg/tip-jar-fill.svg');
	-webkit-mask-image: url('../images/svg/tip-jar-fill.svg');
}

.new-deposit-info-stat-item--deposit {}

.new-deposit-info-stat-item--deposit .new-deposit-info-stat-item__icon::before {
	mask-image: url('../images/svg/tip-jar-fill.svg');
	-webkit-mask-image: url('../images/svg/tip-jar-fill.svg');
}

.new-deposit-info-stat-item--calendar {}

.new-deposit-info-stat-item--calendar .new-deposit-info-stat-item__icon::before {
	mask-image: url('../images/svg/calendar-check-fill.svg');
	-webkit-mask-image: url('../images/svg/calendar-check-fill.svg');
}

.new-deposit-step-list-block {
	border-radius: 8px;
	background: #172a3e;
	padding: 2px;
}

.new-deposit-step-list {
	margin-left: -2px;
	margin-right: -2px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.new-deposit-step-list-item-wrapper {
	padding-left: 2px;
	padding-right: 2px;
}

.new-deposit-step-list-item {
	padding: 9px 15px;
	display: flex;
	align-items: center;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.new-deposit-step-list-item__count-block {
	width: 24px;
	height: 24px;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.new-deposit-step-list-item__count-block::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-image: url('../images/svg/check-circle.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/check-circle.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #17C964;
	transition: 0.4s ease;
	opacity: 0;
	visibility: hidden;
}

.new-deposit-step-list-item__count {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
}

.new-deposit-step-list-item__title {
	max-width: calc(100% - 24px);
	padding-left: 8px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
}

.new-deposit-step-list-item-sep {
	width: 50px;
	height: 1px;
	background-color: #3E6795;
}

.new-deposit-step-list-item:hover {
	border-color: #1a314b;
	background: #14202d;
}

.new-deposit-step-list-item.active {
	border-color: #1a314b;
	background: #14202d;
}

.new-deposit-step-list-item.active .new-deposit-step-list-item__count {
	color: #17c964;
}

.new-deposit-step-list-item.active .new-deposit-step-list-item__title {
	color: #ebf4ff;
}


.new-deposit-step-list-item.ready {}

.new-deposit-step-list-item.ready .new-deposit-step-list-item__count {
	opacity: 0;
	visibility: hidden;
}

.new-deposit-step-list-item.ready .new-deposit-step-list-item__count-block::before {
	opacity: 1;
	visibility: visible;
}



.new-deposit-form-block {
	padding-top: 12px;
}

.new-deposit-form-steps {}

.new-deposit-form-step {
	background-color: #14202d;
	border-radius: 8px;
	padding: 12px;
	display: none;
}

.new-deposit-form-step:first-child {
	display: block;
}


.new-deposit-form-step-top-block {}

.new-deposit-form-step-top {}

.new-deposit-form-step-title {
	color: #ebf4ff;
}

.new-deposit-form-step-descr {
	margin-top: 1px;
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.new-dep-ps-block {
	margin-top: 12px;
}

.new-dep-ps-search-block {}


.new-dep-ps-radio-items-block {
	margin-top: 12px;
}

.new-dep-ps-radio-items {
	margin-top: -12px;
	margin-left: -6px;
	margin-right: -6px;
	display: flex;
	flex-wrap: wrap;
}

.new-dep-ps-radio-item-wrapper {
	width: 33.33%;
	margin-top: 12px;
	padding-left: 6px;
	padding-right: 6px;
}

.new-dep-ps-radio-item-label {
	display: block;
	margin: 0;
}

.new-dep-ps-radio-item {
	display: flex;
	position: relative;
	border: 1px solid #1a314b;
	border-radius: 8px;
	background: #172a3e;
	padding: 12px;
	cursor: pointer;
	transition: 0.4s ease;
}

.new-dep-ps-radio-item__icon {
	width: 36px;
	height: 36px;
}

.new-dep-ps-radio-item__icon .image {
	width: 36px;
	height: 36px;
}

.new-dep-ps-radio-item__content {
	width: calc(100% - 36px);
	padding-left: 12px;
	padding-right: 60px;
}

.new-dep-ps-radio-item__amount {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #ebf4ff;
}

.new-dep-ps-radio-item__abbr {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.new-dep-ps-radio-item__currency {
	top: 12px;
	right: 12px;
	position: absolute;
	font-size: 12px;
	line-height: 1.25;
	text-align: right;
	color: #406084;
	transition: 0.4s ease;
}

.radio:checked~.new-dep-ps-radio-item {
	box-shadow: 0 3px 12px 0 rgba(23, 201, 100, 0.12);
	background-color: #172a3e;
	border-color: #17c964;
}

.radio:checked~.new-dep-ps-radio-item .new-dep-ps-radio-item__currency {
	color: #ebf4ff;
}

.new-dep-ps-radio-item-label--disabled {
	pointer-events: none;
	opacity: 0.5;
}

.new-deposit-form-next-step-btn-block {
	margin-top: 12px;
}

.new-deposit-form-next-step-btn {
	width: 100%;
}

.new-deposit-form-step--payment-system {}

.new-deposit-form-step--payment-system .new-deposit-form-next-step-btn {
	max-width: 274px;
}

.new-deposit-form-step--investment-plan {}

.new-deposit-form-step--investment-plan .new-deposit-form-next-step-btn {
	max-width: 274px;
}




.new-dep-inv-plan-radio-items-block {
	margin-top: 12px;
}

.new-dep-inv-plan-radio-items {
	margin-top: -12px;
	margin-left: -6px;
	margin-right: -6px;
	display: flex;
	flex-wrap: wrap;
}

.new-dep-inv-plan-radio-item-wrapper {
	width: 33.33%;
	margin-top: 12px;
	padding-left: 6px;
	padding-right: 6px;
}

.new-dep-inv-plan-radio-item-label {
	display: block;
	margin: 0;
}

.new-dep-inv-plan-radio-item {
	position: relative;
	border: 1px solid #1a314b;
	border-radius: 8px;
	background: #172a3e;
	padding: 15px;
	cursor: pointer;
	transition: 0.4s ease;
}


.new-dep-inv-plan-radio-item-title-block {}

.new-dep-inv-plan-radio-item-title {
	display: flex;
	align-items: center;
}

.new-dep-inv-plan-radio-item-title__icon-block {
	width: 22px;
	height: 24px;
	position: relative;
}

.new-dep-inv-plan-radio-item-title__icon {
	width: 22px;
	height: 24px;
	position: relative;
	transition: 0.4s ease;
}

.new-dep-inv-plan-radio-item-title__icon-hover {
	width: 22px;
	height: 24px;
	position: absolute;
	left: 0;
	top: 0;
	opacity: 0;
	visibility: hidden;
	transition: 0.4s ease;
}

.new-dep-inv-plan-radio-item-title__icon::before {
	content: '';
	position: absolute;
	width: 22px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/dashboard/new-dep-inv-plan-radio-item-title-icon.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 22px 24px;
	-webkit-mask-image: url('../images/svg/dashboard/new-dep-inv-plan-radio-item-title-icon.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 22px 24px;
	background-color: #406084;
}

.new-dep-inv-plan-radio-item-title__text {
	width: calc(100% - 22px);
	padding-left: 8px;
	font-weight: 500;
	font-size: 18px;
	line-height: 1.25;
	color: #ebf4ff;
}

.new-dep-inv-plan-radio-item-title__text-percent {
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.new-dep-inv-plan-radio-item-currency-panel-block {
	margin-top: 8px;
}

.new-dep-inv-plan-radio-item-currency-panel {
	padding-top: 4px;
	padding-bottom: 4px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.new-dep-inv-plan-radio-item-currency-panel-left {
	padding-right: 8px;
}

.new-dep-inv-plan-radio-item-currency-panel-title {
	font-weight: 500;
	font-size: 12px;
	line-height: 1.25;
	color: #406084;
}

.new-dep-inv-plan-radio-item-currency-panel-right {}

.new-dep-inv-plan-radio-item-currency {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.new-dep-inv-plan-radio-item-currency__title {
	padding-right: 4px;
	font-weight: 500;
	font-size: 12px;
	line-height: 1.25;
	text-align: right;
	color: #ebf4ff;
}

.new-dep-inv-plan-radio-item-currency__icon {
	width: 20px;
	height: 20px;
}

.new-dep-inv-plan-radio-item-currency__icon .image {
	width: 20px;
	height: 20px;
}

.new-dep-inv-plan-radio-item-params-block {
	margin-top: 8px;
}

.new-dep-inv-plan-radio-item-params {}

.new-dep-inv-plan-radio-item-param {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #1d3045;
	padding-top: 8px;
	padding-bottom: 8px;
}

.new-dep-inv-plan-radio-item-param:first-child {
	padding-top: 0;
}

.new-dep-inv-plan-radio-item-param:last-child {
	padding-bottom: 0;
	border-bottom: 0;
}

.new-dep-inv-plan-radio-item-param-left {
	padding-right: 8px;
}

.new-dep-inv-plan-radio-item-param-title {
	display: flex;
	align-items: center;
}

.new-dep-inv-plan-radio-item-param-title__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.new-dep-inv-plan-radio-item-param-title__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #406084;
}

.new-dep-inv-plan-radio-item-param-title__text {
	padding-left: 4px;
	font-weight: 500;
	font-size: 12px;
	line-height: 125%;
	color: #406084;
	width: calc(100% - 20px);
}

.new-dep-inv-plan-radio-item-param-right {}

.new-dep-inv-plan-radio-item-param-value {
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	text-align: right;
	color: #ebf4ff;
}


.new-dep-inv-plan-radio-item-param--min {}

.new-dep-inv-plan-radio-item-param--min .new-dep-inv-plan-radio-item-param-title__icon::before {
	mask-image: url('../images/svg/arrow-fat-line-down.svg');
	-webkit-mask-image: url('../images/svg/arrow-fat-line-down.svg');
	background-color: #BD3D44;
}

.new-dep-inv-plan-radio-item-param--max {}

.new-dep-inv-plan-radio-item-param--max .new-dep-inv-plan-radio-item-param-title__icon::before {
	mask-image: url('../images/svg/arrow-fat-line-up.svg');
	-webkit-mask-image: url('../images/svg/arrow-fat-line-up.svg');
	background-color: #17C964;
}

.new-dep-inv-plan-radio-item-param--duration {}

.new-dep-inv-plan-radio-item-param--duration .new-dep-inv-plan-radio-item-param-title__icon::before {
	mask-image: url('../images/svg/calendar-dots.svg');
	-webkit-mask-image: url('../images/svg/calendar-dots.svg');
}

.new-dep-inv-plan-radio-item-btn-block {
	margin-top: 8px;
}

.new-dep-inv-plan-radio-item-btn {
	position: relative;
	padding-top: 4px;
	padding-bottom: 4px;
	font-size: 16px;
	width: 100%;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: 0.4s ease;
}

.new-dep-inv-plan-radio-item-btn__text {
	position: relative;
	transition: 0.4s ease;
}

.new-dep-inv-plan-radio-item-btn__icon {
	width: 24px;
	height: 24px;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	transition: 0.4s ease;
	opacity: 0;
	visibility: hidden;
}

.new-dep-inv-plan-radio-item-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/check-circle.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/check-circle.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #17C964;
}


.new-dep-inv-plan-radio-item:hover {
	box-shadow: 0 3px 12px 0 rgba(23, 201, 100, 0.12);
	border-color: #17c964;
}

.radio:checked~.new-dep-inv-plan-radio-item {
	box-shadow: 0 3px 12px 0 rgba(23, 201, 100, 0.12);
	border-color: #17c964;
}

.radio:checked~.new-dep-inv-plan-radio-item .new-dep-inv-plan-radio-item-btn__text {
	opacity: 0;
	visibility: hidden;
}

.radio:checked~.new-dep-inv-plan-radio-item .new-dep-inv-plan-radio-item-btn__icon {
	opacity: 1;
	visibility: visible;
}

.radio:checked~.new-dep-inv-plan-radio-item .new-dep-inv-plan-radio-item-btn {
	border-color: #1a314b;
	background-color: transparent;
}

.radio:checked~.new-dep-inv-plan-radio-item .new-dep-inv-plan-radio-item-title__icon {
	opacity: 0;
	visibility: hidden;
}

.radio:checked~.new-dep-inv-plan-radio-item .new-dep-inv-plan-radio-item-title__icon-hover {
	opacity: 1;
	visibility: visible;
}


.new-deposit-form-step--amount {}

.new-deposit-form-step--amount .new-deposit-form-next-step-btn {
	max-width: 420px;
}

.new-deposit-form-amount-block {
	margin-top: 12px;
	max-width: 420px;
}

.new-deposit-form-amount {}

.new-deposit-form-amount .field--amount .field-icon {
	display: none;
}

.field-currency-label-block {}

.field-currency-label {
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
}




.new-deposit-form-amount-params-block {
	margin-top: 12px;
}

.new-deposit-form-amount-params {}

.new-deposit-form-amount-param {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #1d3045;
	padding-top: 8px;
	padding-bottom: 8px;
}

.new-deposit-form-amount-param:first-child {
	padding-top: 0;
}

.new-deposit-form-amount-param:last-child {
	padding-bottom: 0;
	border-bottom: 0;
}

.new-deposit-form-amount-param-left {
	padding-right: 8px;
}

.new-deposit-form-amount-param-title {
	display: flex;
	align-items: center;
}

.new-deposit-form-amount-param-title__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.new-deposit-form-amount-param-title__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #406084;
}

.new-deposit-form-amount-param-title__text {
	padding-left: 4px;
	font-weight: 500;
	font-size: 12px;
	line-height: 125%;
	color: #406084;
	width: calc(100% - 20px);
}

.new-deposit-form-amount-param-right {}

.new-deposit-form-amount-param-value {
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	text-align: right;
	color: #ebf4ff;
}


.new-deposit-form-amount-param--min {}

.new-deposit-form-amount-param--min .new-deposit-form-amount-param-title__icon::before {
	mask-image: url('../images/svg/arrow-fat-line-down.svg');
	-webkit-mask-image: url('../images/svg/arrow-fat-line-down.svg');
	background-color: #BD3D44;
}

.new-deposit-form-amount-param--max {}

.new-deposit-form-amount-param--max .new-deposit-form-amount-param-title__icon::before {
	mask-image: url('../images/svg/arrow-fat-line-up.svg');
	-webkit-mask-image: url('../images/svg/arrow-fat-line-up.svg');
	background-color: #17C964;
}

.new-deposit-form-amount-info-items-block {
	margin-top: 12px;
}

.new-deposit-form-amount-info-items {}

.new-deposit-form-amount-info-item-wrapper {
	margin-top: 12px;
}

.new-deposit-form-amount-info-item-wrapper:first-child {
	margin-top: 0;
}

.new-deposit-form-amount-info-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #0e1b28;
	border-radius: 8px;
	padding: 12px;
}

.new-deposit-form-amount-info-item-left {
	padding-right: 8px;
}

.new-deposit-form-amount-info-item-right {}

.new-deposit-form-amount-info-currency {
	display: flex;
	align-items: center;
}

.new-deposit-form-amount-info-currency__icon {
	width: 20px;
	height: 20px;
}

.new-deposit-form-amount-info-currency__icon .image {
	width: 20px;
	height: 20px;
}

.new-deposit-form-amount-info-currency__title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
	max-width: calc(100% - 20px);
	padding-left: 8px;
}

.new-deposit-form-amount-info-item-title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.new-deposit-form-amount-info-value {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #ebf4ff;
}

.deposits-block {}

.deposits {}

.deposits-top-block {}

.deposits-top {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.deposits-top h1 {
	color: #fff;
}

.deposits-top-descr {
	margin-top: 16px;
	line-height: 1.45;
	color: #7998ba;
}


.deposits-filter-block {
	margin-top: 16px;
}

.deposits-filter-panel {
	padding-top: 16px;
}

.deposits-filter-form-block {}

.deposits-filter-form {}

.deposits-filter-form-row {
	display: flex;
	flex-wrap: wrap;
	align-items: flex-end;
	margin-left: -4px;
	margin-right: -4px;
	margin-top: -8px;
}

.deposits-filter-form-col {
	margin-top: 8px;
	width: 25%;
	padding-left: 4px;
	padding-right: 4px;
}

.deposits-filter-form-col--investment-plan {}

.deposits-filter-form-col--currency {}

.deposits-filter-form-col--status {}

.deposits-filter-form-col--buttons {}

.deposits-filter-form-buttons-block {}

.deposits-filter-form-buttons {
	display: flex;
	margin-left: -4px;
	margin-right: -4px;
}

.deposits-filter-form-btn-wrapper {
	width: 50%;
	padding-left: 4px;
	padding-right: 4px;
}

.deposits-filter-form-apply-btn {
	width: 100%;
	font-size: 16px;
	padding-left: 11px;
	padding-right: 11px;
}

.deposits-filter-form-cancel-btn {
	width: 100%;
	font-size: 16px;
	padding-left: 11px;
	padding-right: 11px;
}


.deposits-content-block {
	margin-top: 12px;
}

.deposits-content {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.deposits-content-top-block {}

.deposits-content-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.deposits-content-top-left {
	padding-right: 8px;
}

.deposits-content-top-right {}



.deposits-table-block {
	margin-top: 12px;
}

.deposits-table-block .pagination {
	justify-content: center;
}

.deposits-table {}

.deposits-t-heading {
	display: flex;
}

.deposits-t-h-col {
	width: 16.66%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.deposits-t-h-col--plan {
	width: 18.5%;
}

.deposits-t-h-col--status {
	width: 16.75%;
}

.deposits-t-h-col--total-profit {
	width: 20%;
}

.deposits-t-h-col--start-date {
	width: 19.25%;
}

.deposits-t-h-col--duration {
	width: 15.25%;
}


.deposits-t-h-col--details {
	width: 10.25%;
	text-align: right;
}

.deposits-t-items {}

.deposits-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	background: #0e1b28;
	position: relative;
	padding-bottom: 4px;
	margin-top: 12px;
}

.deposits-t-item:first-child {
	/* margin-top: 0; */
}

.deposits-t-item:nth-child(odd) {}

.deposits-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 16.66%;
}


.deposits-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.deposits-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.deposits-t-i-col__value-block {
	width: 100%;
}


.deposits-t-i-col--plan {
	width: 18.5%;
}

.deposits-t-i-col--status {
	width: 16.75%;
}

.deposits-t-i-col--total-profit {
	width: 20%;
}

.deposits-t-i-col--start-date {
	width: 19.25%;
}

.deposits-t-i-col--duration {
	width: 17.25%;
}

.deposits-t-i-col--details {
	width: 8.25%;
}


.deposits-t-i-date {}

.deposits-t-i-duration {}


.deposits-t-i-plan-block {}

.deposits-t-i-plan {
	display: flex;
	align-items: center;
}

.deposits-t-i-plan__icon {
	width: 22px;
	height: 24px;
}

.deposits-t-i-plan__title {
	width: calc(100% - 22px);
	padding-left: 8px;
	font-weight: 500;
	font-size: 20px;
	line-height: 1;
	color: #ebf4ff;
}

.deposits-t-i-status-block {}

.deposits-t-i-status {
	display: flex;
	align-items: center;
}

.deposits-t-i-status__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.deposits-t-i-status__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #7998BA;
	transition: 0.4s ease;
}

.deposits-t-i-status__title {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.deposits-t-i-status--active {}

.deposits-t-i-status--active .deposits-t-i-status__icon::before {
	mask-image: url('../images/svg/hourglass.svg');
	-webkit-mask-image: url('../images/svg/hourglass.svg');
	background-color: #E5AA35;
}

.deposits-t-i-status--active .deposits-t-i-status__title {
	color: #e5aa35;
}

.deposits-t-i-status--completed {}

.deposits-t-i-status--completed .deposits-t-i-status__icon::before {
	mask-image: url('../images/svg/check-circle.svg');
	-webkit-mask-image: url('../images/svg/check-circle.svg');
	background-color: #2EE57A;
}

.deposits-t-i-status--completed .deposits-t-i-status__title {
	color: #2EE57A;
}



.deposits-t-i-profit-block {}

.deposits-t-i-profit {
	display: flex;
	align-items: center;
}

.deposits-t-i-profit__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.deposits-t-i-profit__content {
	width: calc(100% - 16px);
	padding-left: 8px;
	font-size: 14px;
	line-height: 1.25;
	font-weight: 400;
	color: #7998ba;
}

.deposits-t-i-profit__amount {
	font-weight: 500;
	color: #2ee57a;
}

.deposits-t-i-details-btn-block {
	display: flex;
	justify-content: flex-end;
}

.deposits-t-i-details-btn {
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	position: relative;
	border: 0;
}

.deposits-t-i-details-btn::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/eye.svg');
	background-color: #6B88A8;
	transition: 0.4s ease;
}


.deposits-t-i-progress {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 4px;
	border-radius: 8px;
	background-color: #172a3e;
}

.deposits-t-i-progress__progress {
	position: absolute;
	left: 0;
	width: 0;
	height: 100%;
	border-radius: 8px;
	background-color: #0650a4;
}

.deposits-t-i-progress--complete {}

.deposits-t-i-progress--complete .deposits-t-i-progress__progress {
	background-color: #17c964;
}



.deposits-filter-panel-btn-block {
	display: none;
}

.deposits-filter-panel-btn {
	display: flex;
	align-items: center;
	text-decoration: none;
	border: 0;
	padding: 0;
}

.deposits-filter-panel-btn__icon {
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	position: relative;
}

.deposits-filter-panel-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/caret-down.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/caret-down.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
	transition: 0.4s ease;
}


.deposits-filter-panel-btn__text {
	width: calc(100% - 44px);
	padding-left: 12px;
	font-weight: 500;
	font-size: 20px;
	line-height: 1.25;
	color: #406084;
}


.deposits-filter-panel-btn.active .deposits-filter-panel-btn__icon::before {
	transform: translate(-50%, -50%) rotate(-180deg);
}

.deposits-filter-panel {}





.modal--deposit {}

.modal--deposit .modal-dialog {
	max-width: 440px;
}

.modal--deposit .modal-content {
	padding: 32px;
	border: 0;
}

.modal--deposit .modal-header {}

.modal--deposit .modal-body-content {
	padding-top: 16px;
}

.modal--deposit .modal-close {
	top: 0;
	right: 0;
}



.m-deposit-status-block {
	padding-top: 7px;
	padding-bottom: 7px;
}

.m-deposit-status {
	display: flex;
	align-items: center;
}

.m-deposit-status__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.m-deposit-status__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #7998BA;
	transition: 0.4s ease;
}

.m-deposit-status__title {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.m-deposit-status--active {}

.m-deposit-status--active .m-deposit-status__icon::before {
	mask-image: url('../images/svg/hourglass.svg');
	-webkit-mask-image: url('../images/svg/hourglass.svg');
	background-color: #E5AA35;
}

.m-deposit-status--active .m-deposit-status__title {
	color: #e5aa35;
}

.m-deposit-status--completed {}

.m-deposit-status--completed .m-deposit-status__icon::before {
	mask-image: url('../images/svg/check-circle.svg');
	-webkit-mask-image: url('../images/svg/check-circle.svg');
	background-color: #2EE57A;
}

.m-deposit-status--completed .m-deposit-status__title {
	color: #2EE57A;
}

.m-deposit-block {}

.m-deposit {}

.m-deposit-title-block {}

.m-deposit-title {
	font-weight: 500;
	font-size: 20px;
	line-height: 1;
	color: #ebf4ff;
}

.m-deposit-progress-block {
	margin-top: 16px;
}

.m-deposit-progress {
	height: 4px;
	background-color: #172a3e;
	width: 100%;
	position: relative;
	border-radius: 4px;
}

.m-deposit-progress__progress {
	position: absolute;
	left: 0;
	top: 0;
	width: 0;
	height: 100%;
	border-radius: 4px;
	background-color: #0650A4;
}

.m-deposit-param-items-block {
	margin-top: 12px;
}

.m-deposit-param-items {}

.m-deposit-param-item {
	margin-top: 8px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px;
	background: #0e1b28;
	border-radius: 8px
}

.m-deposit-param-item:first-child {
	margin-top: 0;
}

.m-deposit-param-item-left {
	padding-right: 8px;
}

.m-deposit-param-item-title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.m-deposit-param-item-right {}

.m-deposit-param-item-value-block {}

.m-deposit-param-item-value {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
}

.m-deposit-param-item-value--red {
	color: #e53535;
}

.m-deposit-param-item-value--green {
	color: #17c964;
}

.m-deposit-param-item-currency {
	display: flex;
	align-items: center;
}

.m-deposit-param-item-currency__icon {
	width: 20px;
	height: 20px;
}

.m-deposit-param-item-currency__icon .image {
	width: 20px;
	height: 20px;
}

.m-deposit-param-item-currency__title {
	padding-left: 8px;
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
	width: calc(100% - 20px);
}

.m-deposit-param-item-start {
	display: flex;
	align-items: center;
}

.m-deposit-param-item-start__title {
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
	padding-right: 8px;
}

.m-deposit-param-item-start__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.m-deposit-param-item-start__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/timer.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/timer.svg');
	background-color: #406084;
	transition: 0.4s ease;
}


.m-deposit-param-item-finish {
	display: flex;
	align-items: center;
}

.m-deposit-param-item-finish__title {
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
	padding-right: 8px;
}

.m-deposit-param-item-finish__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.m-deposit-param-item-finish__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/flag-checkered.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/flag-checkered.svg');
	background-color: #406084;
	transition: 0.4s ease;
}


.m-deposit-param-item--currency {}

.m-deposit-param-item--amount {}

.m-deposit-param-item--amount .m-deposit-param-item-value {
	font-size: 16px;
}

.m-deposit-param-item--total-profit {}

.m-deposit-param-item--total-profit .m-deposit-param-item-value {
	font-size: 16px;
}

.m-deposit-param-item--next-refill {}

.m-deposit-param-item--duration {}

.m-deposit-param-item--start {}

.m-deposit-param-item--finish {}





.refill-block {}

.refill {
	display: flex;
}

.refill-left {
	width: 316px;
}

.refill-right {
	width: calc(100% - 316px);
	padding-left: 12px;
}





.refill-info-block {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
	height: 100%;
}

.refill-info {}

.refill-info h1 {
	color: #ebf4ff;
}

.refill-info-descr {
	margin-top: 16px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.account-info-balance-block {
	margin-top: 16px;
}

.account-info-balance-title {
	font-weight: 600;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.account-info-balance-items-block {
	margin-top: 16px;
	padding-top: 4px;
	padding-bottom: 4px;
}

.account-info-balance-items {
	display: flex;
	flex-wrap: wrap;
	margin-top: -12px;
	margin-left: -6px;
	margin-right: -6px;
}

.account-info-balance-item-wrapper {
	width: 100%;
	padding-left: 6px;
	padding-right: 6px;
}

.account-info-balance-item-wrapper:nth-child(odd) .account-info-balance-item {
	background: #172a3e;
}

.account-info-balance-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-radius: 8px;
	padding: 8px;
}

.account-info-balance-item-left {}

.account-info-balance-item-currency {
	display: flex;
	align-items: center;
}

.account-info-balance-item-currency__icon {
	width: 20px;
	height: 20px;
}

.account-info-balance-item-currency__icon .image {
	width: 20px;
	height: 20px;
}

.account-info-balance-item-currency__amount {
	width: calc(100% - 20px);
	padding-left: 8px;
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
}

.account-info-balance-item-currency__amount--empty {
	color: #406084;
}

.account-info-balance-item-right {}

.account-info-balance-item-abbr {
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	text-align: right;
	color: #406084;
}

.account-info-balance-search-block {
	margin-top: 8px;
}

.refill-content-block {
	background: #14202d;
	border-radius: 8px;
	padding: 140px 12px;
}

.refill-form-block {
	max-width: 330px;
	margin-right: auto;
	margin-left: auto;
}

.refill-form-title {
	text-align: center;
	color: #ebf4ff;
}

.refill-form-descr {
	margin-top: 1px;
	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #406084;
}

.refill-form {
	margin-top: 24px;
}

.field--protocol {}


.field--protocol .field-icon {
	z-index: 2;
}

.field--protocol .field-icon::before {
	mask-image: url('../images/svg/shield-check.svg');
	-webkit-mask-image: url('../images/svg/shield-check.svg');
}

.field--protocol .bootstrap-select>.dropdown-toggle {
	padding-left: 44px;
}



.field--wallet .field-icon::before {
	mask-image: url('../images/svg/wallet.svg');
	-webkit-mask-image: url('../images/svg/wallet.svg');
}

.field--wallet textarea {
	height: 56px;
	padding-top: 6px;
	padding-bottom: 6px;
	padding-right: 56px;
}

.field--wallet .field-right-panel-block {
	right: 8px;
}

.field--destination-tag .field-icon::before {
	mask-image: url('../images/svg/paper-plane-tilt.svg');
	-webkit-mask-image: url('../images/svg/paper-plane-tilt.svg');
}


.refill-form .field--destination-tag input[type='text'],
.refill-form .field--destination-tag input[type='email'],
.refill-form .field--destination-tag input[type='password'] {
	height: 56px;
	padding-right: 56px;
}

.field--destination-tag .field-right-panel-block {
	right: 8px;
}

.copy-field-btn.purple-btn {
	width: 40px;
	height: 40px;
}

.copy-field-btn.purple-btn::before {
	background-color: #EBF4FF;
}


.field--textarea .field-icon {
	transform: none;
	top: 8px;
}




.withdrawal-block {}

.withdrawal {
	display: flex;
}

.withdrawal-left {
	width: 316px;
}

.withdrawal-right {
	width: calc(100% - 316px);
	padding-left: 12px;
}





.withdrawal-info-block {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
	height: 100%;
}

.withdrawal-info {}

.withdrawal-info h1 {
	color: #ebf4ff;
}

.withdrawal-info-descr {
	margin-top: 16px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}


.withdrawal-content-block {
	background: #14202d;
	border-radius: 8px;
	padding: 64px;
}

.withdrawal-form-block {
	max-width: 476px;
}

.withdrawal-form-title {
	color: #ebf4ff;
}

.withdrawal-form-descr {
	margin-top: 1px;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.withdrawal-form {
	margin-top: 24px;
}

.withdrawal-form-bottom-block {
	margin-top: 12px;
}

.withdrawal-form-bottom {
	display: flex;
	align-items: center;
}

.withdrawal-form-bottom-left {
	width: 260px;
}

.withdrawal-form-bottom-right {
	width: calc(100% - 260px);
	padding-left: 16px;
}


.withdrawal-form-bottom .form-button-block {
	margin-top: 0;
}

.withdrawal-form .send-btn .send-btn__icon::before {
	mask-image: url('../images/svg/rocket-launch.svg');
	-webkit-mask-image: url('../images/svg/rocket-launch.svg');
}

.withdrawal-form-amount-block {
	display: flex;
}

.withdrawal-form-amount {
	font-weight: 500;
	font-size: 20px;
	line-height: 1.25;
	color: #17c964;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.withdrawal-form-fees-block {
	display: flex;
}

.withdrawal-form-fees {
	position: relative;
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
	padding-right: 24px;
}

.withdrawal-form-fees::after {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	mask-image: url('../images/svg/arrow-circle-up-right-fill.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/arrow-circle-up-right-fill.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #406084;
}






.modal--recovery {}

.confirmation-form-block {}


.confirmation-form {}

.confirmation-code-fields-block {
	margin-top: 16px;
	padding-top: 16px;
	padding-bottom: 16px;
}

.confirmation-code-fields {
	display: flex;
	margin-left: -4px;
	margin-right: -4px;
}

.confirmation-code-field-wrapper {
	width: 16.66%;
	padding-left: 4px;
	padding-right: 4px;
}

.confirmation-code-field {}

.confirmation-code-field .field input[type='text'],
.confirmation-code-field .field input[type='email'],
.confirmation-code-field .field input[type='password'] {
	text-align: center;
}





.transactions-block {}

.transactions {}

.transactions-top-block {}

.transactions-top {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.transactions-top h1 {
	color: #fff;
}

.transactions-top-descr {
	margin-top: 16px;
	line-height: 1.45;
	color: #7998ba;
}


.transactions-filter-block {
	margin-top: 16px;
}

.transactions-filter-panel {
	padding-top: 16px;
}

.transactions-filter-form-block {}

.transactions-filter-form {}

.transactions-filter-form-row {
	display: flex;
	flex-wrap: wrap;
	align-items: flex-end;
	margin-left: -4px;
	margin-right: -4px;
	margin-top: -8px;
}

.transactions-filter-form-col {
	margin-top: 8px;
	width: 25%;
	padding-left: 4px;
	padding-right: 4px;
}

.transactions-filter-form-col--investment-plan {}

.transactions-filter-form-col--currency {}

.transactions-filter-form-col--status {}

.transactions-filter-form-col--buttons {}

.transactions-filter-form-buttons-block {}

.transactions-filter-form-buttons {
	display: flex;
	margin-left: -4px;
	margin-right: -4px;
}

.transactions-filter-form-btn-wrapper {
	width: 50%;
	padding-left: 4px;
	padding-right: 4px;
}

.transactions-filter-form-apply-btn {
	width: 100%;
	font-size: 16px;
	padding-left: 11px;
	padding-right: 11px;
}

.transactions-filter-form-cancel-btn {
	width: 100%;
	font-size: 16px;
	padding-left: 11px;
	padding-right: 11px;
}


.transactions-content-block {
	margin-top: 12px;
}

.transactions-content {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}



.transactions-filter-panel-btn-block {
	display: none;
}

.transactions-filter-panel-btn {
	display: flex;
	align-items: center;
	text-decoration: none;
	border: 0;
	padding: 0;
}

.transactions-filter-panel-btn__icon {
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	position: relative;
}

.transactions-filter-panel-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/caret-down.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/caret-down.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
	transition: 0.4s ease;
}


.transactions-filter-panel-btn__text {
	width: calc(100% - 44px);
	padding-left: 12px;
	font-weight: 500;
	font-size: 20px;
	line-height: 1.25;
	color: #406084;
}


.transactions-filter-panel-btn.active .transactions-filter-panel-btn__icon::before {
	transform: translate(-50%, -50%) rotate(-180deg);
}




.transactions-table-block {}

.transactions-table-block .pagination {
	justify-content: center;
}

.transactions-table-wrapper {
	position: relative;
	overflow: auto;
}


.transactions-table-wrapper::-webkit-scrollbar {
	width: 4px;
	height: 4px;
}

.transactions-table-wrapper::-webkit-scrollbar-track {
	background: #0E1B28;
	border-radius: 12px;
}

.transactions-table-wrapper::-webkit-scrollbar-thumb {
	background-color: #0170EF;
	border-radius: 12px;
}

.transactions-table {
	min-width: 1174px;
}

.transactions-t-heading {
	display: flex;
}

.transactions-t-h-col {
	width: 14.28%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.transactions-t-h-col--id {
	width: 5%;
}

.transactions-t-h-col--status {
	width: 14.4%;
}

.transactions-t-h-col--type {
	width: 15.2%;
}

.transactions-t-h-col--amount {
	text-align: right;
	width: 15.6%;
}

.transactions-t-h-col--date {
	width: 19.4%;
}

.transactions-t-h-col--description {
	width: 22.7%;
}

.transactions-t-h-col--actions {
	text-align: right;
	width: 7.6%;
}

.transactions-t-items {}

.transactions-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.transactions-t-item:nth-child(odd) {
	background: #0e1b28;
}

.transactions-t-i-col {
	display: flex;
	align-items: center;
	padding: 14px 12px;
	width: 14.28%;
}


.transactions-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.transactions-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.transactions-t-i-col__value-block {
	width: 100%;
}


.transactions-t-i-col--id {
	width: 5%;
}

.transactions-t-i-col--status {
	width: 14.4%;
}

.transactions-t-i-col--type {
	width: 15.2%;
}

.transactions-t-i-col--amount {
	width: 15.6%;
}

.transactions-t-i-col--date {
	width: 19.4%;
}

.transactions-t-i-col--description {
	width: 22.7%;
	padding: 8px 12px;
}

.transactions-t-i-col--actions {
	width: 7.6%;
}

.transactions-t-i-id {
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.transactions-t-i-amount {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.transactions-t-i-amount__amount {

	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
	padding-right: 8px;
	text-align: right;
	width: calc(100% - 16px);
}

.transactions-t-i-amount__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.transactions-t-i-amount--red .transactions-t-i-amount__amount {
	color: #e53535;
}

.transactions-t-i-amount--green .transactions-t-i-amount__amount {
	color: #2ee57a;
}

.transactions-t-i-status-block {}

.transactions-t-i-status {
	display: flex;
	align-items: center;
}

.transactions-t-i-status__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.transactions-t-i-status__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #7998BA;
	transition: 0.4s ease;
}

.transactions-t-i-status__title {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.transactions-t-i-status--progress {}

.transactions-t-i-status--progress .transactions-t-i-status__icon::before {
	mask-image: url('../images/svg/hourglass.svg');
	-webkit-mask-image: url('../images/svg/hourglass.svg');
	background-color: #e5aa35;
}

.transactions-t-i-status--progress .transactions-t-i-status__title {
	color: #e5aa35;
}

.transactions-t-i-status--completed {}

.transactions-t-i-status--completed .transactions-t-i-status__icon::before {
	mask-image: url('../images/svg/check-circle.svg');
	-webkit-mask-image: url('../images/svg/check-circle.svg');
	background-color: #2EE57A;
}

.transactions-t-i-status--completed .transactions-t-i-status__title {
	color: #2ee57a;
}

.transactions-t-i-status--error {}

.transactions-t-i-status--error .transactions-t-i-status__icon::before {
	mask-image: url('../images/svg/x-circle.svg');
	-webkit-mask-image: url('../images/svg/x-circle.svg');
	background-color: #FC5757;
}

.transactions-t-i-status--error .transactions-t-i-status__title {
	color: #FC5757;
}


.transactions-t-i-type-block {}

.transactions-t-i-type {
	display: flex;
	align-items: center;
}

.transactions-t-i-type__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.transactions-t-i-type__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #7998BA;
	transition: 0.4s ease;
}

.transactions-t-i-type__title {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.transactions-t-i-type--withdrawal {}

.transactions-t-i-type--withdrawal .transactions-t-i-type__icon::before {
	mask-image: url('../images/svg/rocket-launch.svg');
	-webkit-mask-image: url('../images/svg/rocket-launch.svg');
}

.transactions-t-i-type--deposit {}

.transactions-t-i-type--deposit .transactions-t-i-type__icon::before {
	mask-image: url('../images/svg/wallet.svg');
	-webkit-mask-image: url('../images/svg/wallet.svg');
}

.transactions-t-i-type--refill {}

.transactions-t-i-type--refill .transactions-t-i-type__icon::before {
	mask-image: url('../images/svg/plus-circle.svg');
	-webkit-mask-image: url('../images/svg/plus-circle.svg');
}

.transactions-t-i-date {
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.transactions-t-i-description {
	font-size: 12px;
	line-height: 1.25;
	color: #7998ba;
}

.transactions-t-i-actions-block {}

.transactions-t-i-actions {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-left: -4px;
	margin-right: -4px;
}

.transactions-t-i-action-wrapper {
	padding-left: 4px;
	padding-right: 4px;
}

.transactions-t-i-action-copy-btn {
	width: 20px;
	height: 20px;
	position: relative;
	display: block;
	border: 0;
	padding: 0;
}

.transactions-t-i-action-copy-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/copy.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/copy.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #7998BA;
	transition: 0.4s ease;
}

.transactions-t-i-action-copy-btn::after {
	right: 0;
}

.transactions-t-i-action-copy-btn:hover::before {
	background-color: #ebf4ff;
}

.transactions-t-i-action-copy-btn.active::after {
	opacity: 1;
	visibility: visible;
}



.transactions-t-i-action-withdraw-btn {
	width: 20px;
	height: 20px;
	display: block;
	position: relative;
	border: 0;
	padding: 0;
}

.transactions-t-i-action-withdraw-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/arrow-circle-up-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/arrow-circle-up-right.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.transactions-t-i-action-withdraw-btn:hover::before {
	background-color: #ebf4ff;
}


.transactions-empty-empty {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	height: 550px;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 48px;
}

.transactions-empty-empty__image {}

.transactions-empty-empty__descr {
	margin-top: 16px;
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #20354b;
}





.db-page--wide-menu .db-side-logo__text-block {
	width: calc(100% - 44px);
}

.db-page--wide-menu .db-side-toggle-panel-btn__text-block {
	width: calc(100% - 32px);
	transition: 0.4s width ease;
}

.db-page--wide-menu .db-sidemenu-link__text-block {
	width: calc(100% - 28px);
}

.db-side-tablet-block {
	position: fixed;
	width: 196px;
	height: 100%;
	background: #14202d;
	border-radius: 8px;
	padding: 16px 8px;
	transition: 0.4s transform ease;
	z-index: 1800;
	left: 0;
	transform: translateX(-100%);
	display: none;
}

.db-side-tablet-block.active {
	transform: translateX(0);
	box-shadow: 0 0 0 2000px rgba(13, 23, 33, 0.9);
}

.db-side-tablet-logo-block {
	display: flex;
	align-items: center;
	justify-content: center;
	padding-left: 12px;
	padding-right: 12px;
}

.db-side-tablet-logo {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
}

.db-side-tablet-logo__image {
	width: 44px;
}

.db-side-tablet-logo__text-block {
	width: calc(100% - 44px);
	padding-left: 0;
	position: relative;
	overflow: hidden;
	transition: 0.4s width ease;
}

.db-side-tablet-logo__text {
	padding-left: 12px;
}

.db-side-tablet-logo__text .image {
	width: 61px;
	min-width: 61px;
	max-width: none;
}

.db-side-tablet-panel-btn-block {
	margin-top: 24px;
	display: none;
}

.db-side-tablet-panel-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8px 4px;
	border: 0;
	width: 100%;
}

.db-side-tablet-panel-btn * {
	pointer-events: none;
}

.db-side-tablet-panel-btn__text-block {
	position: relative;
	overflow: hidden;
	width: calc(100% - 32px);
	transition: 0.4s width ease;
}

.db-side-tablet-panel-btn__text {
	padding-left: 8px;
	padding-right: 8px;
	font-weight: 500;
	font-size: 12px;
	line-height: 1.33;
	color: #406084;
	text-align: left;
	white-space: nowrap;
}

.db-side-tablet-panel-btn__icon {
	width: 32px;
	height: 32px;
	position: relative;
}

.db-side-tablet-panel-btn__icon::before {
	content: '';
	width: 32px;
	height: 32px;
	position: absolute;
	content: '';
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 32px 32px;
	mask-image: url('../images/svg/list.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 32px 32px;
	-webkit-mask-image: url('../images/svg/list.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.db-side-tablet-panel-btn.active .db-side-tablet-panel-btn__icon::before {
	mask-image: url('../images/svg/close.svg');
	-webkit-mask-image: url('../images/svg/close.svg');
}

.db-side-tablet-block .db-sidemenu-link__text-block {
	width: calc(100% - 28px);
}



.db-side-tablet-close-panel-btn-block {
	margin-top: 24px;
}

.db-side-tablet-close-panel-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8px 4px;
	border: 0;
	width: 100%;
}

.db-side-tablet-close-panel-btn * {
	pointer-events: none;
}

.db-side-tablet-close-panel-btn__text-block {
	position: relative;
	overflow: hidden;
	width: calc(100% - 32px);
	transition: 0.4s width ease;
}

.db-side-tablet-close-panel-btn__text {
	padding-left: 8px;
	padding-right: 8px;
	font-weight: 500;
	font-size: 12px;
	line-height: 1.33;
	color: #406084;
	text-align: left;
	white-space: nowrap;
}

.db-side-tablet-close-panel-btn__icon {
	width: 32px;
	height: 32px;
	position: relative;
}

.db-side-tablet-close-panel-btn__icon::before {
	content: '';
	width: 32px;
	height: 32px;
	position: absolute;
	content: '';
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 32px 32px;
	mask-image: url('../images/svg/close.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 32px 32px;
	-webkit-mask-image: url('../images/svg/close.svg');
	background-color: #406084;
}





.db-affiliate-block {}

.db-affiliate {
	display: flex;
}

.db-affiliate-left {
	width: 316px;
}

.db-affiliate-right {
	width: calc(100% - 316px);
	padding-left: 12px;
}

.db-affiliate-info-block {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
	height: 100%;
}

.db-affiliate-info {}

.db-affiliate-info h1 {
	color: #ebf4ff;
}

.db-affiliate-info-descr {
	margin-top: 16px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.db-affiliate-info-stats-block {
	margin-top: 16px;
}


.db-affiliate-info-stats-section {
	margin-top: 16px;
}

.db-affiliate-info-stats-section:first-child {
	margin-top: 0;
}


.db-affiliate-info-stats-title {
	font-weight: 600;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.db-affiliate-info-stat-fields-block {
	margin-top: 12px;
}

.db-affiliate-info-stat-fields {
	display: flex;
	flex-wrap: wrap;
	margin-top: -12px;
	margin-left: -6px;
	margin-right: -6px;
}

.db-affiliate-info-stat-field-wrapper {
	margin-top: 12px;
	width: 100%;
	padding-left: 6px;
	padding-right: 6px;
}

.db-affiliate-info-stat-field {
	border-radius: 8px;
	padding: 8px 8px 8px 16px;
	background: #0e1b28;
	display: flex;
	flex-direction: row-reverse;
}

.db-affiliate-info-stat-field__icon {
	width: 40px;
	height: 40px;
	background: #172a3e;
	border-radius: 40px;
	position: relative;
}

.db-affiliate-info-stat-field__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.db-affiliate-info-stat-field__content {
	width: 100%;
}

.db-affiliate-info-stat-field__title {
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.db-affiliate-info-stat-field__value {
	font-weight: 500;
	font-size: 20px;
	line-height: 1.25;
	color: #ebf4ff;
}

.db-affiliate-info-stat-field__icon+.db-affiliate-info-stat-field__content {
	width: calc(100% - 40px);
	padding-right: 8px;
}


.db-affiliate-info-stat-field-wrapper--total {}

.db-affiliate-info-stat-field--total {}

.db-affiliate-info-stat-field--total .db-affiliate-info-stat-field__icon::before {
	mask-image: url('../images/svg/users-four-fill.svg');
	-webkit-mask-image: url('../images/svg/users-four-fill.svg');
}

.db-affiliate-info-stat-field--total .db-affiliate-info-stat-field__value {
	color: #17c964;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}


.db-affiliate-info-stat-field-wrapper--lottery {
	width: 50%;
}

.db-affiliate-info-stat-field--lottery {}

.db-affiliate-info-stat-field-wrapper--casino {
	width: 50%;
}

.db-affiliate-info-stat-field--casino {}



.db-affiliate-info-stat-field-wrapper--structure {
	width: 50%;
}

.db-affiliate-info-stat-field--structure {}

.db-affiliate-info-stat-field-wrapper--earnings {
	width: 50%;
}

.db-affiliate-info-stat-field--earnings {}



.db-affiliate-info-stat-field-wrapper--bonuses {}

.db-affiliate-info-stat-field--bonuses {}

.db-affiliate-info-stat-field--bonuses .db-affiliate-info-stat-field__icon::before {
	mask-image: url('../images/svg/shooting-star.svg');
	-webkit-mask-image: url('../images/svg/shooting-star.svg');
}

.db-affiliate-info-stat-field--bonuses .db-affiliate-info-stat-field__value {
	color: #17c964;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.db-affiliate-info-affiliate-block {
	margin-top: 16px;
}

.db-affiliate-info-affiliate-title {
	font-weight: 600;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.db-affiliate-info-panel {}


.db-affiliate-info-affiliate-copy-link-btn-block {
	display: flex;
}

.db-affiliate-info-affiliate-copy-link-btn {
	width: 32px;
	height: 32px;
	padding: 0;
}

.db-affiliate-info-affiliate-copy-link-btn .iconed-btn__icon::before {
	mask-image: url('../images/svg/link.svg');
	-webkit-mask-image: url('../images/svg/link.svg');
}

.db-affiliate-info-affiliate-copy-link-btn::after {
	right: 0;
}


.db-affiliate-info-affiliate-link-info-block {
	margin-top: 12px;
}

.db-affiliate-info-affiliate-link-info {
	border: 1px solid #1a314b;
	background-color: #172a3e;
	border-radius: 8px;
	padding: 11px;
	display: flex;
}

.db-affiliate-info-affiliate-link-info-share-btn-block {
	width: 20px;
	height: 20px;
	position: relative;
}

.db-affiliate-info-affiliate-link-info-share-btn {
	width: 20px;
	height: 20px;
	position: relative;
	border: 0;
	padding: 0;
}

.db-affiliate-info-affiliate-link-info-share-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/share-network.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/share-network.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #047BDF;
}

.db-affiliate-info-affiliate-link-info-content {
	width: calc(100% - 20px);
	padding-left: 10px;
	display: flex;
}

.db-affiliate-info-affiliate-link-info-content-left {
	width: calc(100% - 32px);
	padding-right: 10px;
}

.db-affiliate-info-affiliate-link-info-content-right {
	width: 32px;
}

.db-affiliate-info-affiliate-link-info-link-block {}

.db-affiliate-info-affiliate-link-info-link {
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
	position: relative;
	overflow-wrap: break-word;
}

.db-affiliate-info-affiliate-link-info-descr {
	margin-top: 10px;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}


.db-affiliate-tabs-block {
	border-radius: 8px;
	background: #172a3e;
}

.db-affiliate-tabs {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
}

.db-affiliate-tab-wrapper {
	padding-left: 8px;
	padding-right: 8px;
}

.db-affiliate-tab {
	padding: 13px 15px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.db-affiliate-tab__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
	transition: 0.4s ease;
	position: relative;
}

.db-affiliate-tab__icon {
	width: 24px;
	height: 24px;
	position: relative;
	margin-top: -2px;
	margin-bottom: -2px;
}

.db-affiliate-tab__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	transition: 0.4s ease;
}


.db-affiliate-tab__icon+.db-affiliate-tab__title {
	padding-left: 8px;
}

.db-affiliate-tab__count-block {
	padding-left: 8px;
}

.db-affiliate-tab__count {
	border-radius: 16px;
	min-width: 16px;
	width: 16px;
	height: 16px;
	box-shadow: 0 1px 4px 0 rgba(46, 229, 122, 0.25);
	background: #17c964;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 600;
	font-size: 10px;
	line-height: 1.25;
	color: #fff;
}



.db-affiliate-tab:hover,
.db-affiliate-tab.active {
	border-color: #1a314b;
	background: #14202d;
}

.db-affiliate-tab.active {
	pointer-events: none;
}

.db-affiliate-tab:hover .db-affiliate-tab__title,
.db-affiliate-tab.active .db-affiliate-tab__title {
	color: #ebf4ff;
}

.db-affiliate-tab:hover .db-affiliate-tab__icon::before,
.db-affiliate-tab.active .db-affiliate-tab__icon::before {
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}


.db-affiliate-tab--products {}

.db-affiliate-tab--products .db-affiliate-tab__icon::before {
	mask-image: url('../images/svg/link.svg');
	-webkit-mask-image: url('../images/svg/link.svg');
}

.db-affiliate-tab--products.active .db-affiliate-tab__icon::before {
	mask-image: url('../images/svg/link.svg');
	-webkit-mask-image: url('../images/svg/link.svg');
}

.db-affiliate-tab--casino-lottery {}

.db-affiliate-tab--casino-lottery .db-affiliate-tab__icon::before {
	mask-image: url('../images/svg/trophy.svg');
	-webkit-mask-image: url('../images/svg/trophy.svg');
}

.db-affiliate-tab--casino-lottery.active .db-affiliate-tab__icon::before {
	mask-image: url('../images/svg/trophy-fill.svg');
	-webkit-mask-image: url('../images/svg/trophy-fill.svg');
}

.db-affiliate-tab--partners {}

.db-affiliate-tab--partners .db-affiliate-tab__icon::before {
	mask-image: url('../images/svg/users-four.svg');
	-webkit-mask-image: url('../images/svg/users-four.svg');
}

.db-affiliate-tab--partners.active .db-affiliate-tab__icon::before {
	mask-image: url('../images/svg/users-four-fill.svg');
	-webkit-mask-image: url('../images/svg/users-four-fill.svg');
}

.db-affiliate-tab--promo {}

.db-affiliate-tab--promo .db-affiliate-tab__icon::before {
	mask-image: url('../images/svg/confetti.svg');
	-webkit-mask-image: url('../images/svg/confetti.svg');
}

.db-affiliate-tab--promo.active .db-affiliate-tab__icon::before {
	mask-image: url('../images/svg/confetti.svg');
	-webkit-mask-image: url('../images/svg/confetti.svg');
}

.db-affiliate-tabs-content-block {
	padding-top: 12px;
}

.db-affiliate-tabs-content {}

.db-affiliate-tab-content {
	position: relative;
	display: none;
}

.db-affiliate-tab-content:first-child {
	display: block;
}

.db-affiliate-tab-content:nth-child(4) {
	/* display: block; */
}



.db-affiliate-program-block {
	background-color: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.db-affiliate-program-top-block {}

.db-affiliate-program-top {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
}

.db-affiliate-program-top-left {
	padding-right: 8px;
}

.db-affiliate-program-top-content {}

.db-affiliate-program-top-title {
	color: #ebf4ff;
}

.db-affiliate-program-top-descr {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.db-affiliate-program-top-right {}



.db-affiliate-program-tabs-block {
	border-radius: 8px;
	background: #172a3e;
	padding: 4px;
}

.db-affiliate-program-tabs {
	margin-left: -6px;
	margin-right: -6px;
	display: flex;
}

.db-affiliate-program-tab-wrapper {
	padding-left: 6px;
	padding-right: 6px;
}

.db-affiliate-program-tab {
	padding: 9px 17px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.db-affiliate-program-tab__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
	transition: 0.4s ease;
	position: relative;
}


.db-affiliate-program-tab:hover,
.db-affiliate-program-tab.active {
	border-color: #1a314b;
	background: #14202d;
}

.db-affiliate-program-tab.active {
	pointer-events: none;
}

.db-affiliate-program-tab:hover .db-affiliate-program-tab__title,
.db-affiliate-program-tab.active .db-affiliate-program-tab__title {
	color: #ebf4ff;
}

.db-affiliate-program-tabs-content {}

.db-affiliate-program-tab-content {
	display: none;
}

.db-affiliate-program-tab-content:first-child {
	display: block;
}

/* .db-affiliate-program-tab-content:nth-child(2) {
		display: block;
	} */

.db-affiliate-program-table-block {
	margin-top: 12px;
}

.db-affiliate-program-table {}

.db-affiliate-program-t-heading {
	display: flex;
}

.db-affiliate-program-t-h-col {
	width: 11.11%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-affiliate-program-t-h-col--name {
	width: 80px;
	min-width: 80px;
}

.db-affiliate-program-t-h-col--structure {
	width: calc(16% - 10px);
}

.db-affiliate-program-t-h-col--level {
	width: calc(12% - 10px);
}

.db-affiliate-program-t-items {}

.db-affiliate-program-t-item {
	position: relative;
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-affiliate-program-t-item:nth-child(odd) {
	background: #0e1b28;
}

.db-affiliate-program-t-item.active::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	border-radius: 8px;
	height: 100%;
	width: calc(33.33% + 52px);
	border: 1px solid #17c964;
	pointer-events: none;
	z-index: 3;
}

.db-affiliate-program-t-item.active .db-affiliate-program-t-i-structure__icon::before {
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.db-affiliate-program-t-item.active .db-affiliate-program-t-i-level {
	color: #ebf4ff;
}

.db-affiliate-program-t-item.active .db-affiliate-program-t-i-level--empty {
	color: #7998BA;
}

.db-affiliate-program-t-item.active:nth-child(1)::before,
.db-affiliate-program-t-item.active:nth-child(2)::before {
	width: calc(40% + 50px);
}

.db-affiliate-program-t-item.active:nth-child(3)::before,
.db-affiliate-program-t-item.active:nth-child(4)::before,
.db-affiliate-program-t-item.active:nth-child(5)::before {
	width: calc(52% + 40px);
}

.db-affiliate-program-t-item.active:nth-child(6)::before,
.db-affiliate-program-t-item.active:nth-child(7)::before {
	width: calc(76% + 20px);
}

.db-affiliate-program-t-item.active:nth-child(8)::before,
.db-affiliate-program-t-item.active:nth-child(9)::before,
.db-affiliate-program-t-item.active:nth-child(10)::before {
	width: 100%;
}


.db-affiliate-program-t-i-col {
	position: relative;
	z-index: 2;
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 14.28%;
}


.db-affiliate-program-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-affiliate-program-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-affiliate-program-t-i-col__value-block {
	width: 100%;
}

.db-affiliate-program-t-i-col--name {
	width: 80px;
	min-width: 80px;
}

.db-affiliate-program-t-i-col--structure {
	width: calc(16% - 10px);
}

.db-affiliate-program-t-i-col--level {
	width: calc(12% - 10px);
}

.db-affiliate-program-t-i-name {}

.db-affiliate-program-t-i-level {}

.db-affiliate-program-t-i-level--empty {
	color: #7998BA;
}

.db-affiliate-program-t-i-structure {
	display: flex;
	align-items: center;
}

.db-affiliate-program-t-i-structure__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-affiliate-program-t-i-structure__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/tip-jar-fill.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/tip-jar-fill.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-affiliate-program-t-i-structure__amount {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 4px;
	width: calc(100% - 16px);
}




.db-affiliate-bonus-table-block {
	margin-top: 12px;
}

.db-affiliate-bonus-table {}

.db-affiliate-bonus-t-heading {
	display: flex;
}

.db-affiliate-bonus-t-h-col {
	width: 33.33%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-affiliate-bonus-t-h-col--name {}

.db-affiliate-bonus-t-h-col--structure {}

.db-affiliate-bonus-t-h-col--bonus {}

.db-affiliate-bonus-t-items {}

.db-affiliate-bonus-t-item {
	position: relative;
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-affiliate-bonus-t-item:nth-child(odd) {
	background: #0e1b28;
}

.db-affiliate-bonus-t-item.active::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	border-radius: 8px;
	height: 100%;
	width: 100%;
	border: 1px solid #17c964;
	pointer-events: none;
	z-index: 3;
}

.db-affiliate-bonus-t-item.active .db-affiliate-bonus-t-i-structure__icon::before {
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}


.db-affiliate-bonus-t-i-col {
	position: relative;
	z-index: 2;
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 33.33%;
}


.db-affiliate-bonus-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-affiliate-bonus-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-affiliate-bonus-t-i-col__value-block {
	width: 100%;
}

.db-affiliate-bonus-t-i-col--name {}

.db-affiliate-bonus-t-i-col--structure {}

.db-affiliate-bonus-t-i-col--bonus {}

.db-affiliate-bonus-t-i-name {}

.db-affiliate-bonus-t-i-bonus {}


.db-affiliate-bonus-t-i-structure {
	display: flex;
	align-items: center;
}

.db-affiliate-bonus-t-i-structure__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-affiliate-bonus-t-i-structure__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/shooting-star.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/shooting-star.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-affiliate-bonus-t-i-structure__amount {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-affiliate-program-warning-block {
	margin-top: 12px;
}

.db-affiliate-program-warning {
	background-color: rgba(239, 141, 50, 0.1);
	border-left: 1px solid #ef8d32;
	border-radius: 0 8px 8px 0;
	padding: 12px 14px;
	display: flex;
	position: relative;

}

.db-affiliate-program-warning__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.db-affiliate-program-warning__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	mask-image: url('../images/svg/info.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/info.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #ef8d32;
	top: 0;
	left: 0;
	transition: 0.4s ease;
}

.db-affiliate-program-warning__text {
	width: calc(100% - 20px);
	padding-left: 10px;
	font-size: 14px;
	line-height: 1.25;
	color: #ef8d32;
}



.db-affiliate-partners-block {
	background-color: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.db-affiliate-partners-top-block {}

.db-affiliate-partners-top {
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
}

.db-affiliate-partners-top-left {
	padding-right: 8px;
}

.db-affiliate-partners-top-content {}

.db-affiliate-partners-top-title {
	color: #ebf4ff;
}

.db-affiliate-partners-top-descr {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.db-affiliate-partners-top-right {}




.db-affiliate-partners-table-block {
	margin-top: 12px;
}

.db-affiliate-partners-table {}

.db-affiliate-partners-t-heading {
	display: flex;
}

.db-affiliate-partners-t-h-col {
	width: 16.66%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-affiliate-partners-t-h-col--partner {
	width: 16.4%;
}

.db-affiliate-partners-t-h-col--date {
	width: 23.9%;
}

.db-affiliate-partners-t-h-col--games {
	width: 15.9%;
	text-align: right;
}

.db-affiliate-partners-t-h-col--lotteries {
	width: 15.9%;
	text-align: right;
}

.db-affiliate-partners-t-h-col--stacking {
	width: 15.9%;
	text-align: right;
}

.db-affiliate-partners-t-h-col--actions {
	width: 12%;
	text-align: right;
}

.db-affiliate-partners-t-items {}

.db-affiliate-partners-t-item {
	position: relative;
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-affiliate-partners-t-item:nth-child(odd) {
	background: #0e1b28;
}


.db-affiliate-partners-t-i-col {
	position: relative;
	z-index: 2;
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 16.66%;
}


.db-affiliate-partners-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-affiliate-partners-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-affiliate-partners-t-i-col__value-block {
	width: 100%;
}


.db-affiliate-partners-t-i-col--partner {
	width: 16.4%;
}

.db-affiliate-partners-t-i-col--date {
	width: 23.9%;
}

.db-affiliate-partners-t-i-col--games {
	width: 15.9%;
}

.db-affiliate-partners-t-i-col--lotteries {
	width: 15.9%;
}

.db-affiliate-partners-t-i-col--stacking {
	width: 15.9%;
}

.db-affiliate-partners-t-i-col--actions {
	width: 12%;
}


.db-affiliate-partners-t-i-partner {
	display: flex;
	align-items: center;
}

.db-affiliate-partners-t-i-partner__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-affiliate-partners-t-i-partner__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/user-circle.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/user-circle.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}


.db-affiliate-partners-t-i-partner__icon .image {
	width: 16px;
	height: 16px;
	position: relative;
	z-index: 2;
	border-radius: 16px;
}

.db-affiliate-partners-t-i-partner__nickname {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-affiliate-partners-t-i-games {
	text-align: right;
}

.db-affiliate-partners-t-i-games--white {
	color: #ebf4ff;
}

.db-affiliate-partners-t-i-lotteries {
	text-align: right;
}

.db-affiliate-partners-t-i-lotteries--white {
	color: #ebf4ff;
}

.db-affiliate-partners-t-i-stacking {
	text-align: right;
}

.db-affiliate-partners-t-i-stacking--green {
	color: #2ee57a;
}

.db-affiliate-partners-t-i-details-btn-block {
	display: flex;
	justify-content: flex-end;
}

.db-affiliate-partners-t-i-details-btn {
	padding: 0;
	width: 20px;
	height: 20px;
	position: relative;
	border: 0;
}

.db-affiliate-partners-t-i-details-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/eye.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-affiliate-partners-t-i-details-btn:hover::before {
	background-color: #0170EF;
}



.modal--partner {}

.modal--partner .modal-dialog {
	max-width: 855px;
}

.modal--partner .modal-content {
	padding: 32px;
}

.modal--partner .modal-title-icon {}

.m-partner-table-block {}


.m-partner-table-block .pagination {
	justify-content: center;
}

.m-partner-table {}

.m-partner-t-heading {
	display: flex;
}

.m-partner-t-h-col {
	width: 25%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.m-partner-t-h-col--currency {}

.m-partner-t-h-col--games {
	text-align: right;
}

.m-partner-t-h-col--lotteries {
	text-align: right;
}

.m-partner-t-h-col--stacking {
	text-align: right;
}

.m-partner-t-items {}

.m-partner-t-item {
	position: relative;
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.m-partner-t-item:nth-child(odd) {
	background: #0e1b28;
}

.m-partner-t-item.active::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	border-radius: 8px;
	height: 100%;
	width: 100%;
	border: 1px solid #17c964;
	pointer-events: none;
	z-index: 3;
}

.m-partner-t-item.active .m-partner-t-i-structure__icon::before {
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}


.m-partner-t-i-col {
	position: relative;
	z-index: 2;
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 25%;
}


.m-partner-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.m-partner-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.m-partner-t-i-col__value-block {
	width: 100%;
}

.m-partner-t-i-col--currency {}

.m-partner-t-i-col--games {}

.m-partner-t-i-col--lotteries {}

.m-partner-t-i-col--stacking {}

.m-partner-t-i-currency {
	color: #ebf4ff;
}

.m-partner-t-i-games {
	text-align: right;
}

.m-partner-t-i-games--white {
	color: #ebf4ff;
}

.m-partner-t-i-games--red {
	color: #e53535;
}

.m-partner-t-i-games--green {
	color: #2ee57a;
}

.m-partner-t-i-lotteries {
	text-align: right;
}

.m-partner-t-i-lotteries--white {
	color: #ebf4ff;
}

.m-partner-t-i-lotteries--red {
	color: #e53535;
}

.m-partner-t-i-lotteries--green {
	color: #2ee57a;
}

.m-partner-t-i-stacking {
	text-align: right;
}


.m-partner-t-i-stacking--white {
	color: #ebf4ff;
}

.m-partner-t-i-stacking--red {
	color: #e53535;
}

.m-partner-t-i-stacking--green {
	color: #2ee57a;
}


.m-partner-t-i-structure {
	display: flex;
	align-items: center;
}

.m-partner-t-i-structure__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.m-partner-t-i-structure__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/shooting-star.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/shooting-star.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.m-partner-t-i-structure__amount {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.m-partner-warning-block {
	margin-top: 12px;
}

.m-partner-warning {
	background-color: rgba(239, 141, 50, 0.1);
	border-left: 1px solid #ef8d32;
	border-radius: 0 8px 8px 0;
	padding: 12px 14px;
	display: flex;
	position: relative;

}

.m-partner-warning__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.m-partner-warning__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	mask-image: url('../images/svg/info.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/info.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #ef8d32;
	top: 0;
	left: 0;
	transition: 0.4s ease;
}

.m-partner-warning__text {
	width: calc(100% - 20px);
	padding-left: 10px;
	font-size: 14px;
	line-height: 1.25;
	color: #ef8d32;
}




.setting-block {}

.setting {
	display: flex;
}

.setting-left {
	width: 316px;
}

.setting-right {
	width: calc(100% - 316px);
	padding-left: 12px;
}

.setting-side-block {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
	height: 100%;
}

.setting-side {}

.setting-side h1 {
	color: #ebf4ff;
}

.setting-side-descr {
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}


.setting-tabs-block {
	border-radius: 8px;
	background: #172a3e;
}

.setting-tabs {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
}

.setting-tab-wrapper {
	padding-left: 8px;
	padding-right: 8px;
}

.setting-tab {
	padding: 13px 15px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.setting-tab__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
	transition: 0.4s ease;
	position: relative;
}

.setting-tab__icon {
	width: 24px;
	height: 24px;
	position: relative;
	margin-top: -2px;
	margin-bottom: -2px;
}

.setting-tab__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	transition: 0.4s ease;
}


.setting-tab__icon+.setting-tab__title {
	padding-left: 8px;
}

.setting-tab__count-block {
	padding-left: 8px;
}

.setting-tab__count {
	border-radius: 16px;
	min-width: 16px;
	width: 16px;
	height: 16px;
	box-shadow: 0 1px 4px 0 rgba(46, 229, 122, 0.25);
	background: #17c964;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 600;
	font-size: 10px;
	line-height: 1.25;
	color: #fff;
}



.setting-tab:hover,
.setting-tab.active {
	border-color: #1a314b;
	background: #14202d;
}

.setting-tab.active {
	pointer-events: none;
}

.setting-tab:hover .setting-tab__title,
.setting-tab.active .setting-tab__title {
	color: #ebf4ff;
}

.setting-tab:hover .setting-tab__icon::before,
.setting-tab.active .setting-tab__icon::before {
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}


.setting-tab--password {}

.setting-tab--password .setting-tab__icon::before {
	mask-image: url('../images/svg/lock.svg');
	-webkit-mask-image: url('../images/svg/lock.svg');
}

.setting-tab--password.active .setting-tab__icon::before {
	mask-image: url('../images/svg/lock-fill.svg');
	-webkit-mask-image: url('../images/svg/lock-fill.svg');
}

.setting-tab--2fa {}

.setting-tab--2fa .setting-tab__icon::before {
	mask-image: url('../images/svg/qr-code.svg');
	-webkit-mask-image: url('../images/svg/qr-code.svg');
}

.setting-tab--2fa.active .setting-tab__icon::before {
	mask-image: url('../images/svg/qr-code-fill.svg');
	-webkit-mask-image: url('../images/svg/qr-code-fill.svg');
}

.setting-tab--wallets {}

.setting-tab--wallets .setting-tab__icon::before {
	mask-image: url('../images/svg/wallet.svg');
	-webkit-mask-image: url('../images/svg/wallet.svg');
}

.setting-tab--wallets.active .setting-tab__icon::before {
	mask-image: url('../images/svg/wallet-fill.svg');
	-webkit-mask-image: url('../images/svg/wallet-fill.svg');
}

.setting-tab--autorisations {}

.setting-tab--autorisations .setting-tab__icon::before {
	mask-image: url('../images/svg/desktop-fill.svg');
	-webkit-mask-image: url('../images/svg/desktop-fill.svg');
}

.setting-tab--autorisations.active .setting-tab__icon::before {
	mask-image: url('../images/svg/desktop-fill.svg');
	-webkit-mask-image: url('../images/svg/desktop-fill.svg');
}

.setting-side-tabs-content-block {}

.setting-side-tabs-content {}

.setting-side-tab-content {
	padding-top: 16px;
	display: none;
}

.setting-side-tab-content:first-child {
	display: block;
}

/* .setting-side-tab-content:nth-child(4) {
	display: block;
} */


.setting-tabs-content-block {
	padding-top: 12px;
}

.setting-tabs-content {}

.setting-tab-content {
	display: none;
	border-radius: 8px;
}

.setting-tab-content:first-child {
	display: block;
}

.setting-tab-content:nth-child(3) {
	/* display: block; */
}


.setting-common-form-block {
	margin-top: 16px;
}

.setting-common-form {}

.upload-image-block {
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 12px;
}

.upload-image {}

.upload-image-panel {
	margin-top: 8px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.upload-image-panel-left {
	padding-right: 8px;
}

.upload-image-result {
	width: 40px;
	height: 40px;
	position: relative;
}

.upload-image-result::before {
	content: '';
	position: absolute;
	width: 40px;
	height: 40px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/user-circle.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 40px 40px;
	-webkit-mask-image: url('../images/svg/user-circle.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 40px 40px;
	background-color: #406084;
}


.upload-image-result .image {
	width: 40px;
	height: 40px;
	border-radius: 40px;
	position: relative;
	z-index: 2;
	object-fit: cover;
}

.upload-image-panel-right {}

.upload-image-field {}


.input-file {
	display: block;
	margin-bottom: 0;
}

.input-file input[type=file] {
	display: none;
}

.input-file-field {
	padding-top: 12px;
	padding-bottom: 12px;
	font-size: 16px;
}

.input-file-field__text {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.image[src=""] {
	opacity: 0;
	visibility: hidden;
}

.switch-checkbox-block {}

.switch-checkbox-panel {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.switch-checkbox-panel-left {}

.switch-checkbox-title-block {
	display: flex;
	align-items: center;
}

.switch-checkbox-title {
	font-weight: 500;
	font-size: 14px;
	line-height: 1;
	color: #406084;
	padding-right: 8px;
	max-width: calc(100% - 16px);
}

.switch-checkbox-info-block {
	position: relative;
	width: 16px;
	height: 16px;
}

.switch-checkbox-info {
	width: 16px;
	height: 16px;
	position: relative;
}

.switch-checkbox-info::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/info.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/info.svg');
	background-color: #406084;
	transition: 0.4s ease;
}




.switch-checkbox-panel-right {}

.switch-checkbox-label {
	display: block;
	margin: 0;
}

.switch-checkbox {
	width: 44px;
	height: 24px;
	border-radius: 12px;
	background-color: #1A314B;
	position: relative;
	cursor: pointer;
}

.switch-checkbox::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	border-radius: 12px;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	opacity: 0;
	visibility: hidden;
	pointer-events: none;
	transition: 0.4s ease;
}

.switch-checkbox__slider {
	position: absolute;
	z-index: 2;
	width: 20px;
	height: 20px;
	left: 2px;
	top: 2px;
	border-radius: 100%;
	background-color: #7998ba;
	box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.06), 0 1px 3px 0 rgba(16, 24, 40, 0.1);
	transition: 0.4s ease;
}


.checkbox:checked~.switch-checkbox::before {
	opacity: 1;
	visibility: visible;
}

.checkbox:checked~.switch-checkbox .switch-checkbox__slider {
	left: 22px;
	background-color: #fff;
	box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.06), 0 1px 3px 0 rgba(16, 24, 40, 0.1);
}



.change-password-form-block {
	padding: 12px;
	background-color: #14202d;
	border-radius: 8px;
	padding-top: 88px;
	padding-bottom: 88px;
}

.change-password-form-title {
	text-align: center;
	color: #ebf4ff;
}

.change-password-form-descr {
	margin-top: 1px;
	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #406084;
}

.change-password-form {
	margin-top: 24px;
	max-width: 330px;
	margin-right: auto;
	margin-left: auto;
}


.protected-info-block {
	margin-top: 16px;
}

.protected-info {
	background-color: #172a3e;
	border-left: 1px solid #047bdf;
	border-radius: 0 8px 8px 0;
	padding: 12px 14px;
	display: flex;
	position: relative;
}

.protected-info__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.protected-info__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	mask-image: url('../images/svg/shield-check.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/shield-check.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #047BDF;
	top: 0;
	left: 0;
	transition: 0.4s ease;
}

.protected-info__text {
	width: calc(100% - 20px);
	padding-left: 10px;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.protected-info__text a {
	color: #0681d5;
	text-decoration: none;
}

.protected-info__text a:hover {
	color: #0681d5;
	text-decoration: underline;
}



.two-fa-form-block {
	padding: 12px;
	background-color: #14202d;
	border-radius: 8px;
	padding-top: 88px;
	padding-bottom: 88px;
}

.two-fa-form-title {
	text-align: center;
	color: #ebf4ff;
}

.two-fa-form-descr {
	margin-top: 1px;
	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #406084;
}

.two-fa-form {
	margin-top: 24px;
	max-width: 330px;
	margin-right: auto;
	margin-left: auto;
}

.two-fa-download-block {
	margin-top: 10px;
	max-width: 330px;
	margin-right: auto;
	margin-left: auto;
}

.download-app-items {
	display: flex;
	justify-content: center;
	margin-left: -4px;
	margin-right: -4px;
}

.download-app-item-wrapper {
	padding-left: 4px;
	padding-right: 4px;
}

.download-app-item {}

.two-fa-download-note {
	margin-top: 8px;
	font-weight: 500;
	font-size: 12px;
	line-height: 1.25;
	text-align: center;
	color: #406084;
}

.two-fa-qr-block {
	margin-top: 12px;
	padding-top: 12px;
	padding-bottom: 12px;
	position: relative;
	max-width: 330px;
	margin-right: auto;
	margin-left: auto;
}

.two-fa-qr-image {
	width: 140px;
	height: 140px;
	margin-right: auto;
	margin-left: auto;
}

.two-fa-qr-scaner {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 0;
	overflow: hidden;
	border-top: 1px solid #0170EF;
	background: linear-gradient(0deg, rgba(1, 112, 239, 0) 0%, #0170ef 100%);
	opacity: 0.2;
	animation: twoFaQrScanerAnim 4s linear infinite;
}

@keyframes twoFaQrScanerAnim {
	0% {
		height: 0;
	}

	50% {
		height: 100%;
	}

	100% {
		height: 0;
	}
}


.two-fa-form .confirmation-code-fields-block {
	padding-top: 0;
	padding-bottom: 0;
	margin-top: 12px;
}

.field-bottom-copy-link-block {
	margin-top: 4px;
	display: flex;
}

.copy-link--field {}

.copy-link--field .copy-link__icon::before {
	background-color: #1870d5;
}

.copy-link--field .copy-link__text {
	font-size: 14px;
	line-height: 1;
	padding-left: 4px;
	padding-right: 0;
	color: #1870d5;
}





.db-wallets-block {
	background-color: #14202d;
	border-radius: 8px;
}

.db-wallets-top-block {}

.db-wallets-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 12px;
}

.db-wallets-top-left {
	padding-right: 8px;
}

.db-wallets-top-content {}

.db-wallets-top-title {
	color: #ebf4ff;
}

.db-wallets-top-descr {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.db-wallets-top-right {}


.db-wallets-table-block {
	padding-left: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
}

.db-wallets-table {}

.db-wallets-t-heading {
	display: flex;
}

.db-wallets-t-h-col {
	width: calc(25% - 22px);
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-wallets-t-h-col--payment {}

.db-wallets-t-h-col--number {}

.db-wallets-t-h-col--destination-tag {}

.db-wallets-t-h-col--date {}

.db-wallets-t-h-col--actions {
	text-align: right;
	width: 88px;
}

.db-wallets-t-h-col-title-block {
	display: flex;
	align-items: center;
}

.db-wallets-t-h-col-title {}


.db-wallets-t-items {}

.db-wallets-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-wallets-t-item:nth-child(odd) {
	background: #0e1b28;
}

.db-wallets-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: calc(25% - 22px);
}


.db-wallets-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-wallets-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-wallets-t-i-col__value-block {
	width: 100%;
}


.db-wallets-t-i-col--payment {}

.db-wallets-t-i-col--number {}

.db-wallets-t-i-col--destination-tag {}

.db-wallets-t-i-col--date {}

.db-wallets-t-i-col--actions {
	text-align: right;
	width: 88px;
}


.db-wallets-t-i-payment {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.db-wallets-t-i-payment__title {

	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-wallets-t-i-payment__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-wallets-t-i-currency {}

.db-wallets-t-i-number {
	display: flex;
	align-items: center;
}

.db-wallets-t-i-number__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-wallets-t-i-number__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/wallet.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/wallet.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-wallets-t-i-number__balance {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}


.db-wallets-t-i-actions-block {}

.db-wallets-t-i-actions {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-left: -4px;
	margin-right: -4px;
}

.db-wallets-t-i-action-wrapper {
	padding-left: 4px;
	padding-right: 4px;
}

.db-wallets-t-i-action-edit-btn {
	width: 20px;
	height: 20px;
	position: relative;
	display: block;
	border: 0;
	padding: 0;
}

.db-wallets-t-i-action-edit-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/pencil-simple.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/pencil-simple.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-wallets-t-i-action-edit-btn:hover::before {
	background-color: #ebf4ff;
}

.db-wallets-t-i-action-remove-btn {
	width: 20px;
	height: 20px;
	display: block;
	position: relative;
	border: 0;
	padding: 0;
}

.db-wallets-t-i-action-remove-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/trash-simple.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/trash-simple.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-wallets-t-i-action-remove-btn:hover::before {
	background-color: #ebf4ff;
}

.setting-add-wallet-btn-block {
	margin-top: 16px;
}

.setting-add-wallet-btn {
	width: 100%;
}




.modal--add-wallet {}

.add-wallet-form-block {}

.add-wallet-form {}

.modal--edit-wallet {}

.edit-wallet-form-block {}

.edit-wallet-form {}

.edit-wallet-form .form-button-block .green-gr-btn {
	width: 100%;
}

.edit-wallet-remove-button-block {
	margin-top: 12px;
}

.edit-wallet-remove-btn {
	position: relative;
	width: 100%;
	height: 48px;
}

.edit-wallet-remove-btn::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/trash-simple.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/trash-simple.svg');
	background-color: #EBF4FF;
	transition: 0.4s ease;
}

.db-wallets-toggle-col-visibility-block {
	padding-left: 8px;
}


.db-wallets-toggle-number-visibility {
	display: block;
	width: 16px;
	height: 16px;
	position: relative;
	padding: 0;
	border: 0;
}


.db-wallets-toggle-number-visibility::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.db-wallets-toggle-number-visibility.active::before {
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-image: url('../images/svg/eye.svg');
}


.db-wallets-toggle-number-visibility:hover::before {
	background-color: #17C964;
}


.db-wallets-toggle-tag-visibility {
	display: block;
	width: 16px;
	height: 16px;
	position: relative;
	padding: 0;
	border: 0;
}


.db-wallets-toggle-tag-visibility::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.db-wallets-toggle-tag-visibility.active::before {
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-image: url('../images/svg/eye.svg');
}


.db-wallets-toggle-tag-visibility:hover::before {
	background-color: #17C964;
}


.setting-close-sessions-btn-block {
	margin-top: 16px;
}

.setting-close-sessions-btn {
	width: 100%;
}



.db-autorisations-block {
	background-color: #14202d;
	border-radius: 8px;
}

.db-autorisations-top-block {}

.db-autorisations-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 12px;
}

.db-autorisations-top-left {
	padding-right: 8px;
}

.db-autorisations-top-content {}

.db-autorisations-top-title {
	color: #ebf4ff;
}

.db-autorisations-top-descr {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.db-autorisations-top-right {}


.db-autorisations-table-block {
	padding-left: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
}

.db-autorisations-table {}

.db-autorisations-t-heading {
	display: flex;
}

.db-autorisations-t-h-col {
	width: calc(33.33% - 29px);
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-autorisations-t-h-col--device {}

.db-autorisations-t-h-col--browser {}


.db-autorisations-t-h-col--date {}


.db-autorisations-t-h-col--actions {
	text-align: right;
	width: 87px;
}

.db-autorisations-t-h-col-title-block {
	display: flex;
	align-items: center;
}

.db-autorisations-t-h-col-title {}


.db-autorisations-t-items {}

.db-autorisations-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-autorisations-t-item:nth-child(odd) {
	background: #0e1b28;
}

.db-autorisations-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: calc(33.33% - 29px);
}


.db-autorisations-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-autorisations-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-autorisations-t-i-col__value-block {
	width: 100%;
}


.db-autorisations-t-i-col--device {}

.db-autorisations-t-i-col--browser {}


.db-autorisations-t-i-col--date {}

.db-autorisations-t-i-col--actions {
	text-align: right;
	width: 87px;
}


.db-autorisations-t-i-device {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.db-autorisations-t-i-device__title {

	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-autorisations-t-i-device__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-autorisations-t-i-browser {
	display: flex;
	align-items: center;
}

.db-autorisations-t-i-browser__icon {
	width: 16px;
	height: 16px;
	position: relative;
}


.db-autorisations-t-i-browser__title {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}


.db-autorisations-t-i-actions-block {}

.db-autorisations-t-i-actions {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-left: -4px;
	margin-right: -4px;
}

.db-autorisations-t-i-action-wrapper {
	padding-left: 4px;
	padding-right: 4px;
}

.db-autorisations-t-i-action-remove-btn {
	width: 20px;
	height: 20px;
	display: block;
	position: relative;
	border: 0;
	padding: 0;
}

.db-autorisations-t-i-action-remove-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/trash-simple.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/trash-simple.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-autorisations-t-i-action-remove-btn:hover::before {
	background-color: #ebf4ff;
}


.section-front-top {
	padding-top: 16px;
}

.front-top-block {
	border-radius: 8px;
	padding: 12px;
	background: #14202d;
}

.front-top-slider-block {}


.front-top-slider-block {
	position: relative;
}

.front-top-slider {
	border-radius: 8px;
	position: relative;
}

.front-top-slider .swiper-button-next,
.front-top-slider .swiper-button-prev {
	position: absolute;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	top: 50%;
	transform: translateY(-50%);
	margin-top: 0;
	bottom: auto;
	background: rgba(255, 255, 255, 0.1);
	z-index: 5;
}

.front-top-slider .swiper-button-next::after,
.front-top-slider .swiper-button-prev::after {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.front-top-slider .swiper-button-next {
	right: 30px;
}

.front-top-slider .swiper-button-prev {
	left: 30px;
}


.front-top-slider .swiper-button-next::after {
	mask-image: url('../images/svg/caret-right.svg');
	-webkit-mask-image: url('../images/svg/caret-right.svg');
}

.front-top-slider .swiper-button-prev::after {
	mask-image: url('../images/svg/caret-left.svg');
	-webkit-mask-image: url('../images/svg/caret-left.svg');
}

.front-top-slide {
	height: auto;
}

.front-top-slide-inner {
	height: 100%;
}

.front-top-slide-item {
	height: 100%;
	min-height: 420px;
	display: flex;
	align-items: center;
	position: relative;
	overflow: hidden;
	padding: 32px 84px;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	border-radius: 8px;
}

.front-top-slide-item-content {
	max-width: 525px;
	position: relative;
	z-index: 5;
}

.front-top-slide-item-label-block {
	display: flex;
}

.front-top-slide-item-label {
	border-radius: 4px;
	padding: 2px 4px 2px 4px;
	font-weight: 500;
	font-size: 12px;
	line-height: 1;
	color: #ebf4ff;
	background: #0170ef;
}

.front-top-slide-item-title-block {
	margin-top: 16px;
}

.front-top-slide-item-title {
	color: #ebf4ff;
}

.front-top-slide-item-descr {
	margin-top: 16px;
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #ebf4ff;
}

.front-top-slide-item-btn-block {
	margin-top: 16px;
	display: flex;
}

.front-top-slide-item-btn {
	width: auto;
}




.front-top-slide-item-count-block {
	margin-top: 48px;
}

.front-top-slide-item-count {
	font-size: 12px;
	line-height: 1.45;
	color: #ebf4ff;
	vertical-align: middle;
}

.front-top-slide-item-count-current {
	font-size: 20px;
}

.front-top-slide-item-count-block--mobile {
	margin-top: 0;
	display: none;
}

.front-top-slide-item-image-block {
	/* position: absolute;
	right: 74px;
	bottom: -104px;
	width: 440px;
	height: 440px; */
}

.front-top-slide-item-image-block {
	position: absolute;
	width: 636px;
	height: 100%;
	right: 0;
	top: 0;
}
.front-top-slide-item-image-block::before{
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-image: url('../images/theme/front-top-slide-item-image-block-bg.png');
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
	mix-blend-mode: soft-light;
}
.front-top-slide-item-image {
	width: 386px;
	height: 330px;
	position: absolute;
	z-index: 2;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.front-top-slide-item-image-diamond {
	position: absolute;
	top: 17px;
	left: 17px;
	width: 73px;
	height: 73px;
}

.front-top-features-block {
	margin-top: 20px;
}

.front-top-features {
	display: flex;
	flex-wrap: wrap;
	margin-left: -12px;
	margin-right: -12px;
	margin-top: -24px;
}

.front-top-feature-wrapper {
	width: 25%;
	padding-left: 12px;
	padding-right: 12px;
	margin-top: 24px;
}

.front-top-feature {
	padding: 16px;
	border-radius: 8px;
	background: #172a3e;
	display: flex;
	align-items: center;
	position: relative;
	text-decoration: none;
	color: #ebf4ff;
	height: 100%;
}


.front-top-feature:hover::after {
	opacity: 1;
	visibility: visible;
}

.front-top-feature:hover .front-top-feature__arrow {
	opacity: 1;
	visibility: visible;
}

.front-top-feature::after {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-image: url('../images/theme/front-top-feature-border.png');
	background-position: center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	pointer-events: none;
	opacity: 0;
	visibility: hidden;
	transition: 0.4s ease;
}

.front-top-feature__icon {
	width: 48px;
	height: 48px;
	position: relative;
	z-index: 2;
}

.front-top-feature__content {
	width: calc(100% - 48px);
	padding-left: 8px;
	padding-right: 30px;
	position: relative;
	z-index: 2;
}

.front-top-feature__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.35;
	color: #ebf4ff;
}

.front-top-feature__arrow {
	width: 24px;
	height: 24px;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	right: 16px;
	transition: 0.4s ease;
	opacity: 0;
	visibility: hidden;
}

.front-top-feature__arrow::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/arrow-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/arrow-right.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.front-top-feature__soon-label-block {
	position: absolute;
	right: 16px;
	top: 16px;
}

.front-top-feature__soon-label {
	background: rgba(1, 112, 239, 0.2);
	border-radius: 4px;
	padding: 0px 2px 1px 2px;
	font-weight: 500;
	font-size: 11px;
	line-height: 109%;
	letter-spacing: -0.04em;
	color: #0170ef;
}

.section-front-popular-games {
	padding-top: 32px;
}

.popular-games-title-block {}

.popular-games-title {
	display: flex;
	align-items: center;
}

.popular-games-title__text {
	font-size: 22px;
	color: #ebf4ff;
	padding-right: 4px;
}

.popular-games-title__icon {
	width: 22px;
}

.section-front-popular-games .games-list-items-block {
	margin-top: 12px;
}


.section-front-active-lotteries {
	padding-top: 36px;
}

.s-front-section-top-block {}

.s-front-section-top {
	padding-top: 5px;
	padding-bottom: 5px;
	padding-right: 100px;
}

.s-front-section-top-left {
	display: flex;
	align-items: center;
}

.s-front-section-top-title {
	color: #ebf4ff;
	padding-right: 16px;
}

.s-front-section-top-btn-block {}

.s-front-section-top-btn {
	padding-top: 8px;
	padding-bottom: 8px;
	font-size: 16px;
}

.section-front-staking {
	padding-top: 32px;
}

.front-staking-block {
	height: 100%;
}

.front-staking {
	height: 100%;
	position: relative;
	overflow: hidden;
	border-radius: 8px;
	padding: 64px;
	background: linear-gradient(239deg, #17c964 0%, #0170ef 97.68%);
	color: #ebf4ff;
}

.front-staking__content {
	max-width: 635px;
	position: relative;
	z-index: 2;
}

.front-staking__title {}

.front-staking__descr {
	font-size: 16px;
	line-height: 1.45;
	color: #ebf4ff;
	margin-top: 16px;
}

.front-staking__btn-block {
	margin-top: 24px;
	display: flex;
}

.front-staking__btn {
	padding: 15px 32px;
	display: flex;
	position: relative;
	box-shadow: 0 12px 8px -2px rgba(0, 0, 0, 0.05), 0 3px 10px -3px rgba(255, 255, 255, 0.36);
}

.front-staking__btn__text {}

.front-staking__btn__image {
	position: absolute;
	right: -15px;
	top: 50%;
	transform: translateY(-50%);
	width: 28px;
}

.front-staking__image {
	position: absolute;
	width: 520px;
	right: -60px;
	bottom: -276px;
}

.join-telegram-block {
	height: 100%;
}

.join-telegram {
	display: flex;
	flex-direction: column;
	justify-content: center;
	height: 100%;
	border: 1px solid #25a3e1;
	border-radius: 8px;
	padding: 40px 24px;
	background: #172a3e;
	box-shadow: 0 1px 13px 0 rgba(36, 161, 221, 0.61);

}

.join-telegram-image {
	width: 87px;
	margin-right: auto;
	margin-left: auto;
}

.join-telegram-title-block {
	display: flex;
	justify-content: center;
	margin-top: 12px;
}

.join-telegram-title-block:first-child {
	margin-top: 0;
}

.join-telegram-title {
	font-weight: 500;
	font-size: 18px;
	line-height: 1.45;
	text-align: center;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.join-telegram-descr {
	margin-top: 8px;
	font-size: 16px;
	line-height: 1.25;
	text-align: center;
	color: #406084;
}

.join-telegram-btn-block {
	margin-top: 16px;
	display: flex;
	justify-content: center;
}

.join-telegram-btn {
	display: inline-flex;
	align-items: center;
	line-height: 1;
	color: #ebf4ff;
	font-weight: 500;
	font-size: 16px;
	border: 0;
	text-align: center;
	padding: 13px 26px;
	border-radius: 8px;
	text-decoration: none;
	cursor: pointer;
	border-radius: 10px;
	background-color: #2aabee;
	background: linear-gradient(180deg, #2aabee 0%, #229ed9 100%);
}


.join-telegram-btn:hover {
	color: #EBF4FF;
	text-decoration: none;
	background-color: #2aabee;
}

.join-telegram-btn__icon {}

.join-telegram-btn__text {
	padding-left: 10px;
}


.section-promotions-slider {
	padding-top: 32px;
}


.promotion-slider-block {
	position: relative;
	margin-top: 12px;
}

.promotion-slider {
	position: relative;
	margin-left: -16px;
	margin-right: -16px;
}

.promotion-slider .swiper-button-next,
.promotion-slider .swiper-button-prev {
	position: absolute;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	top: -56px;
	margin-top: 0;
	bottom: auto;
	background: #172a3e;
}

.promotion-slider .swiper-button-next::after,
.promotion-slider .swiper-button-prev::after {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.promotion-slider .swiper-button-next {
	right: 16px;
}

.promotion-slider .swiper-button-prev {
	right: 68px;
}


.promotion-slider .swiper-button-next::after {
	mask-image: url('../images/svg/caret-right.svg');
	-webkit-mask-image: url('../images/svg/caret-right.svg');
}

.promotion-slider .swiper-button-prev::after {
	mask-image: url('../images/svg/caret-left.svg');
	-webkit-mask-image: url('../images/svg/caret-left.svg');
}

.promotion-slide {
	padding-left: 16px;
	padding-right: 16px;
	height: auto;
}


.promotion-item {
	border-radius: 8px;
	background-color: #14202d;
	max-width: 800px;
	margin-right: auto;
	margin-left: auto;
	position: relative;
	height: 100%;
}

.promotion-item__image {
	position: relative;
	display: block;
	background: #172a3e;
	border-radius: 8px;
	height: 200px;
}

.promotion-item__image::before {
	content: '';
	position: absolute;
	width: 64px;
	height: 64px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 64px 64px;
	mask-image: url('../images/svg/image.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 64px 64px;
	-webkit-mask-image: url('../images/svg/image.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.promotion-item__image .image {
	position: relative;
	z-index: 2;
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 8px;
}

.promotion-item__content {
	margin-top: 16px;
	padding: 24px;
	padding-bottom: 68px;
	padding-top: 12px;
	position: relative;
	height: calc(100% - 216px);
}

.promotion-item__title-block {
	display: flex;
}

.promotion-item__title {
	font-weight: 500;
	font-size: 22px;
	line-height: 1.45;
	color: #ebf4ff;
	text-align: left;
	text-decoration: none;
	padding: 0;
	margin: 0;
	border: 0;
	background-color: transparent;
}

.promotion-item__title:hover {
	color: #ebf4ff;
}

.promotion-item__descr {
	margin-top: 4px;
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.promotion-item__bottom-block {
	margin-top: 12px;
	position: absolute;
	bottom: 24px;
	left: 24px;
	right: 24px;
}

.promotion-item__bottom {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;
}

.promotion-item__bottom-left {
	padding-right: 4px;
	padding-top: 4px;
	padding-bottom: 4px;
}

.promotion-item__tags-block {}

.promotion-item__tags {
	margin-top: -8px;
	margin-left: -4px;
	margin-right: -4px;
	display: flex;
	flex-wrap: wrap;
}

.promotion-item__tag-wrapper {
	margin-top: 8px;
	padding-left: 4px;
	padding-right: 4px;
}

.promotion-item__tag {
	border-radius: 4px;
	padding: 2px 4px 3px 4px;
	background: rgba(1, 112, 239, 0.2);
	font-weight: 500;
	font-size: 12px;
	line-height: 1;
	color: #0170ef;
}

.promotion-item__bottom-right {
	padding-top: 4px;
	padding-bottom: 4px;
}

.promotion-item__like-block {}

.promotion-item__like {
	padding: 0;
	background-color: transparent;
	border: 0;
	display: flex;
	align-items: center;
}

.promotion-item__like.active .promotion-item__like__count {
	color: #ebf4ff;
}

.promotion-item__like__count {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
	padding-right: 4px;
}

.promotion-item__like__icon {
	position: relative;
	width: 16px;
	height: 16px;
}

.promotion-item__like__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/heart.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/heart.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}


.promotion-item__like.liked .promotion-item__like__icon::before {
	mask-image: url('../images/svg/heart-fill.svg');
	-webkit-mask-image: url('../images/svg/heart-fill.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}



.section-news-slider {
	padding-top: 32px;
}


.news-slider-block {
	position: relative;
	margin-top: 12px;
}

.news-slider {
	position: relative;
	margin-left: -16px;
	margin-right: -16px;
}

.news-slider .swiper-button-next,
.news-slider .swiper-button-prev {
	position: absolute;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	top: -56px;
	margin-top: 0;
	bottom: auto;
	background: #172a3e;
}

.news-slider .swiper-button-next::after,
.news-slider .swiper-button-prev::after {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.news-slider .swiper-button-next {
	right: 16px;
}

.news-slider .swiper-button-prev {
	right: 68px;
}


.news-slider .swiper-button-next::after {
	mask-image: url('../images/svg/caret-right.svg');
	-webkit-mask-image: url('../images/svg/caret-right.svg');
}

.news-slider .swiper-button-prev::after {
	mask-image: url('../images/svg/caret-left.svg');
	-webkit-mask-image: url('../images/svg/caret-left.svg');
}

.news-slide {
	padding-left: 16px;
	padding-right: 16px;
	height: auto;
}


.news-item {
	position: relative;
	border-radius: 8px;
	background-color: #14202d;
	max-width: 600px;
	margin-right: auto;
	margin-left: auto;
	height: 100%;
}

.news-item__image {
	position: relative;
	display: block;
	background: #172a3e;
	border-radius: 8px;
	height: 200px;
}

.news-item__image::before {
	content: '';
	position: absolute;
	width: 64px;
	height: 64px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 64px 64px;
	mask-image: url('../images/svg/image.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 64px 64px;
	-webkit-mask-image: url('../images/svg/image.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.news-item__image .image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	position: relative;
	z-index: 2;
	border-radius: 8px;
}

.news-item__content {
	margin-top: 16px;
	padding: 24px;
	padding-top: 12px;
	padding-bottom: 76px;
	position: relative;
}

.news-item__date-block {}

.news-item__date {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
	padding-left: 12px;
	position: relative;
}

.news-item__date::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	border-radius: 8px;
	width: 8px;
	height: 8px;
	box-shadow: 0 1px 4px 0 rgba(46, 229, 122, 0.25);
	background: #2ee57a;
}


.news-item__title-block {
	margin-top: 12px;
}

.news-item__title {
	font-weight: 500;
	font-size: 22px;
	line-height: 1.45;
	color: #ebf4ff;
	text-decoration: none;
}

.news-item__title:hover {
	color: #ebf4ff;
}

.news-item__bottom-block {
	margin-top: 12px;
	position: absolute;
	bottom: 24px;
	left: 24px;
	right: 24px;
}

.news-item__bottom {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.news-item__bottom-left {
	padding-right: 4px;
}

.news-item__read-more-btn-block {}

.news-item__read-more-btn {
	font-size: 16px;
	padding-top: 11px;
	padding-bottom: 11px;
}

.news-item__bottom-right {}

.news-item__read-time-block {}

.news-item__read-time {
	font-size: 14px;
	line-height: 1.25;
	text-align: right;
	color: #7998ba
}

.section-start-earning {
	padding-top: 32px;
}

.start-earning-block {}

.start-earning {
	position: relative;
	border-radius: 8px;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.start-earning-content {
	padding: 64px;
	position: relative;
	z-index: 2;
	text-align: center;
	color: #ebf4ff;
}

.start-earning-title-block {}

.start-earning-title {}

.start-earning-descr {
	margin-top: 16px;
	max-width: 635px;

	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #ebf4ff;
	margin-right: auto;
	margin-left: auto;
	text-align: center;
}

.start-earning-btn-block {
	margin-top: 24px;
	display: flex;
	justify-content: center;
}

.start-earning-btn {
	padding: 14px 32px;
	display: flex;
	position: relative;
	box-shadow: 0 12px 8px -2px rgba(0, 0, 0, 0.05), 0 3px 10px -3px rgba(255, 255, 255, 0.36);
}

.start-earning-btn__text {}

.start-earning-btn__image {
	position: absolute;
	right: -15px;
	top: 50%;
	transform: translateY(-50%);
	width: 28px;
}



.start-earning-images {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	border-radius: 8px;
	overflow: hidden;
}

.start-earning-image-left {
	position: absolute;
	left: 18px;
	top: -175px;
	width: 272px;
	mix-blend-mode: color-burn;
}

.start-earning-image-center {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 254px;
}

.start-earning-image-right {
	position: absolute;
	right: -175px;
	top: 4px;
	width: 565px;
	mix-blend-mode: luminosity;
}

footer {
	padding-top: 32px;
}

.footer-content {
	padding-top: 48px;
	padding-bottom: 48px;
}

.footer-content-left {}

.footer-content-right {}

.footer-content-right .footer-copy {
	display: none;
	margin-top: 20px;
}

.footer-menu-blocks {
	margin-left: -12px;
	margin-right: -12px;
	display: flex;
	flex-wrap: wrap;
}

.footer-menu-block {
	padding-left: 12px;
	padding-right: 12px;
	width: 20%;
}

.footer-menu-block-inner {}

.footer-menu-block-title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.45;
	color: #ebf4ff;
}

.footer-menu {}

.footer-menu>ul {
	margin: 0;
	padding: 0;
	list-style-type: none;
}

.footer-menu-item {
	margin-top: 12px;
	display: flex;
}

.footer-menu-link {
	font-weight: 400;
	font-size: 14px;
	line-height: 1.45;
	color: #7998ba;
	text-decoration: none;
}

.footer-menu-link:hover {
	color: #7998ba;
}

.footer-menu-link--soon {
	display: flex;
	align-items: center;
	pointer-events: none;
}

.footer-menu-link--soon .footer-menu-link__text {
	padding-right: 8px;
	color: rgba(121, 152, 186, 0.5);
}

.footer-menu-link__soon {
	border-radius: 4px;
	padding: 0px 2px 1px 2px;
	background: rgba(1, 112, 239, 0.2);
	font-weight: 500;
	font-size: 11px;
	line-height: 109%;
	letter-spacing: -0.04em;
	color: #0170ef;
}


.footer-menu-block--product {}

.footer-menu-block--support {}

.footer-menu-block--terms {}

.footer-menu-block--contact {
	width: 40%;
}

.footer-contact-items-block {
	margin-top: 24px;
	max-width: 295px;
}

.footer-contact-items {}

.footer-contact-item {
	display: flex;
	margin-top: 12px;
}

.footer-contact-item:first-child {
	margin-top: 0;
}

.footer-contact-email-link {
	display: flex;
	align-items: center;
	text-decoration: none;
}

.footer-contact-email-link__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.footer-contact-email-link__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/envelope.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/envelope.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.footer-contact-email-link__text {
	width: calc(100% - 24px);
	padding-left: 8px;
	font-size: 14px;
	line-height: 1.45;
	color: #7998ba;
}

.footer-contact-phone-link {
	display: flex;
	align-items: center;
	text-decoration: none;
}

.footer-contact-phone-link__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.footer-contact-phone-link__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/phone.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/phone.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.footer-contact-phone-link__text {
	width: calc(100% - 24px);
	padding-left: 8px;
	font-size: 14px;
	line-height: 1.45;
	color: #7998ba;
}

.footer-contact-address {
	display: flex;
	align-items: flex-start;
}

.footer-contact-address__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.footer-contact-address__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/house-simple.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/house-simple.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.footer-contact-address__text {
	width: calc(100% - 24px);
	padding-left: 8px;
	font-size: 14px;
	line-height: 1.45;
	color: #7998ba;
}

.footer-copy {
	margin-top: 100px;
	font-size: 14px;
	line-height: 1.45;
	color: #7998ba;
}

.section-about-us {
	padding-top: 72px;
}

.about-us-top-block {}

.about-us-top-block h1 {
	font-weight: 500;
	color: #fff;
}

.about-us-top-descr {
	margin-top: 16px;
	font-size: 20px;
	line-height: 1.45;
	color: #7998ba;
	max-width: 680px;
}

.about-us-content-block {}

.about-us-vision-block {
	margin-top: 24px;
	height: calc(100% - 24px);
}

.about-us-vision {
	position: relative;
	background: #0170ef;
	padding: 32px;
	padding-bottom: 148px;
	border-radius: 8px;
	color: #ebf4ff;
	height: 100%;
}

.about-us-vision__title {}

.about-us-vision__descr {
	margin-top: 32px;
	font-size: 20px;
	line-height: 1.45;
	color: #ebf4ff;
}

.about-us-vision__btn-block {
	position: absolute;
	left: 32px;
	bottom: 68px;
	right: 32px;
	display: flex;
}

.about-us-vision__btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding-top: 12px;
	padding-bottom: 12px;
}

.about-us-vision__btn__text {
	font-weight: 500;
	font-size: 18px;
	line-height: 1.35;
	color: #ebf4ff;
	padding-right: 12px;
}

.about-us-vision__btn__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.about-us-vision__btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/arrow-circle-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/arrow-circle-right.svg');
	background-color: #EBF4FF;
	transition: 0.4s ease;
}



.about-us-values-block {
	margin-top: 24px;
	height: calc(100% - 24px);
}

.about-us-values {
	height: 100%;
	background: #14202d;
	padding: 32px;
	border-radius: 8px;
}

.about-us-values__title {
	color: #ebf4ff;
}

.about-us-values__descr {
	margin-top: 24px;
	font-size: 20px;
	line-height: 1.45;
	color: #7998ba;

}

.about-us-value-items-block {
	margin-top: 24px;
}

.about-us-value-items {}

.about-us-value-item {
	margin-top: 16px;
}

.about-us-value-item:first-child {
	margin-top: 0;
}

.about-us-value-item-title-block {}

.about-us-value-item-title {
	position: relative;
	padding-left: 24px;
	font-weight: 600;
	font-size: 20px;
	line-height: 1.35;
	color: #ebf4ff;
}


.about-us-value-item-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	border-radius: 16px;
	width: 16px;
	height: 16px;
	box-shadow: 0 1px 4px 0 rgba(46, 229, 122, 0.25);
	background: #2ee57a;
}

.about-us-value-item-descr {
	margin-top: 12px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}


.section-about-offer {
	padding-top: 24px;
}

.section-about-offer .h1 {
	color: #ebf4ff;
}

.section-descr {
	margin-top: 24px;
	font-size: 20px;
	line-height: 1.45;
	color: #7998ba;
}

.about-offer-items-block {
	margin-top: 24px;
}

.about-offer-items {
	display: flex;
	flex-wrap: wrap;
	margin-left: -12px;
	margin-right: -12px;
	margin-top: -24px;
}

.about-offer-item-wrapper {
	width: 33.33%;
	padding-left: 12px;
	padding-right: 12px;
	margin-top: 24px;
}


.about-offer-item {
	height: 100%;
	display: block;
	padding: 24px;
	border-radius: 8px;
	background: #172a3e;
	position: relative;
	text-decoration: none;
	color: #ebf4ff;
}


.about-offer-item:hover::after {
	opacity: 1;
	visibility: visible;
}

.about-offer-item:hover .about-offer-item__arrow {
	opacity: 1;
	visibility: visible;
}

.about-offer-item::after {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-image: url('../images/theme/about-offer-item-border.png');
	background-position: center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	pointer-events: none;
	opacity: 0;
	visibility: hidden;
	transition: 0.4s ease;
}


.about-offer-item__content {
	position: relative;
	z-index: 2;
}

.about-offer-item__icon {
	width: 24px;
	height: 24px;
	position: relative;
	z-index: 2;
}

.about-offer-item__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.about-offer-item__title {
	margin-top: 8px;
	font-weight: 500;
	font-size: 22px;
	line-height: 1.45;
	color: #ebf4ff;
}

.about-offer-item__descr {
	margin-top: 8px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.about-offer-item__arrow {
	width: 24px;
	height: 24px;
	position: absolute;
	bottom: 20px;
	right: 16px;
	transition: 0.4s ease;
	opacity: 0;
	visibility: hidden;
}

.about-offer-item__arrow::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/arrow-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/arrow-right.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.about-offer-item__soon-label-block {
	position: absolute;
	right: 24px;
	top: 24px;
}

.about-offer-item__soon-label {
	background: rgba(1, 112, 239, 0.2);
	border-radius: 4px;
	padding: 0px 2px 1px 2px;
	font-weight: 500;
	font-size: 11px;
	line-height: 109%;
	letter-spacing: -0.04em;
	color: #0170ef;
}



.about-offer-item--games {}

.about-offer-item--games .about-offer-item__icon::before {
	mask-image: url('../images/svg/front/about-offer-item-icon--dice-five.svg');
	-webkit-mask-image: url('../images/svg/front/about-offer-item-icon--dice-five.svg');
}

.about-offer-item--lottery {}

.about-offer-item--lottery .about-offer-item__icon::before {
	mask-image: url('../images/svg/front/about-offer-item-icon--ticket.svg');
	-webkit-mask-image: url('../images/svg/front/about-offer-item-icon--ticket.svg');
}

.about-offer-item--staking {}

.about-offer-item--staking .about-offer-item__icon::before {
	mask-image: url('../images/svg/front/about-offer-item-icon--tip-jar.svg');
	-webkit-mask-image: url('../images/svg/front/about-offer-item-icon--tip-jar.svg');
}

.about-offer-item--affiliate {}

.about-offer-item--affiliate .about-offer-item__icon::before {
	mask-image: url('../images/svg/front/about-offer-item-icon--users-four.svg');
	-webkit-mask-image: url('../images/svg/front/about-offer-item-icon--users-four.svg');
}

.about-offer-item--city {}

.about-offer-item--city .about-offer-item__icon::before {
	mask-image: url('../images/svg/front/about-offer-item-icon--lightning.svg');
	-webkit-mask-image: url('../images/svg/front/about-offer-item-icon--lightning.svg');
}

.about-offer-item--taps {}

.about-offer-item--taps .about-offer-item__icon::before {
	mask-image: url('../images/svg/front/about-offer-item-icon--hand-tap.svg');
	-webkit-mask-image: url('../images/svg/front/about-offer-item-icon--hand-tap.svg');
}

.about-offer-item--soon {
	pointer-events: none;
}

.about-offer-item--soon .about-offer-item__icon::before {
	background-image: none;
	background-color: #7998BA;
}

.about-offer-item--soon .about-offer-item__title {
	color: #7998BA;
}

.section-about-community {
	padding-top: 24px;
}

.about-community-block {}

.about-community {
	background: #14202d;
	border-radius: 8px;
	padding: 72px;
	display: flex;

}

.about-community-left {
	width: calc(100% - 416px);
	padding-right: 100px;
}

.about-community-right {
	width: 416px;
}

.about-community-content {}

.about-community-title {
	color: #ebf4ff;
}

.about-community-descr {
	margin-top: 24px;
	font-size: 20px;
	line-height: 1.45;
	color: #7998ba;
}

.about-community-blockqoute {
	margin-top: 24px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
	background: #172a3e;
	border-left: 2px solid #047bdf;
	border-radius: 0 8px 8px 0;
	padding: 8px 16px;
	padding-left: 14px;

}

.about-community-count {
	margin-top: 24px;
	font-size: 12px;
	line-height: 1.45;
	color: #047bdf;
}

.about-community-count-current {
	font-size: 20px;
}

.section-about-choose {
	padding-top: 48px;
	padding-bottom: 24px;
}

.section-about-choose .h1 {
	text-align: center;
	color: #ebf4ff;
}

.section-about-choose .section-descr {
	text-align: center;
	color: #7998ba;
	max-width: 854px;
	margin-right: auto;
	margin-left: auto;
}

.about-choose-items-block {
	margin-top: 24px;
}

.about-choose-items {
	display: flex;
	flex-wrap: wrap;
	margin-left: -12px;
	margin-right: -12px;
	margin-top: -24px;
}

.about-choose-item-wrapper {
	margin-top: 24px;
	padding-left: 12px;
	padding-right: 12px;
	width: 33.33%;
}

.about-choose-item {
	background: #172a3e;
	border-radius: 8px;
	padding: 32px;
}

.about-choose-item__icon {
	display: flex;
	width: 48px;
	height: 48px;
	margin-right: auto;
	margin-left: auto;
}

.about-choose-item__title {
	margin-top: 8px;
	font-weight: 500;
	font-size: 22px;
	line-height: 1.45;
	color: #ebf4ff;
	text-align: center;
}

.about-choose-item__descr {
	margin-top: 8px;
	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #7998ba;
}



.section-staking {
	padding-top: 72px;
}

.staking-top-block {}

.staking-top-block h1 {
	font-weight: 500;
	color: #fff;
}

.staking-top-descr {
	margin-top: 16px;
	font-size: 20px;
	line-height: 1.45;
	color: #7998ba;
	max-width: 680px;
}



.staking-benefits-block {
	margin-top: 16px;
}

.staking-benefits-title {
	font-weight: 600;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.staking-benefit-items-block {
	margin-top: 16px;
}

.staking-benefit-items {
	display: flex;
	flex-wrap: wrap;
	margin-top: -12px;
	margin-left: -6px;
	margin-right: -6px;
}

.staking-benefit-item-wrapper {
	margin-top: 12px;
	padding-left: 6px;
	padding-right: 6px;
}

.staking-benefit-item {
	display: flex;
}

.staking-benefit-item__icon-block {
	padding: 8px;
	width: 40px;
	height: 40px;
	border-radius: 40px;
	background: #172a3e;
	position: relative;
}

.staking-benefit-item__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.staking-benefit-item__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.staking-benefit-item__text {
	width: calc(100% - 40px);
	max-width: 240px;
	padding-left: 16px;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
	padding-right: 24px;
}

.staking-benefit-item__text b {
	font-weight: 600;
	color: #0170ef;
}



.staking-benefit-item--percent {}

.staking-benefit-item--percent .staking-benefit-item__icon::before {
	mask-image: url('../images/svg/percent.svg');
	-webkit-mask-image: url('../images/svg/percent.svg');
}

.staking-benefit-item--deposit {}

.staking-benefit-item--deposit .staking-benefit-item__icon::before {
	mask-image: url('../images/svg/tip-jar-fill.svg');
	-webkit-mask-image: url('../images/svg/tip-jar-fill.svg');
}

.staking-benefit-item--deposit {}

.staking-benefit-item--deposit .staking-benefit-item__icon::before {
	mask-image: url('../images/svg/tip-jar-fill.svg');
	-webkit-mask-image: url('../images/svg/tip-jar-fill.svg');
}

.staking-benefit-item--calendar {}

.staking-benefit-item--calendar .staking-benefit-item__icon::before {
	mask-image: url('../images/svg/calendar-check-fill.svg');
	-webkit-mask-image: url('../images/svg/calendar-check-fill.svg');
}



.staking-offer-block {
	margin-top: 24px;
}

.staking-offer {
	border-radius: 8px;
	padding: 12px;
	background: #14202d;
}

.staking-offer-top-block {}

.staking-offer-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.staking-offer-top-left {
	padding-right: 8px;
}

.staking-offer-top-title {
	font-weight: 500;
	font-size: 22px;
	line-height: 1.45;
	color: #ebf4ff;
}

.staking-offer-top-right {}

.staking-offer-items-block {
	margin-top: 24px;
}

.staking-offer-items-tab {}

.staking-offer-items {
	display: flex;
	flex-wrap: wrap;
	margin-left: -6px;
	margin-right: -6px;
	margin-top: -12px;
}

.staking-offer-item-wrapper {
	padding-left: 6px;
	padding-right: 6px;
	margin-top: 12px;
	width: 33.33%;
}

.staking-offer-item {
	position: relative;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 24px;
	background: #172a3e;
	cursor: pointer;
	transition: 0.4s ease;
}



.staking-offer-item-title-block {}

.staking-offer-item-title {
	display: flex;
	align-items: center;
}

.staking-offer-item-title__icon-block {
	width: 22px;
	height: 24px;
	position: relative;
}

.staking-offer-item-title__icon {
	width: 22px;
	height: 24px;
	position: relative;
	transition: 0.4s ease;
}



.staking-offer-item-title__text {
	width: calc(100% - 22px);
	padding-left: 8px;
	font-weight: 500;
	font-size: 18px;
	line-height: 1.25;
	color: #ebf4ff;
}

.staking-offer-item-title__text-percent {
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.staking-offer-item-currency-panel-block {
	margin-top: 8px;
}

.staking-offer-item-currency-panel {
	padding-top: 4px;
	padding-bottom: 4px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.staking-offer-item-currency-panel-left {
	padding-right: 8px;
}

.staking-offer-item-currency-panel-title {
	font-weight: 500;
	font-size: 12px;
	line-height: 1.25;
	color: #406084;
}

.staking-offer-item-currency-panel-right {}

.staking-offer-item-currency {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.staking-offer-item-currency__title {
	padding-right: 4px;
	font-weight: 500;
	font-size: 12px;
	line-height: 1.25;
	text-align: right;
	color: #ebf4ff;
}

.staking-offer-item-currency__icon {
	width: 20px;
	height: 20px;
}

.staking-offer-item-currency__icon .image {
	width: 20px;
	height: 20px;
}

.staking-offer-item-params-block {
	margin-top: 8px;
}

.staking-offer-item-params {}

.staking-offer-item-param {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #1d3045;
	padding-top: 8px;
	padding-bottom: 8px;
}

.staking-offer-item-param:first-child {
	padding-top: 0;
}

.staking-offer-item-param:last-child {
	padding-bottom: 0;
	border-bottom: 0;
}

.staking-offer-item-param-left {
	padding-right: 8px;
}

.staking-offer-item-param-title {
	display: flex;
	align-items: center;
}

.staking-offer-item-param-title__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.staking-offer-item-param-title__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #406084;
}

.staking-offer-item-param-title__text {
	padding-left: 4px;
	font-weight: 500;
	font-size: 12px;
	line-height: 125%;
	color: #406084;
	width: calc(100% - 20px);
}

.staking-offer-item-param-right {}

.staking-offer-item-param-value {
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	text-align: right;
	color: #ebf4ff;
}


.staking-offer-item-param--min {}

.staking-offer-item-param--min .staking-offer-item-param-title__icon::before {
	mask-image: url('../images/svg/arrow-fat-line-down.svg');
	-webkit-mask-image: url('../images/svg/arrow-fat-line-down.svg');
	background-color: #BD3D44;
}

.staking-offer-item-param--max {}

.staking-offer-item-param--max .staking-offer-item-param-title__icon::before {
	mask-image: url('../images/svg/arrow-fat-line-up.svg');
	-webkit-mask-image: url('../images/svg/arrow-fat-line-up.svg');
	background-color: #17C964;
}

.staking-offer-item-param--duration {}

.staking-offer-item-param--duration .staking-offer-item-param-title__icon::before {
	mask-image: url('../images/svg/calendar-dots.svg');
	-webkit-mask-image: url('../images/svg/calendar-dots.svg');
}


/* 
.staking-offer-item:hover {
	box-shadow: 0 3px 12px 0 rgba(23, 201, 100, 0.12);
	border-color: #17c964;
} */

.section-calculator {
	padding-top: 24px;
}

.calculator-block {}

.calculator {
	position: relative;
	padding: 32px;
}

.calculator::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-image: url('../images/theme/about-offer-item-border.png');
	background-position: center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	pointer-events: none;
}

.calculator-title {
	color: #ebf4ff;
}

.calculator-form-block {
	margin-top: 24px;
}

.calculator-form {}

.calculator-form-row {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
	flex-wrap: wrap;
	align-items: flex-end;
}

.calculator-form-col {
	width: 25%;
	padding-left: 8px;
	padding-right: 8px;
}

.calculator-form-col--input {}

.calculator-form-col--button {}

.calculator-form-col--button .form-button-block {
	margin-top: 0;
}

.calculator-result-block {
	margin-top: 24px;
}

.calculator-result {}

.calculator-result-title-block {
	display: flex;
}

.calculator-result-title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.calculator-result-items-block {
	margin-top: 24px;
}

.calculator-result-items {
	display: flex;
	flex-wrap: wrap;
	margin-top: -16px;
	margin-right: -8px;
	margin-left: -8px;
}

.calculator-result-item-wrapper {
	width: 25%;
	padding-left: 8px;
	padding-right: 8px;
	margin-top: 16px;
}

.calculator-result-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	padding: 16px;
	background: #172a3e;
}

.calculator-result-item__icon {
	width: 48px;
	height: 48px;
}

.calculator-result-item__content {
	width: calc(100% - 48px);
	padding-left: 8px;

	font-weight: 600;
	font-size: 14px;
	line-height: 1.35;
	color: #ebf4ff;
}

.calculator-result-item__title {}

.calculator-result-item__value {}

.section-profit-generate {
	padding-top: 24px;
}

.profit-generate-block {}

.profit-generate {
	padding: 24px;
	background-color: #0170EF;
	border-radius: 8px;
}

.profit-generate-title {
	color: #fff;
}

.profit-generate-scheme-block {
	margin-top: 32px;
}

.profit-generate-scheme {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-left: -8px;
	margin-right: -8px;
}

.profit-generate-scheme-step {
	padding-left: 8px;
	padding-right: 8px;
	width: calc(33.33% - 58px);
}

.profit-generate-scheme-deposit {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.profit-generate-scheme-deposit__icon {}

.profit-generate-scheme-deposit__title {
	margin-top: 8px;
	font-weight: 600;
	font-size: 16px;
	line-height: 135%;
	text-align: center;
	color: #ebf4ff;
}

.profit-generate-scheme-deposit__image {
	margin-top: 8px;
}


.profit-generate-scheme-transition {
	width: 87px;
	padding-left: 8px;
	padding-right: 8px;
	display: flex;
	align-items: center;
}


.profit-generate-scheme-transition-arrow {
	position: relative;
	width: 32px;
	height: 8px;
}

.profit-generate-scheme-transition-arrow::before {
	content: '';
	position: absolute;
	width: 32px;
	height: 8px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 32px 8px;
	mask-image: url('../images/svg/arrow-long-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 32px 8px;
	-webkit-mask-image: url('../images/svg/arrow-long-right.svg');
	background-color: #fff;
	transition: 0.4s ease;
}

.profit-generate-scheme-transition-icon-block {
	width: 40px;
	height: 32px;
	padding-left: 8px;
}

.profit-generate-scheme-transition-icon {
	position: relative;
	width: 32px;
	height: 32px;
	border-radius: 32px;
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.profit-generate-scheme-transition-icon::before {
	content: '';
	position: absolute;
	width: 18px;
	height: 18px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 18px 18px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 18px 18px;
	background-color: #fff;
	transition: 0.4s ease;
}

.profit-generate-scheme-transition--wrench {}

.profit-generate-scheme-transition--wrench .profit-generate-scheme-transition-icon::before {
	mask-image: url('../images/svg/front/profit-generate-scheme-transition-icon--wrench.svg');
	-webkit-mask-image: url('../images/svg/front/profit-generate-scheme-transition-icon--wrench.svg');
}

.profit-generate-scheme-transition--check-circle {}

.profit-generate-scheme-transition--check-circle .profit-generate-scheme-transition-icon::before {
	mask-image: url('../images/svg/front/profit-generate-scheme-transition-icon--check-circle.svg');
	-webkit-mask-image: url('../images/svg/front/profit-generate-scheme-transition-icon--check-circle.svg');
}

.profit-generate-scheme-development {}

.profit-generate-scheme-development-image {
	width: 208px;
	height: 208px;
	margin-right: auto;
	margin-left: auto;
}

.profit-generate-scheme-development-title {
	margin-top: 16px;
	font-weight: 600;
	font-size: 16px;
	line-height: 1.35;
	text-align: center;
	color: #ebf4ff;
}

.profit-generate-scheme-development-descr {
	margin-top: 8px;
	font-weight: 400;
	font-size: 14px;
	line-height: 1.35;
	text-align: center;
	color: #ebf4ff;
}

.profit-generate-scheme-profit {}

.profit-generate-scheme-profit-title {
	font-weight: 600;
	font-size: 16px;
	line-height: 1.35;
	text-align: center;
	color: #ebf4ff;
}

.profit-generate-scheme-profit-items-block {
	margin-top: 8px;
}

.profit-generate-scheme-profit-items {
	display: flex;
	margin-left: -8px;
	margin-right: -8px;
}

.profit-generate-scheme-profit-item-wrapper {
	width: 50%;
	padding-left: 8px;
	padding-right: 8px;
}

.profit-generate-scheme-profit-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.profit-generate-scheme-profit-item-arrow {
	position: relative;
	width: 8px;
	height: 32px;
	margin-right: auto;
	margin-left: auto;
}

.profit-generate-scheme-profit-item-arrow::before {
	content: '';
	position: absolute;
	width: 8px;
	height: 32px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 8px 32px;
	mask-image: url('../images/svg/arrow-long-down.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 8px 32px;
	-webkit-mask-image: url('../images/svg/arrow-long-down.svg');
	background-color: #fff;
	transition: 0.4s ease;
}

.profit-generate-scheme-profit-item-icon-block {
	margin-top: 16px;
	height: 24px;
	position: relative;
	margin-right: auto;
	margin-left: auto;
}

.profit-generate-scheme-profit-item-icon {
	width: 24px;
	height: 24px;
}

.profit-generate-scheme-profit-item-dollar {
	position: absolute;
	right: 0;
	bottom: 0;
	z-index: 2;
}

.profit-generate-scheme-profit-item-title {
	margin-top: 8px;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	text-align: center;
	color: #ebf4ff;
}

.profit-generate-scheme-profit-item-image {
	max-width: 136px;
	margin-right: auto;
	margin-left: auto;
	margin-top: 8px;
}


.profit-generate-scheme-profit-item--platform {}

.profit-generate-scheme-profit-item--platform .profit-generate-scheme-profit-item-icon-block {
	padding-right: 15px;
}

.profit-generate-scheme-profit-item--investors {}

.profit-generate-scheme-profit-item--investors .profit-generate-scheme-profit-item-icon-block {
	padding-right: 10px;
}

.profit-generate-note {
	margin-top: 24px;
	font-size: 14px;
	line-height: 145%;
	color: #ebf4ff;
}

.section-front-features {
	padding-top: 24px;
}

.front-features-title {
	color: #ebf4ff;
}

.front-feature-items-block {
	margin-top: 32px;
}

.front-feature-items {
	display: flex;
	margin-left: -12px;
	margin-right: -12px;
	margin-top: -24px;
}

.front-feature-item-wrapper {
	width: 33.33%;
	padding-left: 12px;
	padding-right: 12px;
	margin-top: 24px;
}

.front-feature-item {
	background: #14202d;
	border-radius: 8px;
	padding: 32px;
	height: 100%;
}

.front-feature-item-title-block {
	display: flex;
	justify-content: center;
}

.front-feature-item-title {
	position: relative;
	padding-left: 24px;
	font-weight: 600;
	font-size: 20px;
	line-height: 1.35;
	color: #ebf4ff;
}

.front-feature-item-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	border-radius: 16px;
	width: 16px;
	height: 16px;
	box-shadow: 0 1px 4px 0 rgba(46, 229, 122, 0.25);
	background: #2ee57a;
}

.front-feature-item-descr {
	margin-top: 12px;
	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #7998ba;
}



.section-front-works {
	padding-top: 24px;
}

.section-front-works .section-descr {
	margin-top: 16px;
	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #7998ba;
}

.front-works-title {
	color: #ebf4ff;
	text-align: center;
}

.front-work-items-block {
	margin-top: 32px;
}

.front-work-items {
	display: flex;
	margin-left: -12px;
	margin-right: -12px;
	margin-top: -24px;
}

.front-work-item-wrapper {
	width: 33.33%;
	padding-left: 12px;
	padding-right: 12px;
	margin-top: 24px;
}

.front-work-item {
	background: #14202d;
	border-radius: 8px;
	padding: 32px;
	height: 100%;
}

.front-work-item-image {
	width: 48px;
	height: 48px;
	margin-right: auto;
	margin-left: auto;
}

.front-work-item-title-block {
	margin-top: 16px;
	display: flex;
	justify-content: center;
}

.front-work-item-title {
	position: relative;
	font-weight: 600;
	font-size: 20px;
	line-height: 1.35;
	color: #ebf4ff;
	text-align: center;
}


.front-work-item-descr {
	margin-top: 8px;
	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #7998ba;
}


.front-works-sign-up-btn-block {
	margin-top: 32px;
	text-align: center;
}

.front-works-sign-up-btn {
	padding-left: 42px;
	padding-right: 42px;
}



.section-affiliate {
	padding-top: 72px;
}

.affiliate-top-block {}

.affiliate-top-block h1 {
	font-weight: 500;
	color: #fff;
}

.affiliate-top-descr {
	margin-top: 16px;
	font-size: 20px;
	line-height: 1.45;
	color: #7998ba;
	max-width: 680px;
}

.affiliate-tabs-block-panel {
	margin-top: 24px;
	display: flex;
}

.affiliate-tabs-block {
	border-radius: 8px;
	background: #172a3e;
}

.affiliate-tabs {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
}

.affiliate-tab-wrapper {
	padding-left: 8px;
	padding-right: 8px;
}

.affiliate-tab {
	padding: 13px 15px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.affiliate-tab__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
	transition: 0.4s ease;
	position: relative;
}

.affiliate-tab__icon {
	width: 24px;
	height: 24px;
	position: relative;
	margin-top: -2px;
	margin-bottom: -2px;
}

.affiliate-tab__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	transition: 0.4s ease;
}


.affiliate-tab__icon+.affiliate-tab__title {
	padding-left: 8px;
}

.affiliate-tab__count-block {
	padding-left: 8px;
}

.affiliate-tab__count {
	border-radius: 16px;
	min-width: 16px;
	width: 16px;
	height: 16px;
	box-shadow: 0 1px 4px 0 rgba(46, 229, 122, 0.25);
	background: #17c964;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 600;
	font-size: 10px;
	line-height: 1.25;
	color: #fff;
}



.affiliate-tab:hover,
.affiliate-tab.active {
	border-color: #1a314b;
	background: #14202d;
}

.affiliate-tab.active {
	pointer-events: none;
}

.affiliate-tab:hover .affiliate-tab__title,
.affiliate-tab.active .affiliate-tab__title {
	color: #ebf4ff;
}

.affiliate-tab:hover .affiliate-tab__icon::before,
.affiliate-tab.active .affiliate-tab__icon::before {
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}



.affiliate-tab--casino-lottery {}

.affiliate-tab--casino-lottery .affiliate-tab__icon::before {
	mask-image: url('../images/svg/trophy.svg');
	-webkit-mask-image: url('../images/svg/trophy.svg');
}

.affiliate-tab--casino-lottery.active .affiliate-tab__icon::before {
	mask-image: url('../images/svg/trophy-fill.svg');
	-webkit-mask-image: url('../images/svg/trophy-fill.svg');
}

.affiliate-tab--staking {}

.affiliate-tab--staking .affiliate-tab__icon::before {
	mask-image: url('../images/svg/tip-jar.svg');
	-webkit-mask-image: url('../images/svg/tip-jar.svg');
}

.affiliate-tab--staking.active .affiliate-tab__icon::before {
	mask-image: url('../images/svg/tip-jar-fill.svg');
	-webkit-mask-image: url('../images/svg/tip-jar-fill.svg');
}


.affiliate-tab--products {}

.affiliate-tab--products .affiliate-tab__icon::before {
	mask-image: url('../images/svg/link.svg');
	-webkit-mask-image: url('../images/svg/link.svg');
}

.affiliate-tab--products.active .affiliate-tab__icon::before {
	mask-image: url('../images/svg/link.svg');
	-webkit-mask-image: url('../images/svg/link.svg');
}

.affiliate-tabs-content-block {
	padding-top: 12px;
}

.affiliate-tabs-content {}

.affiliate-tab-content {
	position: relative;
	display: none;
}

.affiliate-tab-content:first-child {
	display: block;
}


.affiliate-tab-content .db-affiliate-program-warning-block {
	margin-top: 0;
}

.affiliate-tab-content .db-affiliate-program-table-block {
	margin-top: 0;
}

.affiliate-tab-content .db-affiliate-program-block {
	margin-top: 12px;
}

.affiliate-program-tabs-pabel {
	position: absolute;
	right: 0;
	top: -60px;
	z-index: 2;
}



.section-news {
	padding-top: 72px;
}

.news-top-block {}

.news-top-block h1 {
	font-weight: 500;
	color: #fff;
	text-align: center;
}

.news-top-descr {
	margin-top: 16px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
	text-align: center;
	max-width: 640px;
	margin-right: auto;
	margin-left: auto;
}

.news-top-descr a {
	color: #0170ef;
}

.news-top-descr a:hover {
	color: #0170ef;
	text-decoration: none;
}

.news-items-block {
	margin-top: 72px;
}

.news-items {
	display: flex;
	flex-wrap: wrap;
	margin-left: -12px;
	margin-right: -12px;
	margin-top: -24px;
}

.news-item-wrapper {
	width: 33.33%;
	padding-left: 12px;
	padding-right: 12px;
	margin-top: 24px;
}

.news-items-block .pagination {
	margin-top: 36px;
	justify-content: center;
}


.section-contacts {
	padding-top: 72px;
}

.contacts-top-block {}

.contacts-top-block h1 {
	font-weight: 500;
	color: #fff;
	text-align: center;
}

.contacts-top-descr {
	margin-top: 16px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
	text-align: center;
	max-width: 640px;
	margin-right: auto;
	margin-left: auto;
}

.contacts-block {
	margin-top: 72px;
	max-width: 1076px;
	margin-right: auto;
	margin-left: auto;
}

.contacts {
	display: flex;
	border-radius: 24px;
}

.contacts-left {
	width: 440px;
}

.contacts-right {
	width: calc(100% - 440px);
}

.contacts-info-block {
	background-color: #14202d;
	border-bottom: 1px solid #1a314b;
	border-left: 1px solid #1a314b;
	border-top: 1px solid #1a314b;
	border-radius: 24px 0 0 24px;
	padding: 48px 72px;
	height: 100%;
}

.contacts-info {}

.contacts-info-title {
	color: #ebf4ff;
	font-size: 22px;
}



.contact-items-block {
	margin-top: 24px;
}

.contact-items {}

.contact-item {
	display: flex;
	margin-top: 12px;
}

.contact-item:first-child {
	margin-top: 0;
}

.contact-email-link {
	display: flex;
	align-items: center;
	text-decoration: none;
	background: #172a3e;
	border-radius: 8px;
	padding: 16px;
	width: 100%;
}

.contact-email-link__icon {
	width: 28px;
	height: 28px;
	position: relative;
}

.contact-email-link__icon::before {
	content: '';
	position: absolute;
	width: 28px;
	height: 28px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 28px 28px;
	mask-image: url('../images/svg/envelope.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 28px 28px;
	-webkit-mask-image: url('../images/svg/envelope.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.contact-email-link__text {
	width: calc(100% - 28px);
	padding-left: 8px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1.45;
	color: #ebf4ff;
}

.contact-phone-link {
	display: flex;
	align-items: center;
	text-decoration: none;
	background: #172a3e;
	border-radius: 8px;
	padding: 16px;
	width: 100%;
}

.contact-phone-link__icon {
	width: 28px;
	height: 28px;
	position: relative;
}

.contact-phone-link__icon::before {
	content: '';
	position: absolute;
	width: 28px;
	height: 28px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 28px 28px;
	mask-image: url('../images/svg/phone.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 28px 28px;
	-webkit-mask-image: url('../images/svg/phone.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.contact-phone-link__text {
	width: calc(100% - 28px);
	padding-left: 8px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1.45;
	color: #ebf4ff;
}

.contact-address {
	display: flex;
	align-items: flex-start;
	border-radius: 8px;
	padding: 16px;
	background: #172a3e;
	width: 100%;
}

.contact-address__icon {
	width: 28px;
	height: 28px;
	position: relative;
}

.contact-address__icon::before {
	content: '';
	position: absolute;
	width: 28px;
	height: 28px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 28px 28px;
	mask-image: url('../images/svg/house-simple.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 28px 28px;
	-webkit-mask-image: url('../images/svg/house-simple.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.contact-address__content {
	width: calc(100% - 28px);
	padding-left: 8px;
}

.contact-address__text {
	font-weight: 500;
	font-size: 16px;
	line-height: 145%;
	color: #ebf4ff;
}

.contact-address__copy {
	margin-top: 8px;
	font-weight: 400;
	font-size: 14px;
	line-height: 1.25;
	color: #406084;
}

.contacts-info .join-telegram-block {
	margin-top: 16px;
}

.contacts-info .join-telegram {
	padding: 24px;
}

.contacts-info .join-telegram-descr {
	font-size: 14px;
}

.contacts-form-block {
	border: 1px solid #1a314b;
	background-color: #14202d;
	border-radius: 0 24px 24px 0;
	padding: 48px 72px;
	height: 100%;
}

.contacts-form-title {
	font-weight: 500;
	font-size: 22px;
	line-height: 1.45;
	color: #ebf4ff;
}

.contacts-form {
	margin-top: 24px;
}

.contacts-form .field--username .field-icon::before {
	mask-image: url('../images/svg/user-circle.svg');
	-webkit-mask-image: url('../images/svg/user-circle.svg');
}

.contacts-form .field textarea {
	height: 144px;
}



.section-faq {
	padding-top: 72px;
}

.faq-top-block {}

.faq-top-uptitle-block {
	display: flex;
}

.faq-top-uptitle {
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.faq-top-block h1 {
	margin-top: 16px;
	font-weight: 500;
	color: #fff;
}

.faq-top-descr {
	margin-top: 16px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.faq-top-ask-block {
	margin-top: 16px;
}

.faq-top-ask {
	display: flex;
	border-left: 1px solid #047bdf;
	border-radius: 0 8px 8px 0;
	padding: 12px;
	background: #172a3e;
}

.faq-top-ask-left {
	width: calc(100% - 185px);
	padding-right: 16px;
	display: flex;
	align-items: flex-start;
}

.faq-top-ask__icon {
	width: 20px;
	height: 20px;
	position: relative;
}

.faq-top-ask__icon::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/chat-circle-text.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/chat-circle-text.svg');
	background-color: #047BDF;
	transition: 0.4s ease;
}

.faq-top-ask__content {
	width: calc(100% - 20px);
	padding-left: 12px;
	font-size: 16px;
	line-height: 1.45;
	color: #ebf4ff;
}

.faq-top-ask-right {
	width: 185px;
}

.faq-top-ask-btn-block {
	display: flex;
	width: 100%;
}

.faq-top-ask-btn {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.faq-top-ask-btn__title {
	padding-right: 12px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1.35;
	color: #ebf4ff;
}

.faq-top-ask-btn__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.faq-top-ask-btn__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/question.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/question.svg');
	background-color: #EBF4FF;
	transition: 0.4s ease;
}

.faq-category-items-block {
	margin-top: 24px;
}

.faq-category-items {
	display: flex;
	flex-wrap: wrap;
	margin-left: -6px;
	margin-right: -6px;
	margin-top: -12px;
}

.faq-category-item-wrapper {
	width: 50%;
	padding-left: 6px;
	padding-right: 6px;
	margin-top: 12px;
}

.faq-category-item {
	border-radius: 8px;
	background: #172a3e;
	padding: 24px;
	display: block;
	text-decoration: none;
	position: relative;
	height: 100%;
}

.faq-category-item::after {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-image: url('../images/theme/faq-category-item-border.png');
	background-position: center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	pointer-events: none;
	opacity: 0;
	visibility: hidden;
	transition: 0.4s ease;
}




.faq-category-item:hover::after {
	opacity: 1;
	visibility: visible;
}

.faq-category-item:hover .faq-category-item__arrow {
	opacity: 1;
	visibility: visible;
}


.faq-category-item__icon {
	width: 28px;
	height: 28px;
	position: relative;
}

.faq-category-item__icon::before {
	content: '';
	position: absolute;
	width: 28px;
	height: 28px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 28px 28px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 28px 28px;
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.faq-category-item__title {
	margin-top: 8px;
	font-weight: 500;
	font-size: 22px;
	line-height: 1.45;
	color: #ebf4ff;
}

.faq-category-item__descr {
	margin-top: 8px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.faq-category-item__arrow {
	width: 24px;
	height: 24px;
	position: absolute;
	bottom: 20px;
	right: 16px;
	opacity: 0;
	visibility: hidden;
	transition: 0.4s ease;
}

.faq-category-item__arrow::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/arrow-right.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/arrow-right.svg');
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.faq-category-item--all {}

.faq-category-item--all .faq-category-item__icon::before {
	mask-image: url('../images/svg/squares-four.svg');
	-webkit-mask-image: url('../images/svg/squares-four.svg');
}

.faq-category-item--games {}

.faq-category-item--games .faq-category-item__icon::before {
	mask-image: url('../images/svg/dice-five.svg');
	-webkit-mask-image: url('../images/svg/dice-five.svg');
}

.faq-category-item--lottery {}

.faq-category-item--lottery .faq-category-item__icon::before {
	mask-image: url('../images/svg/ticket.svg');
	-webkit-mask-image: url('../images/svg/ticket.svg');
}

.faq-category-item--staking {}

.faq-category-item--staking .faq-category-item__icon::before {
	mask-image: url('../images/svg/tip-jar.svg');
	-webkit-mask-image: url('../images/svg/tip-jar.svg');
}

.faq-category-item--affiliate {}

.faq-category-item--affiliate .faq-category-item__icon::before {
	mask-image: url('../images/svg/users-four.svg');
	-webkit-mask-image: url('../images/svg/users-four.svg');
}

.faq-category-item--dashboard {}

.faq-category-item--dashboard .faq-category-item__icon::before {
	mask-image: url('../images/svg/desktop.svg');
	-webkit-mask-image: url('../images/svg/desktop.svg');
}

.section-faq+.section-start-earning .start-earning-image-left {
	left: -89px;
}

.section-faq+.section-start-earning .start-earning-image-right {
	right: -270px;
}



.modal--contact {}

.m-contact-form-block {}

.m-contact-form {}



.faq-category-top-block {}

.faq-category-back-link-block {
	display: flex;
}

.faq-category-back-link {
	display: inline-flex;
	align-items: center;
	text-decoration: none;
}

.faq-category-back-link__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.faq-category-back-link__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/arrow-left.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/arrow-left.svg');
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.faq-category-back-link__text {
	padding-left: 8px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}


.faq-category-title-block {
	margin-top: 16px;
	display: flex;
}

.faq-category-title {
	font-weight: 500;
	color: #fff;
	display: flex;
	align-items: center;
}

.faq-category-title__text {
	padding-right: 16px;
}

.faq-category-title__icon {
	width: 28px;
	height: 28px;
	position: relative;
}

.faq-category-title__icon::before {
	content: '';
	position: absolute;
	width: 28px;
	height: 28px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 28px 28px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 28px 28px;
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}



.faq-category-title--all {}

.faq-category-title--all .faq-category-title__icon::before {
	mask-image: url('../images/svg/squares-four.svg');
	-webkit-mask-image: url('../images/svg/squares-four.svg');
}

.faq-category-title--games {}

.faq-category-title--games .faq-category-title__icon::before {
	mask-image: url('../images/svg/dice-five.svg');
	-webkit-mask-image: url('../images/svg/dice-five.svg');
}

.faq-category-title--lottery {}

.faq-category-title--lottery .faq-category-title__icon::before {
	mask-image: url('../images/svg/ticket.svg');
	-webkit-mask-image: url('../images/svg/ticket.svg');
}

.faq-category-title--staking {}

.faq-category-title--staking .faq-category-title__icon::before {
	mask-image: url('../images/svg/tip-jar.svg');
	-webkit-mask-image: url('../images/svg/tip-jar.svg');
}

.faq-category-title--affiliate {}

.faq-category-title--affiliate .faq-category-title__icon::before {
	mask-image: url('../images/svg/users-four.svg');
	-webkit-mask-image: url('../images/svg/users-four.svg');
}

.faq-category-title--dashboard {}

.faq-category-title--dashboard .faq-category-title__icon::before {
	mask-image: url('../images/svg/desktop.svg');
	-webkit-mask-image: url('../images/svg/desktop.svg');
}

.faq-category-top-descr {
	margin-top: 16px;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.faq-category-top-search-block {
	margin-top: 12px;
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.faq-category-top-search-form {}



.faq-items-block {
	margin-top: 24px;
}

.faq-items-block .pagination-block {
	margin-top: 24px;
}

.faq-items {}

.faq-item {
	background: #14202d;
	border-radius: 8px;
	margin-top: 12px;
}

.faq-item:first-child {
	margin-top: 0;
}

.faq-item.active .faq-item-heading-arrow:before {
	transform: translate(-50%, -50%) rotate(180deg);
}

.faq-item-heading {
	border-radius: 8px;
	padding: 10px 12px;
	display: flex;
	align-items: center;
	position: relative;
	cursor: pointer;
}


.faq-item-heading-title {
	padding-right: 16px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1.45;
	color: #ebf4ff;
	width: calc(100% - 44px);
}

.faq-item-heading-arrow {
	position: relative;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	background: #172a3e;
}

.faq-item-heading-arrow::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/caret-down.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px;
	-webkit-mask-image: url('../images/svg/caret-down.svg');
	background-color: #EBF4FF;
	z-index: 5;
	transition: 0.4s ease;
	pointer-events: none;
	display: block;
}

.faq-item-body {
	padding-top: 2px;
	padding-left: 12px;
	padding-right: 12px;
	padding-bottom: 10px;
	display: none;
}

.faq-item-body-content {
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.text--light {
	color: #ebf4ff;
}

.text--light {
	color: #ebf4ff;
}

.faq-item-body-content {}

.faq-item-body-content b {
	font-weight: 500;
	color: #0681d5;
}

.faq-item-body-content ol,
.faq-item-body-content ul {
	padding-left: 20px;
}

.faq-item-body-content ol li,
.faq-item-body-content ul li {
	margin-top: 16px;
}

.faq-item-body-content ol li:first-child,
.faq-item-body-content ul li:first-child {
	margin-top: 0;
}

.faq-item-body-content ol ul li {
	margin-top: 0;
}

.faq-item-body-content a {
	font-weight: 500;
	color: #0681d5;
	text-decoration: none;
}

.faq-item-body-content a:hover {
	font-weight: 500;
	color: #0681d5;
	text-decoration: underline;
}

.content-image-list-block {
	margin-top: 24px;
	margin-bottom: 16px;
}

.content-image-list {
	margin-left: -6px;
	margin-right: -6px;
	margin-top: -12px;
	display: flex;
	flex-wrap: wrap;
}

.content-image-list-item-wrapper {
	width: 50%;
	padding-left: 6px;
	padding-right: 6px;
	margin-top: 12px;
}

.content-image-list-item {
	position: relative;
	display: block;
	padding: 0;
	border: 0;
	background-color: transparent;
	width: 100%;
}

.content-image-list-item::before {
	content: '';
	position: absolute;
	width: 32px;
	height: 32px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 32px 32px;
	mask-image: url('../images/svg/magnifying-glass-plus.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 32px 32px;
	-webkit-mask-image: url('../images/svg/magnifying-glass-plus.svg');
	background-color: #E8E8E8;
	z-index: 5;
	transition: 0.4s ease;
	opacity: 0;
	visibility: hidden;
}

.content-image-list-item:hover::before {
	opacity: 1;
	visibility: visible;
}


.content-image-list-item-image {
	position: relative;
	display: block;
	background: #172a3e;
	border-radius: 8px;
	height: 200px;
}

.content-image-list-item-image::before {
	content: '';
	position: absolute;
	width: 64px;
	height: 64px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 64px 64px;
	mask-image: url('../images/svg/image.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 64px 64px;
	-webkit-mask-image: url('../images/svg/image.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.content-image-list-item-image .image {
	position: relative;
	z-index: 2;
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 8px;
}



.modal--image .modal-content {
	padding: 12px;
	padding-top: 24px;
}

.modal--image .modal-close {
	top: -16px;
}


.section-promotions {
	padding-top: 72px;
}



.promotions-top-block {}

.promotions-top {}

.promotions-top-title-block {}

.promotions-top-title {
	color: #fff;
}

.promotions-top-descr-block {
	margin-top: 16px;
}

.promotions-top-descr {
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.promotions-filter-block {
	margin-top: 20px;
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.promotions-filter {}

.promotions-filter-form {}

.promotions-filter-form-row {
	display: flex;
	justify-content: space-between;
	margin-left: -6px;
	margin-right: -6px;
}

.promotions-filter-form-col {
	padding-left: 6px;
	padding-right: 6px;
}

.promotions-filter-form-col--left {
	width: auto;
	max-width: calc(100% - 200px);
}


.promotions-filter-form-col--right {
	width: 200px;
}



.promotions-filter-form-type-items-block {
	background: #172a3e;
	border-radius: 8px;
}

.promotions-filter-form-type-items {
	margin-left: -8px;
	margin-right: -8px;
	display: flex;
}

.promotions-filter-form-type-item-wrapper {
	padding-left: 8px;
	padding-right: 8px;
}

.promotions-filter-form-type-item-label {
	display: block;
	margin: 0;
}

.promotions-filter-form-type-item {
	display: block;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	color: #406084;
	background-color: #172a3e;
	border: 1px solid #172a3e;
	padding: 15px 15px;
	text-decoration: none;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.promotions-filter-form-type-item:hover {
	color: #fff;
	background-color: #14202d;
	border: 1px solid #1a314b;
}

.radio:checked~.promotions-filter-form-type-item {
	color: #fff;
	background-color: #14202d;
	border: 1px solid #1a314b;
	pointer-events: none;
}

.hot-promo-block {
	margin-top: 24px;
}

.hot-promo-title-block {
	padding-top: 2px;
	padding-bottom: 2px;
}

.hot-promo-title {
	display: flex;
	align-items: center;
}

.hot-promo-title__text {
	font-size: 22px;
	color: #ebf4ff;
	padding-right: 4px;
}

.hot-promo-title__icon {
	width: 22px;
}



.hot-promo-slider-block {
	position: relative;
	margin-top: 12px;
}

.hot-promo-slider {
	position: relative;
	margin-left: -12px;
	margin-right: -12px;
}

.hot-promo-slider .swiper-button-next,
.hot-promo-slider .swiper-button-prev {
	position: absolute;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	top: -56px;
	margin-top: 0;
	bottom: auto;
	background: #172a3e;
}

.hot-promo-slider .swiper-button-next::after,
.hot-promo-slider .swiper-button-prev::after {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.hot-promo-slider .swiper-button-next {
	right: 16px;
}

.hot-promo-slider .swiper-button-prev {
	right: 68px;
}


.hot-promo-slider .swiper-button-next::after {
	mask-image: url('../images/svg/caret-right.svg');
	-webkit-mask-image: url('../images/svg/caret-right.svg');
}

.hot-promo-slider .swiper-button-prev::after {
	mask-image: url('../images/svg/caret-left.svg');
	-webkit-mask-image: url('../images/svg/caret-left.svg');
}

.hot-promo-slide {
	padding-left: 12px;
	padding-right: 12px;
	height: auto;
}

.promotions-block {
	margin-top: 24px;
}

.promotions-title-block {}

.promotions-title {
	font-weight: 500;
	font-size: 22px;
	line-height: 1.45;
	color: #ebf4ff;
}

.promotion-items-block {
	margin-top: 12px;
}

.promotion-items {
	display: flex;
	flex-wrap: wrap;
	margin-left: -12px;
	margin-right: -12px;
	margin-top: -24px;
}

.promotion-item-wrapper {
	width: 25%;
	padding-left: 12px;
	padding-right: 12px;
	margin-top: 24px;
}

.promotions-show-all-btn-block {
	margin-top: 24px;
	display: flex;
	justify-content: center;
}

.promotion-show-all-btn {
	width: 100%;
	justify-content: center;
	max-width: 300px;
	margin-right: auto;
	margin-left: auto;
}



.modal--promotion {}

.modal--promotion .modal-dialog {
	max-width: 615px;
}

.modal--promotion .modal-body-content {
	padding-top: 12px;
}

.m-promotion-top-descr {
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.m-promotion-image-block {
	margin-top: 12px;
}

.m-promotion-image {
	position: relative;
	display: block;
	background: #172a3e;
	border-radius: 8px;
	height: 200px;
}

.m-promotion-image::before {
	content: '';
	position: absolute;
	width: 64px;
	height: 64px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 64px 64px;
	mask-image: url('../images/svg/image.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 64px 64px;
	-webkit-mask-image: url('../images/svg/image.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.m-promotion-image .image {
	position: relative;
	z-index: 2;
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 8px;
}



.m-promotion-panel-block {
	margin-top: 12px;
}

.m-promotion-panel {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;
}

.m-promotion-panel-left {
	padding-right: 4px;
	padding-top: 4px;
	padding-bottom: 4px;
}

.promotion-item__tags-block {}

.m-promotion-tags {
	margin-top: -8px;
	margin-left: -4px;
	margin-right: -4px;
	display: flex;
	flex-wrap: wrap;
}

.m-promotion-tag-wrapper {
	margin-top: 8px;
	padding-left: 4px;
	padding-right: 4px;
}

.m-promotion-tag {
	border-radius: 4px;
	padding: 2px 4px 3px 4px;
	background: rgba(1, 112, 239, 0.2);
	font-weight: 500;
	font-size: 12px;
	line-height: 1;
	color: #0170ef;
}

.m-promotion-panel-right {
	padding-top: 4px;
	padding-bottom: 4px;
}

.m-promotion-like-block {}

.m-promotion-like {
	padding: 0;
	background-color: transparent;
	border: 0;
	display: flex;
	align-items: center;
}

.m-promotion-like.active .m-promotion-like__count {
	color: #ebf4ff;
}

.m-promotion-like__count {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
	padding-right: 4px;
}

.m-promotion-like__icon {
	position: relative;
	width: 16px;
	height: 16px;
}

.m-promotion-like__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/heart.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/heart.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}


.m-promotion-like.liked .m-promotion-like__icon::before {
	mask-image: url('../images/svg/heart-fill.svg');
	-webkit-mask-image: url('../images/svg/heart-fill.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}


.m-promotion-info-block {
	margin-top: 12px;
}

.m-promotion-info {
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.m-promotion-buttons-block {}

.m-promotion-buttons {}

.m-promotion-btn-wrapper {
	margin-top: 12px;
}

.m-promotion-btn {
	width: 100%;
}

.m-promotion-buttons .join-telegram-btn-block {
	margin-top: 12px;
}

.m-promotion-buttons .join-telegram-btn {
	width: 100%;
	justify-content: center;
}

.section-news-single {
	padding-top: 64px;
}

.section-news-single+.section-start-earning .start-earning-image-left {
	left: -89px;
}

.section-news-single+.section-start-earning .start-earning-image-right {
	right: -270px;
}


.page-back-link-block {
	display: flex;
}

.page-back-link {
	display: inline-flex;
	align-items: center;
	text-decoration: none;
}

.page-back-link__icon {
	width: 24px;
	height: 24px;
	position: relative;
}

.page-back-link__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	mask-image: url('../images/svg/arrow-left.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/arrow-left.svg');
	background-color: #17c964;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.page-back-link__text {
	padding-left: 8px;
	font-weight: 500;
	font-size: 16px;
	line-height: 1;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}



.news-single-title-block {
	margin-top: 16px;
	display: flex;
}

.news-single-title {
	font-weight: 500;
	color: #fff;
	display: flex;
	align-items: center;
}

.news-single-date-block {
	margin-top: 16px;
	display: flex;
}

.news-single-date {
	position: relative;
	padding-left: 12px;
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.news-single-date::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	border-radius: 8px;
	width: 8px;
	height: 8px;
	box-shadow: 0 1px 4px 0 rgba(46, 229, 122, 0.25);
	background: #2ee57a;
}

.news-single-image-block {
	margin-top: 16px;
}

.news-single-image {
	position: relative;
	display: block;
	border-radius: 8px;
	height: 300px;
	background: #172a3e;
}


.news-single-image::before {
	content: '';
	position: absolute;
	width: 64px;
	height: 64px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 64px 64px;
	mask-image: url('../images/svg/image.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 64px 64px;
	-webkit-mask-image: url('../images/svg/image.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.news-single-image .image {
	position: relative;
	z-index: 2;
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 8px;
}

.news-single-content {
	margin-top: 16px;
	font-size: 16px;
	line-height: 145%;
	color: #7998ba;
}

.news-single-content p {
	margin-bottom: 16px;
}

.news-single-content p:last-child {
	margin-bottom: 0;
}

.news-single-content h2 {
	color: #ebf4ff;
	font-size: 22px;
	margin-bottom: 16px;
}

.news-single-content b {
	font-weight: 600;
}

.news-single-content ul,
.news-single-content ol {
	margin-bottom: 16px;
}

.news-single-content ul:last-child,
.news-single-content ol:last-child {
	margin-bottom: 0;
}

.news-single-content blockquote {
	border-left: 2px solid #047bdf;
	padding: 8px 16px;
	background: #172a3e;
	font-style: italic;
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
	margin-bottom: 16px;
}

.news-single-content blockquote:last-child {
	margin-bottom: 0;
}

.news-single-content a {
	color: #0170ef;
	text-decoration: none;
}

.news-single-content a:hover {
	color: #0170ef;
	text-decoration: underline;
}

.news-single-community-blockquotes-block {
	margin-bottom: 16px;
}

.news-single-community-blockquotes {
	margin-top: -12px;
	margin-left: -6px;
	margin-right: -6px;
	display: flex;
	flex-wrap: wrap;
}

.news-single-community-blockquote-wrapper {
	margin-top: 12px;
	padding-left: 6px;
	padding-right: 6px;
	width: 50%;
}

.news-single-community-blockquote {
	padding: 16px;
	background: #172a3e;
	border-left: 2px solid #17C964;
	height: 100%;
	font-style: italic;
	color: #7998ba;
}

.news-single-btn-block {
	margin-top: 16px;
}

.news-single-btn {}

.news-single-likes-block {
	margin-top: 28px;
	padding-top: 10px;
	border-top: 1px solid #172a3e;
}

.news-single-like {
	padding: 0;
	background-color: transparent;
	border: 0;
	display: flex;
	align-items: center;
}

.news-single-like.active .news-single-like__count {
	color: #ebf4ff;
}

.news-single-like__count {
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
	padding-right: 4px;
}

.news-single-like__icon {
	position: relative;
	width: 16px;
	height: 16px;
}

.news-single-like__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/heart.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/heart.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}


.news-single-like.liked .news-single-like__icon::before {
	mask-image: url('../images/svg/heart-fill.svg');
	-webkit-mask-image: url('../images/svg/heart-fill.svg');
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.news-single-block .join-telegram-block {
	margin-top: 28px;
}

.news-single-block .join-telegram {
	padding-top: 24px;
	padding-bottom: 24px;
}

.news-single-recent-news-block {
	margin-top: 40px;
}


.ns-news-slider-block {
	position: relative;
	margin-top: 12px;
}

.ns-news-slider {
	position: relative;
	margin-left: -16px;
	margin-right: -16px;
}

.ns-news-slider .swiper-button-next,
.ns-news-slider .swiper-button-prev {
	position: absolute;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	top: -56px;
	margin-top: 0;
	bottom: auto;
	background: #172a3e;
}

.ns-news-slider .swiper-button-next::after,
.ns-news-slider .swiper-button-prev::after {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.ns-news-slider .swiper-button-next {
	right: 16px;
}

.ns-news-slider .swiper-button-prev {
	right: 68px;
}


.ns-news-slider .swiper-button-next::after {
	mask-image: url('../images/svg/caret-right.svg');
	-webkit-mask-image: url('../images/svg/caret-right.svg');
}

.ns-news-slider .swiper-button-prev::after {
	mask-image: url('../images/svg/caret-left.svg');
	-webkit-mask-image: url('../images/svg/caret-left.svg');
}

.ns-news-slide {
	padding-left: 16px;
	padding-right: 16px;
	height: auto;
}







.modal--news-preview {}

.modal--news-preview .modal-dialog {
	max-width: 615px;
}

.modal--news-preview .modal-body-content {
	padding-top: 12px;
}

.m-news-preview-top-descr {
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.m-news-preview-image-block {
	margin-top: 12px;
}

.m-news-preview-image {
	position: relative;
	display: block;
	background: #172a3e;
	border-radius: 8px;
	height: 200px;
}

.m-news-preview-image::before {
	content: '';
	position: absolute;
	width: 64px;
	height: 64px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 64px 64px;
	mask-image: url('../images/svg/image.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 64px 64px;
	-webkit-mask-image: url('../images/svg/image.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.m-news-preview-image .image {
	position: relative;
	z-index: 2;
	width: 100%;
	height: 100%;
	object-fit: cover;
}


.m-news-preview-info-block {
	margin-top: 12px;
}

.m-news-preview-info {
	font-size: 16px;
	line-height: 1.45;
	color: #7998ba;
}

.m-news-preview-buttons-block {}

.m-news-preview-buttons {}

.m-news-preview-btn-wrapper {
	margin-top: 12px;
}

.m-news-preview-btn {
	width: 100%;
}

.m-news-preview-buttons .join-telegram-btn-block {
	margin-top: 12px;
}

.m-news-preview-buttons .join-telegram-btn {
	width: 100%;
	justify-content: center;
}


.m-news-preview-date-block {
	display: flex;
}

.m-news-preview-date {
	position: relative;
	padding-left: 12px;
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.m-news-preview-date::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	border-radius: 8px;
	width: 8px;
	height: 8px;
	box-shadow: 0 1px 4px 0 rgba(46, 229, 122, 0.25);
	background: #2ee57a;
}




.db-affiliate-promo-block {
	background-color: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.db-affiliate-promo-top-block {}

.db-affiliate-promo-top {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
}

.db-affiliate-promo-top-left {
	padding-right: 8px;
}

.db-affiliate-promo-top-content {}

.db-affiliate-promo-top-title {
	color: #ebf4ff;
}

.db-affiliate-promo-top-descr {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.db-affiliate-promo-top-right {}



.db-affiliate-promo-tabs-block {
	border-radius: 8px;
	background: #172a3e;
	padding: 4px;
}

.db-affiliate-promo-tabs {
	margin-left: -6px;
	margin-right: -6px;
	display: flex;
}

.db-affiliate-promo-tab-wrapper {
	padding-left: 6px;
	padding-right: 6px;
}

.db-affiliate-promo-tab {
	padding: 9px 17px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid transparent;
	border-radius: 8px;
	cursor: pointer;
	transition: 0.4s ease;
}

.db-affiliate-promo-tab__title {
	font-weight: 500;
	font-size: 16px;
	line-height: 1.25;
	color: #406084;
	transition: 0.4s ease;
	position: relative;
}


.db-affiliate-promo-tab:hover,
.db-affiliate-promo-tab.active {
	border-color: #1a314b;
	background: #14202d;
}

.db-affiliate-promo-tab.active {
	pointer-events: none;
}

.db-affiliate-promo-tab:hover .db-affiliate-promo-tab__title,
.db-affiliate-promo-tab.active .db-affiliate-promo-tab__title {
	color: #ebf4ff;
}

.db-affiliate-promo-tabs-content {}

.db-affiliate-promo-tab-content {
	display: none;
}

.db-affiliate-promo-tab-content:first-child {
	display: block;
}

.db-affiliate-promo-tab-content:nth-child(2) {
	/* display: block; */
}




.db-affiliate-banners-table-block {
	margin-top: 12px;
}

.db-affiliate-banners-table {}

.db-affiliate-banners-t-heading {
	display: flex;
}

.db-affiliate-banners-t-h-col {
	width: 16.66%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-affiliate-banners-t-h-col--title {
	width: calc(55% - 165px);
}

.db-affiliate-banners-t-h-col--description {
	width: 15%;
}

.db-affiliate-banners-t-h-col--type {
	width: 15%;
}

.db-affiliate-banners-t-h-col--lang {
	width: 15%;
}

.db-affiliate-banners-t-h-col--size {
	width: 76px;
	text-align: right;
}

.db-affiliate-banners-t-h-col--actions {
	width: 89px;
	text-align: right;
}

.db-affiliate-banners-t-items {}

.db-affiliate-banners-t-item {
	position: relative;
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-affiliate-banners-t-item:nth-child(odd) {
	background: #0e1b28;
}


.db-affiliate-banners-t-i-col {
	position: relative;
	z-index: 2;
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 16.66%;
}


.db-affiliate-banners-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-affiliate-banners-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-affiliate-banners-t-i-col__value-block {
	width: 100%;
}


.db-affiliate-banners-t-i-col--title {
	width: calc(55% - 165px);
}

.db-affiliate-banners-t-i-col--description {
	width: 15%;
}

.db-affiliate-banners-t-i-col--type {
	width: 15%;
}

.db-affiliate-banners-t-i-col--lang {
	width: 15%;
}

.db-affiliate-banners-t-i-col--size {
	width: 76px;
}

.db-affiliate-banners-t-i-col--actions {
	width: 89px;
}


.db-affiliate-banners-t-i-title {
	display: flex;
	align-items: center;
}

.db-affiliate-banners-t-i-title__icon {
	width: 16px;
	height: 16px;
	position: relative;
}



.db-affiliate-banners-t-i-title__icon .image {
	width: 16px;
	height: 16px;
	position: relative;
	z-index: 2;
}

.db-affiliate-banners-t-i-title__title {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-affiliate-banners-t-i-description {}

.db-affiliate-banners-t-i-type {}

.db-affiliate-banners-t-i-lang {
	display: flex;
	align-items: center;
}

.db-affiliate-banners-t-i-lang__flag {
	width: 21px;
	height: 16px;
	border-radius: 5px;
}

.db-affiliate-banners-t-i-lang__flag .image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 5px;
}

.db-affiliate-banners-t-i-lang__title {
	width: calc(100% - 21px);
	padding-left: 8px;
	color: #7998ba;
}

.db-affiliate-banners-t-i-size {
	color: #ebf4ff;
	text-align: right;
}

.db-affiliate-banners-t-i-actions {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.db-affiliate-banners-t-i-download-btn-block {
	display: flex;
}

.db-affiliate-banners-t-i-download-btn {
	position: relative;
	width: 20px;
	height: 20px;
	display: block;
}

.db-affiliate-banners-t-i-download-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/cloud-arrow-down-line.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/cloud-arrow-down-line.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #7998BA;
	transition: 0.4s ease;
}


.db-affiliate-banners-t-i-download-btn:hover::before {
	background-color: #0170EF;
}

.db-affiliate-banners-t-i-details-btn-block {
	display: flex;
	justify-content: flex-end;
	padding-left: 8px;
}

.db-affiliate-banners-t-i-details-btn {
	padding: 0;
	width: 20px;
	height: 20px;
	position: relative;
	border: 0;
}

.db-affiliate-banners-t-i-details-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/eye.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-affiliate-banners-t-i-details-btn:hover::before {
	background-color: #0170EF;
}



.db-affiliate-promo-pdf-table-block {
	margin-top: 12px;
}

.db-affiliate-promo-pdf-table {}

.db-affiliate-promo-pdf-t-heading {
	display: flex;
}

.db-affiliate-promo-pdf-t-h-col {
	width: 16.66%;
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-affiliate-promo-pdf-t-h-col--title {
	width: calc(52% - 160px);
}

.db-affiliate-promo-pdf-t-h-col--description {
	width: 33%;
}


.db-affiliate-promo-pdf-t-h-col--lang {
	width: 15%;
}

.db-affiliate-promo-pdf-t-h-col--size {
	width: 71px;
	text-align: right;
}

.db-affiliate-promo-pdf-t-h-col--actions {
	width: 89px;
	text-align: right;
}

.db-affiliate-promo-pdf-t-items {}

.db-affiliate-promo-pdf-t-item {
	position: relative;
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-affiliate-promo-pdf-t-item:nth-child(odd) {
	background: #0e1b28;
}


.db-affiliate-promo-pdf-t-i-col {
	position: relative;
	z-index: 2;
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: 20%;
}


.db-affiliate-promo-pdf-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-affiliate-promo-pdf-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-affiliate-promo-pdf-t-i-col__value-block {
	width: 100%;
}


.db-affiliate-promo-pdf-t-i-col--title {
	width: calc(52% - 160px);
}

.db-affiliate-promo-pdf-t-i-col--description {
	width: 33%;
}


.db-affiliate-promo-pdf-t-i-col--lang {
	width: 15%;
}

.db-affiliate-promo-pdf-t-i-col--size {
	width: 71px;
}

.db-affiliate-promo-pdf-t-i-col--actions {
	width: 89px;
}


.db-affiliate-promo-pdf-t-i-title {
	display: flex;
	align-items: center;
}

.db-affiliate-promo-pdf-t-i-title__icon {
	width: 16px;
	height: 16px;
	position: relative;
}



.db-affiliate-promo-pdf-t-i-title__icon .image {
	width: 16px;
	height: 16px;
	position: relative;
	z-index: 2;
}

.db-affiliate-promo-pdf-t-i-title__title {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-affiliate-promo-pdf-t-i-description {}

.db-affiliate-promo-pdf-t-i-type {}

.db-affiliate-promo-pdf-t-i-lang {
	display: flex;
	align-items: center;
}

.db-affiliate-promo-pdf-t-i-lang__flag {
	width: 21px;
	height: 16px;
	border-radius: 5px;
}

.db-affiliate-promo-pdf-t-i-lang__flag .image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 5px;
}

.db-affiliate-promo-pdf-t-i-lang__title {
	width: calc(100% - 21px);
	padding-left: 8px;
	color: #7998ba;
}

.db-affiliate-promo-pdf-t-i-size {
	color: #ebf4ff;
	text-align: right;
}

.db-affiliate-promo-pdf-t-i-actions {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.db-affiliate-promo-pdf-t-i-download-btn-block {
	display: flex;
}

.db-affiliate-promo-pdf-t-i-download-btn {
	position: relative;
	width: 20px;
	height: 20px;
	display: block;
}

.db-affiliate-promo-pdf-t-i-download-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/cloud-arrow-down-line.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/cloud-arrow-down-line.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	background-color: #7998BA;
	transition: 0.4s ease;
}


.db-affiliate-promo-pdf-t-i-download-btn:hover::before {
	background-color: #0170EF;
}

.db-affiliate-promo-pdf-t-i-details-btn-block {
	display: flex;
	justify-content: flex-end;
	padding-left: 8px;
}

.db-affiliate-promo-pdf-t-i-details-btn {
	padding: 0;
	width: 20px;
	height: 20px;
	position: relative;
	border: 0;
}

.db-affiliate-promo-pdf-t-i-details-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/eye.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-affiliate-promo-pdf-t-i-details-btn:hover::before {
	background-color: #0170EF;
}

.deposits-empty-block {
	padding-top: 40px;
	padding-left: 32px;
	padding-right: 32px;
	padding-bottom: 36px;
}

.deposits-content .deposits-empty-block {
	padding-top: 30px;
	padding-left: 20px;
	padding-right: 20px;
	padding-bottom: 24px;
}

.deposits-empty {
	max-width: 774px;
	margin-right: auto;
	margin-left: auto;
	text-align: center;
}

.deposits-empty-title {
	color: #ebf4ff;
}

.deposits-empty-items-block {
	margin-top: 32px;
}

.deposits-empty-items {
	margin-top: -12px;
	display: flex;
	flex-wrap: wrap;
	margin-left: -6px;
	margin-right: -6px;
}

.deposits-empty-item-wrapper {
	margin-top: 12px;
	width: 33.33%;
	padding-left: 6px;
	padding-right: 6px;
}

.deposits-empty-item {}

.deposits-empty-item__icon {
	border-radius: 100px;
	padding: 8px;
	width: 40px;
	height: 40px;
	background: #172a3e;
	margin-right: auto;
	margin-left: auto;
	position: relative;
}

.deposits-empty-item__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.deposits-empty-item__descr {
	padding-top: 16px;
	color: #406084;
	line-height: 1.25;
}

.deposits-empty-item__descr b {
	font-weight: 600;
	color: #0170ef;
}


.deposits-empty-item--percent {}

.deposits-empty-item--percent .deposits-empty-item__icon::before {
	mask-image: url('../images/svg/percent.svg');
	-webkit-mask-image: url('../images/svg/percent.svg');
}

.deposits-empty-item--deposit {}

.deposits-empty-item--deposit .deposits-empty-item__icon::before {
	mask-image: url('../images/svg/tip-jar-fill.svg');
	-webkit-mask-image: url('../images/svg/tip-jar-fill.svg');
}

.deposits-empty-item--time {}

.deposits-empty-item--time .deposits-empty-item__icon::before {
	mask-image: url('../images/svg/calendar-check-fill.svg');
	-webkit-mask-image: url('../images/svg/calendar-check-fill.svg');
}


.deposits-empty-btn-block {
	margin-top: 32px;
	text-align: center;
}

.deposits-empty-btn {
	min-width: 230px;
}

.db-empty-elements-block {
	min-height: 580px;
	padding: 40px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.db-empty-elements-title {
	font-size: 16px;
	line-height: 1.45;
	text-align: center;
	color: #7998ba;
}

.db-empty-elements-btn-block {
	margin-top: 16px;
	text-align: center;
}

.db-empty-elements-btn {
	min-width: 230px;
}




.custom-modal .refill-form-block {
	max-width: none;
}


.db-affiliate-partners-no-partners-block {
	padding-top: 30px;
	padding-bottom: 30px;
	max-width: 456px;
	margin-right: auto;
	margin-left: auto;
}

.db-affiliate-partners-no-partners-title {
	text-align: center;
	color: #ebf4ff;
}

.no-partners-fields-block {
	margin-top: 24px;
	max-width: 436px;
	margin-right: auto;
	margin-left: auto;
}

.no-partners-fields {}

.no-partners-fields .field--textarea textarea {
	height: 56px;
	padding-top: 12px;
	padding-bottom: 12px;
	padding-right: 54px;
	vertical-align: middle;
	line-height: 1.75;
}

.no-partners-fields .field--textarea .field-icon {
	top: 50%;
	transform: translateY(-50%);
}

.no-partners-fields .field input[type='text'],
.no-partners-fields .field input[type='email'],
.no-partners-fields .field input[type='password'] {
	height: 56px;
}

.no-partners-fields .field-right-panel-block {
	right: 12px;
}


.field--partner-link .field-icon::before {
	mask-image: url('../images/svg/share-network.svg');
	-webkit-mask-image: url('../images/svg/share-network.svg');
	background-color: #047BDF;
}

.field--referral-code .field-icon::before {
	mask-image: url('../images/svg/shield-check.svg');
	-webkit-mask-image: url('../images/svg/shield-check.svg');
	background-color: #047BDF;
}



.no-partners-benefit-items-block {
	margin-top: 24px;
}

.no-partners-benefit-items {
	padding-top: 1px;
	display: flex;
	flex-wrap: wrap;
	margin-left: -6px;
	margin-right: -6px;
}

.no-partners-benefit-item-wrapper {
	margin-top: 24px;
	width: 33.33%;
	padding-left: 6px;
	padding-right: 6px;
}

.no-partners-benefit-item {}

.no-partners-benefit-item__icon {
	border-radius: 100px;
	padding: 8px;
	width: 40px;
	height: 40px;
	background: #172a3e;
	margin-right: auto;
	margin-left: auto;
	position: relative;
}

.no-partners-benefit-item__icon::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #406084;
	background-image: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	transition: 0.4s ease;
}

.no-partners-benefit-item__descr {
	padding-top: 16px;
	color: #406084;
	line-height: 1.25;
	text-align: center;
}

.no-partners-benefit-item__descr b {
	font-weight: 600;
	color: #0170ef;
}


.no-partners-benefit-item--rocket-launch {}

.no-partners-benefit-item--rocket-launch .no-partners-benefit-item__icon::before {
	mask-image: url('../images/svg/rocket-launch.svg');
	-webkit-mask-image: url('../images/svg/rocket-launch.svg');
}

.no-partners-benefit-item--users-four {}

.no-partners-benefit-item--users-four .no-partners-benefit-item__icon::before {
	mask-image: url('../images/svg/users-four.svg');
	-webkit-mask-image: url('../images/svg/users-four.svg');
}

.no-partners-benefit-item--tip-jar {}

.no-partners-benefit-item--tip-jar .no-partners-benefit-item__icon::before {
	mask-image: url('../images/svg/tip-jar.svg');
	-webkit-mask-image: url('../images/svg/tip-jar.svg');
}

.cookie-block {
	position: fixed;
	z-index: 100;
	left: 50%;
	transform: translateX(-50%);
	bottom: 54px;
	width: 990px;
	display: none;
}

.cookie {
	background: #14202d;
	border-radius: 8px;
	padding: 16px;
	display: flex;
	justify-content: space-between;
}

.cookie__left {
	padding-right: 16px;
}

.cookie__info {
	padding: 5px;
	display: flex;
}

.cookie__info__icon {
	width: 20px;
	height: 20px;
}

.cookie__info__descr {
	width: calc(100% - 20px);
	padding-left: 12px;
	font-size: 14px;
	line-height: 1.6;
	color: #ebf4ff;
	letter-spacing: -0.4px;
}

.cookie__info__descr a {
	text-decoration: underline;
	text-decoration-skip-ink: none;
	color: #384bf2;
}


.cookie__right {}

.cookie__buttons-block {}

.cookie__buttons {
	display: flex;
	margin-left: -2px;
	margin-right: -2px;
}

.cookie__btn-wrapper {
	padding-left: 2px;
	padding-right: 2px;
}

.cookie__cancel-btn {
	font-size: 14px;
	padding: 10px 16px;
	white-space: nowrap;
}

.cookie__accept-btn {
	font-size: 14px;
	padding: 10px 16px;
	white-space: nowrap;
}

.page-progress-block {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	z-index: 100;
}

.page-progress {
	height: 4px;
	background-color: #172a3e;
	position: relative;
}

.page-progress__progress {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
	border-bottom-right-radius: 4px;
	border-top-right-radius: 4px;
}

.games-bet-table-block .db-empty-elements-block {
	min-height: 0;
	padding: 0;
	padding-top: 80px;
	padding-bottom: 80px;
}

.section-available-currency-list {
	padding-top: 40px;
}

.section-available-currency-list+footer {
	padding-top: 0;
}

.available-currency-list-block {
	max-width: 1076px;
	margin-right: auto;
	margin-left: auto;
}

.available-currency-list {
	margin-top: -12px;
	margin-left: -6px;
	margin-right: -6px;
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
}

.available-currency-list-item-wrapper {
	margin-top: 12px;
	padding-left: 6px;
	padding-right: 6px;
}

.available-currency-list-item {
	padding: 8px;
}

.available-currency-list-item-img {
	margin-right: auto;
	margin-left: auto;
	max-width: 120px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.game-dice-bet-overlay {
	position: absolute;
	left: 12px;
	top: 12px;
	right: 12px;
	bottom: 12px;
	z-index: 10;
}

.game-dice-bet {
	width: 60px;
	height: 66px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-position: center;
	background-repeat: no-repeat;
	background-size: 60px 66px;
	position: absolute;
	bottom: 4px;
	transform: translateX(-50%);
	left: 0;
}


.game-dice-bet__count {
	font-weight: 700;
	font-size: 14px;
	line-height: 1.25;
	text-align: center;
}

.game-dice-bet--green {
	background-image: url('../images/svg/games/dice/dive-slider-bet--green.svg');
}

.game-dice-bet--green .game-dice-bet__count {
	color: #2ee57a;
}

.game-dice-bet--red {
	background-image: url('../images/svg/games/dice/dive-slider-bet--red.svg');
}

.game-dice-bet--red .game-dice-bet__count {
	color: #df2727;
}



.game-bet-slider-block {
	position: relative;
	margin-top: 12px;
	width: 496px;
}

.game-bet-slider {
	position: relative;
	margin-left: -4px;
	margin-right: -4px;
	padding-left: 20px;
	padding-right: 20px;
}

.game-bet-slider .swiper-button-next,
.game-bet-slider .swiper-button-prev {
	position: absolute;
	width: 16px;
	height: 16px;
	top: 50%;
	transform: translateY(-50%);
	bottom: auto;
	background-color: transparent;
}

.game-bet-slider .swiper-button-next::after,
.game-bet-slider .swiper-button-prev::after {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #EBF4FF;
}

.game-bet-slider .swiper-button-next {
	right: 4px;
}

.game-bet-slider .swiper-button-prev {
	left: 4px;
}


.game-bet-slider .swiper-button-next::after {
	mask-image: url('../images/svg/caret-right.svg');
	-webkit-mask-image: url('../images/svg/caret-right.svg');
}

.game-bet-slider .swiper-button-prev::after {
	mask-image: url('../images/svg/caret-left.svg');
	-webkit-mask-image: url('../images/svg/caret-left.svg');
}

.game-bet-slide {
	padding-left: 4px;
	padding-right: 4px;
	height: auto;
	width: auto;
}

.game-bet-slide-item {
	border: 1px solid #1a314b;
	background: #172a3e;
	border-radius: 22px;
	padding: 8px 10px;
	width: 58px;
	height: 34px;
	font-weight: 500;
	font-size: 14px;
	line-height: 1.25;
	color: #ebf4ff;
	display: flex;
	align-items: center;
	justify-content: center;
}

.game-bet-slide-item--green {
	color: #14202d;
	background: #2ee57a;
	border: 1px solid #14c55e;
}



.available-currency-slider-block {
	position: relative;
	display: none;
}

.available-currency-slider {
	position: relative;
	margin-left: -2px;
	margin-right: -2px;
	padding-right: 102px;
}


.available-currency-slider .swiper-button-next,
.available-currency-slider .swiper-button-prev {
	position: absolute;
	border: 1px solid #1a314b;
	border-radius: 8px;
	padding: 10px;
	width: 44px;
	height: 44px;
	top: 50%;
	transform: translateY(-50%);
	margin-top: 0;
	bottom: auto;
	background: #172a3e;
}

.available-currency-slider .swiper-button-next::after,
.available-currency-slider .swiper-button-prev::after {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
}

.available-currency-slider .swiper-button-next {
	right: 2px;
}

.available-currency-slider .swiper-button-prev {
	right: 54px;
}


.available-currency-slider .swiper-button-next::after {
	mask-image: url('../images/svg/caret-right.svg');
	-webkit-mask-image: url('../images/svg/caret-right.svg');
}

.available-currency-slider .swiper-button-prev::after {
	mask-image: url('../images/svg/caret-left.svg');
	-webkit-mask-image: url('../images/svg/caret-left.svg');
}

.available-currency-slide {
	padding-left: 2px;
	padding-right: 2px;
	height: auto;
	width: auto;
}


.section-privacy {
	padding-top: 52px;
	padding-bottom: 40px;
}

.privacy-block {
	color: #ebf4ff;
}

.privacy-date-block {
	margin-top: 32px;
	display: flex;
}

.privacy-date {
	color: #ebf4ff;
	font-size: 14px;
	font-weight: 500;
	background: #172a3e;
	border: 1px solid #1a314b;
	border-radius: 22px;
	padding: 8px 10px;
}

.privacy-date b {
	color: #2ee57a;
	font-weight: 600;
}

.privacy-descr-block {
	margin-top: 16px;
}

.privacy-descr {
	color: #7998ba;
	line-height: 1.45;
}

.privacy-text-block {
	margin-top: 16px;
}

.privacy-text-title {
	font-size: 22px;
}

.privacy-text {
	margin-top: 16px;
	color: #7998ba;
}

.privacy-text a {
	color: #0681d5;
	text-decoration: none;
}

.privacy-text a:hover {
	color: #0681d5;
	text-decoration: underline;
}

.privacy-info {
	margin-top: 16px;
	background: #172a3e;
	color: #7998ba;
	border-left: 2px solid #047bdf;
	padding: 8px 16px;
	padding-left: 14px;
}



.game-seed-block {}

.game-seed {}

.game-seed-top-block {}

.game-seed-top {
	background: #14202d;
	border-radius: 8px;
	padding: 12px;
}

.game-seed-top h1 {
	color: #fff;
}

.game-seed-top-descr {
	margin-top: 16px;
	line-height: 1.45;
	color: #7998ba;
}



.game-seed-content-block {
	margin-top: 12px;
}

.game-seed-content {
	background: #14202d;
	border-radius: 8px;
	padding: 10px 12px;
}

.game-seed-content .pagination-block {
	margin-top: 24px;
}

.game-seed-content .pagination {
	justify-content: center;
}

.game-seed-items-block {}

.game-seed-items {}

.game-seed-item-wrapper {
	margin-top: 12px;
}

.game-seed-item-wrapper:first-child {
	margin-top: 0;
}

.game-seed-item {
	border-radius: 8px;
	padding: 12px;
	background: #0e1b28;
	display: flex;
	flex-wrap: wrap;
}

.game-seed-item__left {
	width: 620px;
}

.game-seed-item__left-top {
	display: flex;
	align-items: center;
}

.game-seed-item__right {
	width: calc(100% - 620px);
	padding-left: 26px;
}

.game-seed-item__title-block {
	padding-right: 8px;
}

.game-seed-item__title {
	display: flex;
	align-items: center;
	text-decoration: none;
	border: 0;
	padding: 0;
}

.game-seed-item__title__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.game-seed-item__title__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #406084;
	transition: 0.4s ease;
}

.game-seed-item--dice {}

.game-seed-item--dice .game-seed-item__title__icon::before {
	mask-image: url('../images/svg/games/dice-five.svg');
	-webkit-mask-image: url('../images/svg/games/dice-five.svg');
}

.game-seed-item--roulette {}

.game-seed-item--roulette .game-seed-item__title__icon::before {
	mask-image: url('../images/svg/games/checkerboard.svg');
	-webkit-mask-image: url('../images/svg/games/checkerboard.svg');
}

.game-seed-item--limbo {}

.game-seed-item--limbo .game-seed-item__title__icon::before {
	mask-image: url('../images/svg/games/boules.svg');
	-webkit-mask-image: url('../images/svg/games/boules.svg');
}




.game-seed-item__title__name {
	font-weight: 500;
	font-size: 14px;
	line-height: 1;
	color: #ebf4ff;
	max-width: calc(100% - 16px);
	padding-left: 8px;
	transition: 0.4s ease;
}

.game-seed-item__count-block {}

.game-seed-item__count {
	border-radius: 4px;
	padding: 2px;
	background: rgba(1, 112, 239, 0.2);
	font-weight: 500;
	font-size: 14px;
	line-height: 0.85;
	color: #0170ef;
	width: 30px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}


.custom-modal {
	/* display: block;
	position: relative;
	opacity: 1 !important; */
}


.field-block--game-seed {
	margin-top: 8px;
}

.field-block--game-seed .field-right-panel-block {
	right: 8px;
}

.field--game-seed .field-icon::before {
	mask-image: url('../images/svg/lock.svg');
	-webkit-mask-image: url('../images/svg/lock.svg');
}

.field--game-seed input[type='text'],
.field--game-seed input[type='email'],
.field--game-seed input[type='password'] {
	height: 56px;
	padding-right: 106px;
}

.field--game-seed textarea {
	height: 56px;
	padding-right: 106px;
	padding-top: 17px;
	padding-bottom: 17px;
}

.field--game-seed .field-icon {
	top: 50%;
	transform: translateY(-50%);
}

.field--game-seed .copy-field-btn::before {
	width: 24px;
	height: 24px;
	mask-size: 24px 24px;
	-webkit-mask-size: 24px 24px;
}


.download-field-btn-block {
	position: relative;
	padding-left: 8px;
}

.download-field-btn {
	position: relative;
	display: block;
	width: 40px;
	height: 40px;
}



.download-field-btn::before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/download.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 24px 24px;
	-webkit-mask-image: url('../images/svg/download.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 24px 24px;
	background-color: #EBF4FF;
	transition: 0.4s ease;
}

.game-seed-item__params {}

.game-seed-item__param {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 5px 4px;
	border-bottom: 1px solid #1a314b;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.game-seed-item__param:last-child {
	border-bottom: 0;
}

.game-seed-item__param-title {
	padding-right: 8px;
}

.game-seed-item__param-value {}



.db-address-whitelist-block {
	background-color: #14202d;
	border-radius: 8px;
}

.db-address-whitelist-top-block {}

.db-address-whitelist-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 12px;
}

.db-address-whitelist-top-left {
	/* padding-right: 8px; */
}

.db-address-whitelist-top-content {}

.db-address-whitelist-top-title {
	color: #ebf4ff;
}

.db-address-whitelist-top-descr {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.45;
	color: #406084;
}

.db-address-whitelist-top-right {}


.db-address-whitelist-table-block {
	padding-left: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
}

.db-address-whitelist-table {}

.db-address-whitelist-t-heading {
	display: flex;
}

.db-address-whitelist-t-h-col {
	width: calc(25% - 22px);
	font-weight: 600;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
	padding: 16px 12px;
}

.db-address-whitelist-t-h-col--payment {
	width: calc(15% - 22px);
}

.db-address-whitelist-t-h-col--type {
	width: calc(18% - 22px);
}

.db-address-whitelist-t-h-col--number {
	width: calc(45% - 22px);
}

.db-address-whitelist-t-h-col--destination-tag {
	width: calc(22% - 22px);
}


.db-address-whitelist-t-h-col--actions {
	text-align: right;
	width: 88px;
}

.db-address-whitelist-t-h-col-title-block {
	display: flex;
	align-items: center;
}

.db-address-whitelist-t-h-col-title {}


.db-address-whitelist-t-items {}

.db-address-whitelist-t-item {
	display: flex;
	align-items: center;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1;
	color: #7998ba;
}

.db-address-whitelist-t-item:nth-child(odd) {
	background: #0e1b28;
}

.db-address-whitelist-t-i-col {
	display: flex;
	align-items: center;
	padding: 15px 12px;
	width: calc(25% - 22px);
}


.db-address-whitelist-t-i-col__title-block {
	padding-right: 8px;
	display: none;
}

.db-address-whitelist-t-i-col__title {
	font-weight: 600;
	font-size: 14px;
	line-height: 1.25;
	color: #7998ba;
}

.db-address-whitelist-t-i-col__value-block {
	width: 100%;
}


.db-address-whitelist-t-i-col--payment {
	width: calc(15% - 22px);
}

.db-address-whitelist-t-i-col--type {
	width: calc(18% - 22px);
}

.db-address-whitelist-t-i-col--number {
	width: calc(45% - 22px);
}

.db-address-whitelist-t-i-col--destination-tag {
	width: calc(22% - 22px);
}


.db-address-whitelist-t-i-col--actions {
	text-align: right;
	width: 88px;
}


.db-address-whitelist-t-i-payment {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.db-address-whitelist-t-i-payment__title {

	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
}

.db-address-whitelist-t-i-payment__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-address-whitelist-t-i-currency {}

.db-address-whitelist-t-i-number {
	display: flex;
	align-items: center;
}

.db-address-whitelist-t-i-number__icon {
	width: 16px;
	height: 16px;
	position: relative;
}

.db-address-whitelist-t-i-number__icon::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/wallet.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/wallet.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-address-whitelist-t-i-number__balance {
	font-weight: 500;
	line-height: 1.25;
	color: #ebf4ff;
	padding-left: 8px;
	width: calc(100% - 16px);
	word-wrap: break-word;
}


.db-address-whitelist-t-i-actions-block {}

.db-address-whitelist-t-i-actions {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-left: -4px;
	margin-right: -4px;
}

.db-address-whitelist-t-i-action-wrapper {
	padding-left: 4px;
	padding-right: 4px;
}

.db-address-whitelist-t-i-action-edit-btn {
	width: 20px;
	height: 20px;
	position: relative;
	display: block;
	border: 0;
	padding: 0;
}

.db-address-whitelist-t-i-action-edit-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/pencil-simple.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/pencil-simple.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-address-whitelist-t-i-action-edit-btn:hover::before {
	background-color: #ebf4ff;
}

.db-address-whitelist-t-i-action-remove-btn {
	width: 20px;
	height: 20px;
	display: block;
	position: relative;
	border: 0;
	padding: 0;
}

.db-address-whitelist-t-i-action-remove-btn::before {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 20px 20px;
	mask-image: url('../images/svg/trash-simple.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 20px 20px;
	-webkit-mask-image: url('../images/svg/trash-simple.svg');
	background-color: #7998BA;
	transition: 0.4s ease;
}

.db-address-whitelist-t-i-action-remove-btn:hover::before {
	background-color: #ebf4ff;
}



.db-address-whitelist-toggle-col-visibility-block {
	padding-left: 8px;
}


.db-address-whitelist-toggle-number-visibility {
	display: block;
	width: 16px;
	height: 16px;
	position: relative;
	padding: 0;
	border: 0;
}


.db-address-whitelist-toggle-number-visibility::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.db-address-whitelist-toggle-number-visibility.active::before {
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-image: url('../images/svg/eye.svg');
}


.db-address-whitelist-toggle-number-visibility:hover::before {
	background-color: #17C964;
}


.db-address-whitelist-toggle-tag-visibility {
	display: block;
	width: 16px;
	height: 16px;
	position: relative;
	padding: 0;
	border: 0;
}


.db-address-whitelist-toggle-tag-visibility::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	mask-image: url('../images/svg/eye-closed.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/eye-closed.svg');
	background-color: #406084;
	transition: 0.4s ease;
}

.db-address-whitelist-toggle-tag-visibility.active::before {
	mask-image: url('../images/svg/eye.svg');
	-webkit-mask-image: url('../images/svg/eye.svg');
}


.db-address-whitelist-toggle-tag-visibility:hover::before {
	background-color: #17C964;
}


.toast-close {
	position: absolute;
	top: 20px;
	right: 20px;
	width: 16px;
	height: 16px;
	box-shadow: none;
	font-size: 0;
	text-shadow: none;
	opacity: 1;
	float: none;
}

.toast-close::before {
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	mask-image: url('../images/svg/close.svg');
	mask-position: center;
	mask-repeat: no-repeat;
	mask-size: 16px 16px;
	-webkit-mask-image: url('../images/svg/close.svg');
	-webkit-mask-position: center;
	-webkit-mask-repeat: no-repeat;
	-webkit-mask-size: 16px 16px;
	background-color: #7998BA;
}

#toast-container>.toast {
	box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
	background-color: #172a3e;
	padding: 20px 46px 20px 46px;
	border-radius: 8px;
	opacity: 1;
}

#toast-container>.toast:hover {
	box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}

#toast-container>.toast-success {
	background-image: url('../images/theme/toast--success-bg.png'), url('../images/svg/toast-icon--currency-circle-dollar.svg') !important;
	background-position: center, top 20px left 16px;
	background-repeat: no-repeat, no-repeat;
	background-size: 100% 100%, 20px 20px;
}

#toast-container>.toast-info {
	background-image: url('../images/theme/toast--info-bg.png'), url('../images/svg/toast-icon--info-fill.svg') !important;
	background-position: center, top 20px left 16px;
	background-repeat: no-repeat, no-repeat;
	background-size: 100% 100%, 20px 20px;
}

.toast-message {
	font-weight: 400;
	font-size: 12px;
	line-height: 125%;
	color: #7998ba;
}

.toast-message b {
	font-weight: 400;
	color: #fafcff;
}