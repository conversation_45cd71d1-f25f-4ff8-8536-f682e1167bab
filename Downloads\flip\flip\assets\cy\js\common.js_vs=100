// $('body').hide()


var topNav = priorityNav.init({
	mainNavWrapper: ".topmenu",
	navDropdownLabel: "",
	navDropdownBreakpointLabel: "",
	throttleDelay: 0,
});



$("body").on("click", ".change-pswd-type-link", function (e) {
	e.preventDefault();
	if ($(this).is(".active")) {
		$(this).removeClass("active")
		$(this).parents(".field").find('input').attr("type", "password")
	}
	else {
		$(this).addClass("active")
		$(this).parents(".field").find('input').attr("type", "text")
	}
});


$("body").on("click", ".mobile-panel-btn", function (e) {
	e.preventDefault();
});
$("body").on("click", function (e) {
	var mobilePanel = $(".mobile-panel-block");
	var mobilePanelLink = $(".mobile-panel-btn");
	if (mobilePanelLink.is(e.target)) {
		if ($(".mobile-panel-btn").is(".active")) {
			mobilePanelLink.removeClass("active")
			mobilePanel.removeClass("active")
		}
		else {
			mobilePanelLink.addClass("active")
			mobilePanel.addClass("active")
		}
	} else {
		if (!mobilePanel.is(e.target) && mobilePanel.has(e.target).length === 0) {
			if ($(".mobile-panel-btn").is(".active")) {
				$(".mobile-panel-btn").removeClass("active")
				mobilePanel.removeClass("active")
			} else {
			}
		}
	}
});
$("body").on("click", ".mobile-panel-close-btn", function (e) {
	e.preventDefault();
	$(".mobile-panel-btn").removeClass("active")
	$(".mobile-panel-block").removeClass("active")
});




$("body").on("click", ".topline-balance-toggle-btn", function (e) {
	e.preventDefault();
	if ($(this).is(".active")) {
		$(this).removeClass("active")
		$(this).parents(".topline-balance").find('.topline-balance-btn__amount').html($(this).data("amount"))
	}
	else {
		$(this).addClass("active")
		$(this).parents(".topline-balance").find('.topline-balance-btn__amount').html($(this).data("amount-hidden"))
	}
});


// $("body").on("click", ".lottery-list-filter-form-type-item", function (e) {
// 	e.preventDefault();
// 	if ($(this).is(".active")) {
// 	}
// 	else {
// 		$(".lottery-list-filter-form-type-item").removeClass('active')
// 		$(this).addClass("active")
// 	}
// });


$('.select-currency').selectpicker({
});


$("body").on("click", ".lottery-list-filter-panel-btn", function (e) {
	e.preventDefault();
	if ($(this).is(".active")) {
		$(this).removeClass("active")
		$(".lottery-list-filter-panel").slideUp()
	}
	else {
		$(this).addClass("active")
		$(".lottery-list-filter-panel").slideDown()
	}
});



document.querySelectorAll('.related-lottery-slider .swiper').forEach(function (elem) {
	relSwiper = new Swiper(elem, {
		// direction: 'horizontal',
		// loop: true,
		slidesPerView: 1,

		navigation: {
			nextEl: elem.nextElementSibling.nextElementSibling,
			prevEl: elem.nextElementSibling,
		},

		breakpoints: {
			320: {
				slidesPerView: 1,
			},
			576: {
				slidesPerView: 2,
			},
			768: {
				slidesPerView: 2,
			},
			992: {
				slidesPerView: 3,
			},
			1200: {
				slidesPerView: 3,
			},
			1400: {
				slidesPerView: 4,
			}
		}
	});
});




$("body").on("click", ".field-count-btn--minus", function () {
	var $input = $(this).parent().find('input');
	var count = parseInt($input.val()) - 1;

	count = count < 1 ? 1 : count;

	$input.val(count);
	$input.change();
	return false;
});


$("body").on("click", ".field-count-btn--plus", function () {
	var $input = $(this).parent().find('input');

	$input.val(parseInt($input.val()) + 1);

	$input.change();
	return false;
});


$("body").on("click", ".copy-link", function (e) {
	e.preventDefault();
});


var clipboardLink = new ClipboardJS('.copy-link', {
})
clipboardLink.on('success', function (e) {
	console.info('Action:', e.action);
	console.info('Text:', e.text);
	console.info('Trigger:', e.trigger);

	e.trigger.classList.add("active");

	setTimeout(function (e) {
		$('.copy-link.active').removeClass('active')
	}, 2000);
});
clipboardLink.on('error', function (e) {
	console.error('Action:', e.action);
	console.error('Trigger:', e.trigger);
});


document.querySelectorAll('.lottery-create-banner-info-slider .swiper').forEach(function (elem) {
	new Swiper(elem, {
		// direction: 'horizontal',
		// loop: true,
		slidesPerView: 1,

		pagination: {
			el: elem.nextElementSibling,
			clickable: true,
		},


		breakpoints: {
			320: {
				slidesPerView: 1,
			},
			768: {
				slidesPerView: 1,
			},
			992: {
				slidesPerView: 1,
			},
			1200: {
				slidesPerView: 1,
			}
		}
	});
});



// $("body").on("click", ".games-list-filter-form-type-item", function (e) {
// 	e.preventDefault();
// 	if ($(this).is(".active")) {
// 	}
// 	else {
// 		$(".games-list-filter-form-type-item").removeClass('active')
// 		$(this).addClass("active")
// 	}
// });

$('.select').selectpicker({
});



$(".games-bet-tab").on('click', function () {
	$(this).parents(".games-bet-tabs").find(".games-bet-tab").removeClass("active").eq($(this).parent().index()).addClass("active");
	$(".games-bet-tabs-content").find(".games-bet-tab-content").hide().eq($(this).parent().index()).fadeIn()
}).eq(0).addClass("active");


$(".game-manage-tab").on('click', function () {
	$(this).parents(".game-manage-tabs").find(".game-manage-tab").removeClass("active").eq($(this).parent().index()).addClass("active");
	$(".game-manage-tabs-content").find(".game-manage-tab-content").hide().eq($(this).parent().index()).fadeIn()
}).eq(0).addClass("active");



document.querySelectorAll('.chip-value-slider .swiper').forEach(function (elem) {
	new Swiper(elem, {
		// direction: 'horizontal',
		// loop: true,
		slidesPerView: 1,

		navigation: {
			nextEl: elem.nextElementSibling.nextElementSibling,
			prevEl: elem.nextElementSibling,
		},

		breakpoints: {
			320: {
				slidesPerView: 6,
			},
			768: {
				slidesPerView: 6,
			},
			992: {
				slidesPerView: 4,
			},
			1200: {
				slidesPerView: 6,
			}
		}
	});
});


$(".chip-value-item").on('click', function (e) {
	e.preventDefault()
	$(".chip-value-item").removeClass('active')
	$(this).addClass("active")
});


$(".game-descr-tab").on('click', function () {
	$(this).parents(".game-descr-tabs").find(".game-descr-tab").removeClass("active").eq($(this).parent().index()).addClass("active");
	$(".game-descr-tabs-content").find(".game-descr-tab-content").hide().eq($(this).parent().index()).fadeIn()
}).eq(0).addClass("active");



$(".m-fairness-tab").on('click', function () {
	$(this).parents(".m-fairness-tabs").find(".m-fairness-tab").removeClass("active").eq($(this).parent().index()).addClass("active");
	$(".m-fairness-tabs-content").find(".m-fairness-tab-content").hide().eq($(this).parent().index()).fadeIn()
}).eq(0).addClass("active");


$("body").on("click", ".copy-field-btn", function (e) {
	e.preventDefault();
});


var clipboardFieldBtn = new ClipboardJS('.copy-field-btn', {
})
clipboardFieldBtn.on('success', function (e) {
	console.info('Action:', e.action);
	console.info('Text:', e.text);
	console.info('Trigger:', e.trigger);

	e.trigger.classList.add("active");

	setTimeout(function (e) {
		$('.copy-field-btn.active').removeClass('active')
	}, 2000);
});
clipboardFieldBtn.on('error', function (e) {
	console.error('Action:', e.action);
	console.error('Trigger:', e.trigger);
});



$("body").on("click", ".field-right-count-btn--count-down", function () {
	var $input = $(this).parents(".field").find('input');
	var count = parseInt($input.val()) - 1;

	count = count < 1 ? 1 : count;

	$input.val(count);
	$input.change();
	return false;
});


$("body").on("click", ".field-right-count-btn--count-up", function () {
	var $input = $(this).parents(".field").find('input');

	$input.val(parseInt($input.val()) + 1);

	$input.change();
	return false;
});



$("body").on("click", ".game-description-dropdown-panel__heading", function (e) {
	e.preventDefault();
	if ($(this).parent().is(".active")) {
		$(this).parent().removeClass("active")
		$(this).next().slideUp()
	}
	else {
		$(this).parent().addClass("active")
		$(this).next().slideDown()
	}
});



$("body").on("click", ".copy-field-alt-btn", function (e) {
	e.preventDefault();
});


var clipboardFieldAltBtn = new ClipboardJS('.copy-field-alt-btn', {
})
clipboardFieldAltBtn.on('success', function (e) {
	console.info('Action:', e.action);
	console.info('Text:', e.text);
	console.info('Trigger:', e.trigger);

	e.trigger.classList.add("active");

	setTimeout(function (e) {
		$('.copy-field-alt-btn.active').removeClass('active')
	}, 2000);
});
clipboardFieldAltBtn.on('error', function (e) {
	console.error('Action:', e.action);
	console.error('Trigger:', e.trigger);
});



$("body").on("click", ".m-bet-honesty-panel-heading", function (e) {
	e.preventDefault();
	if ($(this).parent().is(".active")) {
		$(this).parent().removeClass("active")
		$(this).next().slideUp()
	}
	else {
		$(this).parent().addClass("active")
		$(this).next().slideDown()
	}
});

$gameDiceSlider = $(".game-dice-slider");
var min = 0;
var max = 100;
var marks = [32.1, 73.3];

$gameDiceSlider.ionRangeSlider({
	grid: true,
	min: min,
	max: max,
	from: 52,
	onStart: function (data) {
		addMarks(data.slider);
	}
});


function convertToPercent(num) {
	var percent = (num - min) / (max - min) * 100;

	return percent;
}


function addMarks($slider) {
	var html = '';
	var left = 0;
	var i;

	for (i = 0; i < marks.length; i++) {

		left = convertToPercent(marks[i]);

		if (left <= 50) {
			html += '<div class="game-dice-bet game-dice-bet--red" style="left: ' + left + '%"><div class="game-dice-bet__count">' + marks[i] + '</div></div>';
		} else {
			html += '<div class="game-dice-bet game-dice-bet--green" style="left: ' + left + '%"><div class="game-dice-bet__count">' + marks[i] + '</div></div>';
		}
	}

	$slider.append(html);
}


$("body").on("click", ".db-side-toggle-panel-btn", function (e) {
	e.preventDefault();
	if ($(this).is(".active")) {
		$(this).removeClass("active")
		$(".db-page").removeClass("db-page--wide-menu")
	}
	else {
		$(this).addClass("active")
		$(".db-page").addClass("db-page--wide-menu")
	}
});



$(".dashboard-tab").on('click', function () {
	$(this).parents(".dashboard-tabs").find(".dashboard-tab").removeClass("active").eq($(this).parent().index()).addClass("active");
	$(".dashboard-tabs-content").find(".dashboard-tab-content").hide().eq($(this).parent().index()).fadeIn()
}).eq(0).addClass("active");



$("body").on("click", ".copy-btn", function (e) {
	e.preventDefault();
});


var clipboardLink = new ClipboardJS('.copy-btn', {
})
clipboardLink.on('success', function (e) {
	console.info('Action:', e.action);
	console.info('Text:', e.text);
	console.info('Trigger:', e.trigger);

	e.trigger.classList.add("active");

	setTimeout(function (e) {
		$('.copy-btn.active').removeClass('active')
	}, 2000);
});
clipboardLink.on('error', function (e) {
	console.error('Action:', e.action);
	console.error('Trigger:', e.trigger);
});


autosize($('textarea'));


$("body").on("click", ".new-deposit-form-next-step-btn", function (e) {
	e.preventDefault();

	$(this).parents(".new-deposit-form-step").hide()
	$(this).parents(".new-deposit-form-step").next().fadeIn()

});



$("body").on("click", ".deposits-filter-panel-btn", function (e) {
	e.preventDefault();
	if ($(this).is(".active")) {
		$(this).removeClass("active")
		$(".deposits-filter-panel").slideUp()
	}
	else {
		$(this).addClass("active")
		$(".deposits-filter-panel").slideDown()
	}
});


$("body").on("click", ".transactions-filter-panel-btn", function (e) {
	e.preventDefault();
	if ($(this).is(".active")) {
		$(this).removeClass("active")
		$(".transactions-filter-panel").slideUp()
	}
	else {
		$(this).addClass("active")
		$(".transactions-filter-panel").slideDown()
	}
});



$("body").on("click", ".db-side-tablet-panel-btn", function (e) {
	e.preventDefault();
});
$("body").on("click", function (e) {
	var dbSideTablet = $(".db-side-tablet-block");
	var dbSideTabletLink = $(".db-side-tablet-panel-btn");
	if (dbSideTabletLink.is(e.target)) {
		if ($(".db-side-tablet-panel-btn").is(".active")) {
			dbSideTabletLink.removeClass("active")
			dbSideTablet.removeClass("active")
		}
		else {
			dbSideTabletLink.addClass("active")
			dbSideTablet.addClass("active")
		}
	} else {
		if (!dbSideTablet.is(e.target) && dbSideTablet.has(e.target).length === 0) {
			if ($(".db-side-tablet-panel-btn").is(".active")) {
				$(".db-side-tablet-panel-btn").removeClass("active")
				dbSideTablet.removeClass("active")
			} else {
			}
		}
	}
});
$("body").on("click", ".db-side-tablet-close-panel-btn", function (e) {
	e.preventDefault();
	$(".db-side-tablet-panel-btn").removeClass("active")
	$(".db-side-tablet-block").removeClass("active")
});



$(".db-affiliate-tab").on('click', function () {
	$(this).parents(".db-affiliate-tabs").find(".db-affiliate-tab").removeClass("active").eq($(this).parent().index()).addClass("active");
	$(".db-affiliate-tabs-content").find(".db-affiliate-tab-content").hide().eq($(this).parent().index()).fadeIn()
}).eq(0).addClass("active");



$(".db-affiliate-program-tab").on('click', function () {
	$(this).parents(".db-affiliate-program-tabs").find(".db-affiliate-program-tab").removeClass("active").eq($(this).parent().index()).addClass("active");
	$(".db-affiliate-program-tabs-content").find(".db-affiliate-program-tab-content").hide().eq($(this).parent().index()).fadeIn()
}).eq(0).addClass("active");



$(".setting-tab").on('click', function () {
	$(this).parents(".setting-tabs").find(".setting-tab").removeClass("active").eq($(this).parent().index()).addClass("active");
	$(".setting-tabs-content").find(".setting-tab-content").hide().eq($(this).parent().index()).fadeIn()
	$(".setting-side-tabs-content").find(".setting-side-tab-content").hide().eq($(this).parent().index()).fadeIn()
}).eq(0).addClass("active");



$(".input-file input").on("change", function (e) {
	readURL(this);
});


function readURL(input) {
	if (input.files && input.files[0]) {
		var reader = new FileReader();
		reader.onload = function (e) {
			$('.input-file-field__text').text(input.files[0].name);

			document.getElementById('loaded-image').setAttribute('src', e.target.result);

		}
		reader.readAsDataURL(input.files[0]);

	}
}




$("body").on("click", ".db-wallets-toggle-number-visibility", function (e) {
	e.preventDefault();
	if ($(this).is(".active")) {
		$(this).removeClass("active")
		$(".db-wallets-t-i-number__balance").each(function () {
			$(this).html($(this).data("hidden-value"))
		})
	}
	else {
		$(this).addClass("active")
		$(".db-wallets-t-i-number__balance").each(function () {
			$(this).html($(this).data("value"))
		})
	}
});

$("body").on("click", ".db-wallets-toggle-tag-visibility", function (e) {
	e.preventDefault();
	if ($(this).is(".active")) {
		$(this).removeClass("active")
		$(".db-wallets-t-i-destination-tag").each(function () {
			$(this).html($(this).data("hidden-value"))
		})
	}
	else {
		$(this).addClass("active")
		$(".db-wallets-t-i-destination-tag").each(function () {
			$(this).html($(this).data("value"))
		})
	}
});



document.querySelectorAll('.front-top-slider .swiper').forEach(function (elem) {
	new Swiper(elem, {
		// direction: 'horizontal',
		// loop: true,
		slidesPerView: 1,

		navigation: {
			nextEl: elem.nextElementSibling.nextElementSibling,
			prevEl: elem.nextElementSibling,
		},

		breakpoints: {
			320: {
				slidesPerView: 1,
			},
			576: {
				slidesPerView: 1,
			},
			768: {
				slidesPerView: 1,
			},
			992: {
				slidesPerView: 1,
			},
			1200: {
				slidesPerView: 1,
			},
			1400: {
				slidesPerView: 1,
			}
		}
	});
});


document.querySelectorAll('.promotion-slider .swiper').forEach(function (elem) {
	new Swiper(elem, {
		// direction: 'horizontal',
		// loop: true,
		slidesPerView: 1,

		navigation: {
			nextEl: elem.nextElementSibling.nextElementSibling,
			prevEl: elem.nextElementSibling,
		},

		breakpoints: {
			320: {
				slidesPerView: 1,
			},
			576: {
				slidesPerView: 2,
			},
			768: {
				slidesPerView: 2,
			},
			992: {
				slidesPerView: 3,
			},
			1200: {
				slidesPerView: 3,
			},
			1400: {
				slidesPerView: 4,
			}
		}
	});
});


document.querySelectorAll('.news-slider .swiper').forEach(function (elem) {
	new Swiper(elem, {
		// direction: 'horizontal',
		// loop: true,
		slidesPerView: 1,

		navigation: {
			nextEl: elem.nextElementSibling.nextElementSibling,
			prevEl: elem.nextElementSibling,
		},

		breakpoints: {
			320: {
				slidesPerView: 1,
			},
			576: {
				slidesPerView: 1,
			},
			768: {
				slidesPerView: 2,
			},
			992: {
				slidesPerView: 2,
			},
			1200: {
				slidesPerView: 3,
			},
			1400: {
				slidesPerView: 3,
			}
		}
	});
});



$(".affiliate-tab").on('click', function () {
	$(this).parents(".affiliate-tabs").find(".affiliate-tab").removeClass("active").eq($(this).parent().index()).addClass("active");
	$(".affiliate-tabs-content").find(".affiliate-tab-content").hide().eq($(this).parent().index()).fadeIn()
}).eq(0).addClass("active");



$("body").on("click", ".faq-item-heading", function (e) {
	e.preventDefault();

	if ($(this).parent().is(".active")) {
		$(this).parent().removeClass("active");
		$(this).next().slideUp();
	} else {
		$(this).parent().addClass("active");
		$(this).next().slideDown();
	}
});


var $imageSrc;
$('.image-modal-btn').click(function () {
	$imageSrc = $(this).data("src");
});


$('#modal-image').on('shown.bs.modal', function (e) {
	$("#image-modal").attr('src', $imageSrc);
})


$('#modal-image').on('hide.bs.modal', function (e) {
	$("#image-modal").attr('src', '');
})



document.querySelectorAll('.hot-promo-slider .swiper').forEach(function (elem) {
	new Swiper(elem, {
		// direction: 'horizontal',
		// loop: true,
		slidesPerView: 1,

		navigation: {
			nextEl: elem.nextElementSibling.nextElementSibling,
			prevEl: elem.nextElementSibling,
		},

		breakpoints: {
			320: {
				slidesPerView: 1,
			},
			576: {
				slidesPerView: 1,
			},
			768: {
				slidesPerView: 2,
			},
			992: {
				slidesPerView: 2,
			},
			1200: {
				slidesPerView: 2,
			},
			1400: {
				slidesPerView: 2,
			}
		}
	});
});



document.querySelectorAll('.ns-news-slider .swiper').forEach(function (elem) {
	new Swiper(elem, {
		// direction: 'horizontal',
		// loop: true,
		slidesPerView: 1,

		navigation: {
			nextEl: elem.nextElementSibling.nextElementSibling,
			prevEl: elem.nextElementSibling,
		},

		breakpoints: {
			320: {
				slidesPerView: 1,
			},
			576: {
				slidesPerView: 1,
			},
			768: {
				slidesPerView: 2,
			},
			992: {
				slidesPerView: 2,
			},
			1200: {
				slidesPerView: 2,
			},
			1400: {
				slidesPerView: 2,
			}
		}
	});
});


$(".db-affiliate-promo-tab").on('click', function () {
	$(this).parents(".db-affiliate-promo-tabs").find(".db-affiliate-promo-tab").removeClass("active").eq($(this).parent().index()).addClass("active");
	$(".db-affiliate-promo-tabs-content").find(".db-affiliate-promo-tab-content").hide().eq($(this).parent().index()).fadeIn()
}).eq(0).addClass("active");



$("body").on("click", ".cookie__cancel-btn, .cookie__accept-btn", function (e) {
	e.preventDefault();

	$(".cookie-block").remove()
});


document.querySelectorAll('.game-bet-slider .swiper').forEach(function (elem) {
	new Swiper(elem, {
		// direction: 'horizontal',
		// loop: true,
		slidesPerView: "auto",

		navigation: {
			nextEl: elem.nextElementSibling.nextElementSibling,
			prevEl: elem.nextElementSibling,
		},

		breakpoints: {
			320: {
				slidesPerView: "auto",
			},
			768: {
				slidesPerView: "auto",
			},
			992: {
				slidesPerView: "auto",
			},
			1200: {
				slidesPerView: "auto",
			}
		}
	});
});


document.querySelectorAll('.available-currency-slider .swiper').forEach(function (elem) {
	new Swiper(elem, {
		// direction: 'horizontal',
		// loop: true,
		slidesPerView: "auto",

		navigation: {
			nextEl: elem.nextElementSibling.nextElementSibling,
			prevEl: elem.nextElementSibling,
		},

		breakpoints: {
			320: {
				slidesPerView: 2,
			},
			576: {
				slidesPerView: "auto",
			},
			992: {
				slidesPerView: "auto",
			},
			1200: {
				slidesPerView: "auto",
			}
		}
	});
});




$("body").on("click", ".db-address-whitelist-toggle-number-visibility", function (e) {
	e.preventDefault();
	if ($(this).is(".active")) {
		$(this).removeClass("active")
		$(".db-address-whitelist-t-i-number__balance").each(function () {
			$(this).html($(this).data("hidden-value"))
		})
	}
	else {
		$(this).addClass("active")
		$(".db-address-whitelist-t-i-number__balance").each(function () {
			$(this).html($(this).data("value"))
		})
	}
});

$("body").on("click", ".db-address-whitelist-toggle-tag-visibility", function (e) {
	e.preventDefault();
	if ($(this).is(".active")) {
		$(this).removeClass("active")
		$(".db-address-whitelist-t-i-destination-tag").each(function () {
			$(this).html($(this).data("hidden-value"))
		})
	}
	else {
		$(this).addClass("active")
		$(".db-address-whitelist-t-i-destination-tag").each(function () {
			$(this).html($(this).data("value"))
		})
	}
});



toastr.options = {
	"closeButton": true,
	"debug": true,
	"newestOnTop": false,
	"progressBar": false,
	"positionClass": "toast-bottom-right",
	"preventDuplicates": false,
	"onclick": null,
	"showDuration": "300",
	"hideDuration": "1000",
	"timeOut": "5000",
	"extendedTimeOut": "1000",
	"showEasing": "swing",
	"hideEasing": "linear",
	"showMethod": "fadeIn",
	"hideMethod": "fadeOut",
	"closeHtml" : '<button class="toast-close"></button>'
}

// toastr["success"]("Your account has been successfully funded with <b>200 USDT</b>")
// toastr["info"]("We have received your payment, will be credited after <b>8 confirms</b> ")